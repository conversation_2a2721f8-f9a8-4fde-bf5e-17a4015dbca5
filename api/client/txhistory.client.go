// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package client

import (
	bytes "bytes"
	context "context"
	_go "github.com/json-iterator/go"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	api "gitlab.com/gx-regional/dbmy/transaction-history/api"
	http "net/http"
)

// TxHistoryClient makes calls to TxHistory service.
type TxHistoryClient struct {
	machinery klient.RoundTripper
}

// MakeTxHistoryClient instantiates a new TxHistoryClient.
// Deprecated: Use NewTxHistoryClient instead
func MakeTxHistoryClient(initializer klient.Initializer) (*TxHistoryClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &TxHistoryClient{
		machinery: roundTripper,
	}, nil
}

// NewTxHistoryClient instantiates a new TxHistoryClient.
func NewTxHistoryClient(baseURL string, options ...klient.Option) (*TxHistoryClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &TxHistoryClient{
		machinery: roundTripper,
	}, nil
}

// GetAccountTransactionsSearch: API to fetch all transactions for customer (currently CASA account only).
func (t *TxHistoryClient) GetAccountTransactionsSearch(ctx context.Context, req *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error) {
	reqShell := (*GetAccountTransactionsSearchRequestShell)(req)
	resShell := &GetAccountTransactionsSearchResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAccountTransactionsSearchDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTransactionsHistoryResponse)(resShell), err
}

// GetAccountCalendarActivity: API to fetch year and month for all accounts.
func (t *TxHistoryClient) GetAccountCalendarActivity(ctx context.Context, req *api.GetAccountCalendarActivityRequest) (*api.AccountCalendarActivityResponse, error) {
	reqShell := (*GetAccountCalendarActivityRequestShell)(req)
	resShell := &GetAccountCalendarActivityResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAccountCalendarActivityDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AccountCalendarActivityResponse)(resShell), err
}

// GetTransactionDetail: API to fetch transaction detail (currently only for CASA with pockets)
func (t *TxHistoryClient) GetTransactionDetail(ctx context.Context, req *api.GetTransactionDetailRequest) (*api.GetTransactionDetailResponse, error) {
	reqShell := (*GetTransactionDetailRequestShell)(req)
	resShell := &GetTransactionDetailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTransactionDetailDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTransactionDetailResponse)(resShell), err
}

// GetCASATransactionsSummary: API to share detail of monthly spent and interest earned for CASA account only.
func (t *TxHistoryClient) GetCASATransactionsSummary(ctx context.Context, req *api.GetCASATransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error) {
	reqShell := (*GetCASATransactionsSummaryRequestShell)(req)
	resShell := &GetCASATransactionsSummaryResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCASATransactionsSummaryDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCASATransactionsSummaryResponse)(resShell), err
}

// GetPocketInterestEarned: API to fetch total interest earned on the pocket
func (t *TxHistoryClient) GetPocketInterestEarned(ctx context.Context, req *api.GetPocketInterestEarnedRequest) (*api.GetPocketInterestEarnedResponse, error) {
	reqShell := (*GetPocketInterestEarnedRequestShell)(req)
	resShell := &GetPocketInterestEarnedResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPocketInterestEarnedDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPocketInterestEarnedResponse)(resShell), err
}

// GetPocketActivities: fetch all transactions related to pockets
func (t *TxHistoryClient) GetPocketActivities(ctx context.Context, req *api.GetPocketActivitiesRequest) (*api.GetPocketActivitiesResponse, error) {
	reqShell := (*GetPocketActivitiesRequestShell)(req)
	resShell := &GetPocketActivitiesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPocketActivitiesDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPocketActivitiesResponse)(resShell), err
}

// GetPocketActivityDetail: API to fetch transaction detail for pocket transactions
func (t *TxHistoryClient) GetPocketActivityDetail(ctx context.Context, req *api.GetPocketActivityDetailRequest) (*api.GetPocketActivityDetailResponse, error) {
	reqShell := (*GetPocketActivityDetailRequestShell)(req)
	resShell := &GetPocketActivityDetailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPocketActivityDetailDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPocketActivityDetailResponse)(resShell), err
}

// GetCASAInterestEarned: API to fetch total interest earned on the CASA account
func (t *TxHistoryClient) GetCASAInterestEarned(ctx context.Context, req *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error) {
	reqShell := (*GetCASAInterestEarnedRequestShell)(req)
	resShell := &GetCASAInterestEarnedResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCASAInterestEarnedDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCASAInterestEarnedResponse)(resShell), err
}

// GetSTM453TransactionInfo: API to fetch STM453 Transaction Info
func (t *TxHistoryClient) GetSTM453TransactionInfo(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error) {
	reqShell := (*GetSTM453TransactionInfoRequestShell)(req)
	resShell := &GetSTM453TransactionInfoResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getSTM453TransactionInfoDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetSTM453TransactionInfoResponse)(resShell), err
}

// ListAccountTransactionsSearch: API to fetch transactions for customer
func (t *TxHistoryClient) ListAccountTransactionsSearch(ctx context.Context, req *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error) {
	reqShell := (*ListAccountTransactionsSearchRequestShell)(req)
	resShell := &ListAccountTransactionsSearchResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listAccountTransactionsSearchDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTransactionsHistoryResponse)(resShell), err
}

// GetInternalTransactionDetail: API to fetch transaction detail for internal usage
func (t *TxHistoryClient) GetInternalTransactionDetail(ctx context.Context, req *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error) {
	reqShell := (*GetInternalTransactionDetailRequestShell)(req)
	resShell := &GetInternalTransactionDetailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getInternalTransactionDetailDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetInternalTransactionDetailResponse)(resShell), err
}

// GetOpsSearch: API to fetch transaction detail for customer portal usage
func (t *TxHistoryClient) GetOpsSearch(ctx context.Context, req *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error) {
	reqShell := (*GetOpsSearchRequestShell)(req)
	resShell := &GetOpsSearchResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getOpsSearchDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetOpsSearchResponse)(resShell), err
}

// GetAccountTransactionsSearchForCX: API to fetch all transactions for customer (currently CASA account only) with transaction domain filter for CX.
func (t *TxHistoryClient) GetAccountTransactionsSearchForCX(ctx context.Context, req *api.GetTransactionsHistoryCXRequest) (*api.GetTransactionsHistoryCXResponse, error) {
	reqShell := (*GetAccountTransactionsSearchForCXRequestShell)(req)
	resShell := &GetAccountTransactionsSearchForCXResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAccountTransactionsSearchForCXDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTransactionsHistoryCXResponse)(resShell), err
}

// GetLendingTransactionDetail: API to fetch information about a particular loan transaction.
func (t *TxHistoryClient) GetLendingTransactionDetail(ctx context.Context, req *api.GetLendingTransactionDetailRequest) (*api.GetLendingTransactionDetailResponse, error) {
	reqShell := (*GetLendingTransactionDetailRequestShell)(req)
	resShell := &GetLendingTransactionDetailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLendingTransactionDetailDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLendingTransactionDetailResponse)(resShell), err
}

// GetLendingTransactionSearch: API to fetch all transactions for customer (Flexi Loan Account only).
func (t *TxHistoryClient) GetLendingTransactionSearch(ctx context.Context, req *api.GetLendingTransactionSearchRequest) (*api.GetLendingTransactionSearchResponse, error) {
	reqShell := (*GetLendingTransactionSearchRequestShell)(req)
	resShell := &GetLendingTransactionSearchResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLendingTransactionSearchDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLendingTransactionSearchResponse)(resShell), err
}

// GetLendingTransactionSearchForCRM: API to fetch all transactions for customer from CRM (Flexi Loan Account only).
func (t *TxHistoryClient) GetLendingTransactionSearchForCRM(ctx context.Context, req *api.GetLendingTransactionSearchForCRMRequest) (*api.GetLendingTransactionSearchResponse, error) {
	reqShell := (*GetLendingTransactionSearchForCRMRequestShell)(req)
	resShell := &GetLendingTransactionSearchForCRMResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLendingTransactionSearchForCRMDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLendingTransactionSearchResponse)(resShell), err
}

// GetLendingTransactionDetailForCRM: API to fetch information about a particular loan transaction from CRM.
func (t *TxHistoryClient) GetLendingTransactionDetailForCRM(ctx context.Context, req *api.GetLendingTransactionDetailForCRMRequest) (*api.GetLendingTransactionDetailResponse, error) {
	reqShell := (*GetLendingTransactionDetailForCRMRequestShell)(req)
	resShell := &GetLendingTransactionDetailForCRMResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLendingTransactionDetailForCRMDescriptor)
	err := t.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLendingTransactionDetailResponse)(resShell), err
}

// GetAccountTransactionsSearchRequestShell is a wrapper to make the object a klient.Request
type GetAccountTransactionsSearchRequestShell api.GetTransactionsHistoryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAccountTransactionsSearchRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transactions-search"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetAccountTransactionsSearchResponseShell is a wrapper to make the object a klient.Request
type GetAccountTransactionsSearchResponseShell api.GetTransactionsHistoryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAccountTransactionsSearchResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetAccountCalendarActivityRequestShell is a wrapper to make the object a klient.Request
type GetAccountCalendarActivityRequestShell api.GetAccountCalendarActivityRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAccountCalendarActivityRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v2/account-calendar-activity"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetAccountCalendarActivityResponseShell is a wrapper to make the object a klient.Request
type GetAccountCalendarActivityResponseShell api.AccountCalendarActivityResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAccountCalendarActivityResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTransactionDetailRequestShell is a wrapper to make the object a klient.Request
type GetTransactionDetailRequestShell api.GetTransactionDetailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTransactionDetailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v2/transactions/get"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTransactionDetailResponseShell is a wrapper to make the object a klient.Request
type GetTransactionDetailResponseShell api.GetTransactionDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTransactionDetailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCASATransactionsSummaryRequestShell is a wrapper to make the object a klient.Request
type GetCASATransactionsSummaryRequestShell api.GetCASATransactionsSummaryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCASATransactionsSummaryRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/casa-transactions-summary"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCASATransactionsSummaryResponseShell is a wrapper to make the object a klient.Request
type GetCASATransactionsSummaryResponseShell api.GetCASATransactionsSummaryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCASATransactionsSummaryResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetPocketInterestEarnedRequestShell is a wrapper to make the object a klient.Request
type GetPocketInterestEarnedRequestShell api.GetPocketInterestEarnedRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPocketInterestEarnedRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-total-interest-earned"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetPocketInterestEarnedResponseShell is a wrapper to make the object a klient.Request
type GetPocketInterestEarnedResponseShell api.GetPocketInterestEarnedResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPocketInterestEarnedResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetPocketActivitiesRequestShell is a wrapper to make the object a klient.Request
type GetPocketActivitiesRequestShell api.GetPocketActivitiesRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPocketActivitiesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-activities"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetPocketActivitiesResponseShell is a wrapper to make the object a klient.Request
type GetPocketActivitiesResponseShell api.GetPocketActivitiesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPocketActivitiesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetPocketActivityDetailRequestShell is a wrapper to make the object a klient.Request
type GetPocketActivityDetailRequestShell api.GetPocketActivityDetailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPocketActivityDetailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-activity-detail"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetPocketActivityDetailResponseShell is a wrapper to make the object a klient.Request
type GetPocketActivityDetailResponseShell api.GetPocketActivityDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPocketActivityDetailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCASAInterestEarnedRequestShell is a wrapper to make the object a klient.Request
type GetCASAInterestEarnedRequestShell api.GetCASAInterestEarnedRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCASAInterestEarnedRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/casa-total-interest-earned"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCASAInterestEarnedResponseShell is a wrapper to make the object a klient.Request
type GetCASAInterestEarnedResponseShell api.GetCASAInterestEarnedResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCASAInterestEarnedResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetSTM453TransactionInfoRequestShell is a wrapper to make the object a klient.Request
type GetSTM453TransactionInfoRequestShell api.GetSTM453TransactionInfoRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetSTM453TransactionInfoRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/stm453-transaction-info"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetSTM453TransactionInfoResponseShell is a wrapper to make the object a klient.Request
type GetSTM453TransactionInfoResponseShell api.GetSTM453TransactionInfoResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetSTM453TransactionInfoResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// ListAccountTransactionsSearchRequestShell is a wrapper to make the object a klient.Request
type ListAccountTransactionsSearchRequestShell api.GetTransactionsHistoryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListAccountTransactionsSearchRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v2/transactions/list"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListAccountTransactionsSearchResponseShell is a wrapper to make the object a klient.Request
type ListAccountTransactionsSearchResponseShell api.GetTransactionsHistoryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListAccountTransactionsSearchResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// GetInternalTransactionDetailRequestShell is a wrapper to make the object a klient.Request
type GetInternalTransactionDetailRequestShell api.GetInternalTransactionDetailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetInternalTransactionDetailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/transactions/get"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetInternalTransactionDetailResponseShell is a wrapper to make the object a klient.Request
type GetInternalTransactionDetailResponseShell api.GetInternalTransactionDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetInternalTransactionDetailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetOpsSearchRequestShell is a wrapper to make the object a klient.Request
type GetOpsSearchRequestShell api.GetOpsSearchRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetOpsSearchRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/transactions/list"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetOpsSearchResponseShell is a wrapper to make the object a klient.Request
type GetOpsSearchResponseShell api.GetOpsSearchResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetOpsSearchResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetAccountTransactionsSearchForCXRequestShell is a wrapper to make the object a klient.Request
type GetAccountTransactionsSearchForCXRequestShell api.GetTransactionsHistoryCXRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAccountTransactionsSearchForCXRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transactions-search-cx"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetAccountTransactionsSearchForCXResponseShell is a wrapper to make the object a klient.Request
type GetAccountTransactionsSearchForCXResponseShell api.GetTransactionsHistoryCXResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAccountTransactionsSearchForCXResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLendingTransactionDetailRequestShell is a wrapper to make the object a klient.Request
type GetLendingTransactionDetailRequestShell api.GetLendingTransactionDetailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLendingTransactionDetailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/lending/transaction-detail"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLendingTransactionDetailResponseShell is a wrapper to make the object a klient.Request
type GetLendingTransactionDetailResponseShell api.GetLendingTransactionDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLendingTransactionDetailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLendingTransactionSearchRequestShell is a wrapper to make the object a klient.Request
type GetLendingTransactionSearchRequestShell api.GetLendingTransactionSearchRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLendingTransactionSearchRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/lending/transactions-search"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLendingTransactionSearchResponseShell is a wrapper to make the object a klient.Request
type GetLendingTransactionSearchResponseShell api.GetLendingTransactionSearchResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLendingTransactionSearchResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLendingTransactionSearchForCRMRequestShell is a wrapper to make the object a klient.Request
type GetLendingTransactionSearchForCRMRequestShell api.GetLendingTransactionSearchForCRMRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLendingTransactionSearchForCRMRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/lending/transactions-search-cx"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLendingTransactionSearchForCRMResponseShell is a wrapper to make the object a klient.Request
type GetLendingTransactionSearchForCRMResponseShell api.GetLendingTransactionSearchResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLendingTransactionSearchForCRMResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLendingTransactionDetailForCRMRequestShell is a wrapper to make the object a klient.Request
type GetLendingTransactionDetailForCRMRequestShell api.GetLendingTransactionDetailForCRMRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLendingTransactionDetailForCRMRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/lending/transaction-detail-cx"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLendingTransactionDetailForCRMResponseShell is a wrapper to make the object a klient.Request
type GetLendingTransactionDetailForCRMResponseShell api.GetLendingTransactionDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLendingTransactionDetailForCRMResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

var getAccountTransactionsSearchDescriptor = klient.EndpointDescriptor{
	Name:        "GetAccountTransactionsSearch",
	Description: "GetAccountTransactionsSearch: API to fetch all transactions for customer (currently CASA account only).",
	Method:      "POST",
	Path:        "/v1/transactions-search",
}

var getAccountCalendarActivityDescriptor = klient.EndpointDescriptor{
	Name:        "GetAccountCalendarActivity",
	Description: "GetAccountCalendarActivity: API to fetch year and month for all accounts.",
	Method:      "POST",
	Path:        "/v2/account-calendar-activity",
}

var getTransactionDetailDescriptor = klient.EndpointDescriptor{
	Name:        "GetTransactionDetail",
	Description: "GetTransactionDetail: API to fetch transaction detail (currently only for CASA with pockets)",
	Method:      "POST",
	Path:        "/v2/transactions/get",
}

var getCASATransactionsSummaryDescriptor = klient.EndpointDescriptor{
	Name:        "GetCASATransactionsSummary",
	Description: "GetCASATransactionsSummary: API to share detail of monthly spent and interest earned for CASA account only.",
	Method:      "POST",
	Path:        "/v1/casa-transactions-summary",
}

var getPocketInterestEarnedDescriptor = klient.EndpointDescriptor{
	Name:        "GetPocketInterestEarned",
	Description: "GetPocketInterestEarned: API to fetch total interest earned on the pocket",
	Method:      "POST",
	Path:        "/v1/pocket-total-interest-earned",
}

var getPocketActivitiesDescriptor = klient.EndpointDescriptor{
	Name:        "GetPocketActivities",
	Description: "GetPocketActivities: fetch all transactions related to pockets",
	Method:      "POST",
	Path:        "/v1/pocket-activities",
}

var getPocketActivityDetailDescriptor = klient.EndpointDescriptor{
	Name:        "GetPocketActivityDetail",
	Description: "GetPocketActivityDetail: API to fetch transaction detail for pocket transactions",
	Method:      "POST",
	Path:        "/v1/pocket-activity-detail",
}

var getCASAInterestEarnedDescriptor = klient.EndpointDescriptor{
	Name:        "GetCASAInterestEarned",
	Description: "GetCASAInterestEarned: API to fetch total interest earned on the CASA account",
	Method:      "POST",
	Path:        "/v1/casa-total-interest-earned",
}

var getSTM453TransactionInfoDescriptor = klient.EndpointDescriptor{
	Name:        "GetSTM453TransactionInfo",
	Description: "GetSTM453TransactionInfo: API to fetch STM453 Transaction Info",
	Method:      "POST",
	Path:        "/v1/stm453-transaction-info",
}

var listAccountTransactionsSearchDescriptor = klient.EndpointDescriptor{
	Name:        "ListAccountTransactionsSearch",
	Description: "ListAccountTransactionsSearch: API to fetch transactions for customer",
	Method:      "POST",
	Path:        "/v2/transactions/list",
}

var getInternalTransactionDetailDescriptor = klient.EndpointDescriptor{
	Name:        "GetInternalTransactionDetail",
	Description: "GetInternalTransactionDetail: API to fetch transaction detail for internal usage",
	Method:      "POST",
	Path:        "/v1/internal/transactions/get",
}

var getOpsSearchDescriptor = klient.EndpointDescriptor{
	Name:        "GetOpsSearch",
	Description: "GetOpsSearch: API to fetch transaction detail for customer portal usage",
	Method:      "POST",
	Path:        "/v1/internal/transactions/list",
}

var getAccountTransactionsSearchForCXDescriptor = klient.EndpointDescriptor{
	Name:        "GetAccountTransactionsSearchForCX",
	Description: "GetAccountTransactionsSearchForCX: API to fetch all transactions for customer (currently CASA account only) with transaction domain filter for CX.",
	Method:      "POST",
	Path:        "/v1/transactions-search-cx",
}

var getLendingTransactionDetailDescriptor = klient.EndpointDescriptor{
	Name:        "GetLendingTransactionDetail",
	Description: "GetLendingTransactionDetail: API to fetch information about a particular loan transaction.",
	Method:      "POST",
	Path:        "/v1/lending/transaction-detail",
}

var getLendingTransactionSearchDescriptor = klient.EndpointDescriptor{
	Name:        "GetLendingTransactionSearch",
	Description: "GetLendingTransactionSearch: API to fetch all transactions for customer (Flexi Loan Account only).",
	Method:      "POST",
	Path:        "/v1/lending/transactions-search",
}

var getLendingTransactionSearchForCRMDescriptor = klient.EndpointDescriptor{
	Name:        "GetLendingTransactionSearchForCRM",
	Description: "GetLendingTransactionSearchForCRM: API to fetch all transactions for customer from CRM (Flexi Loan Account only).",
	Method:      "POST",
	Path:        "/v1/lending/transactions-search-cx",
}

var getLendingTransactionDetailForCRMDescriptor = klient.EndpointDescriptor{
	Name:        "GetLendingTransactionDetailForCRM",
	Description: "GetLendingTransactionDetailForCRM: API to fetch information about a particular loan transaction from CRM.",
	Method:      "POST",
	Path:        "/v1/lending/transaction-detail-cx",
}
