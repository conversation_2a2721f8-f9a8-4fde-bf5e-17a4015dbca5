syntax = "proto3";

package transactionHistory;

option go_package = "gitlab.com/gx-regional/dbmy/transaction-history/api";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "gxs/api/annotations.proto";

enum TransactionType {
  QR_PAYMENT = 0;
}

enum TransactionTags {
  OVERDRAFT = 0;
}

message GetTransactionsHistoryRequest {
  string accountID = 1 [(gxs.api.validate) = "regex,pattern=^[0-9]*$"];
  string endDate = 2 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9:-]*$"];
  string startDate = 3 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9:-]*$"];
  int64 pageSize = 4;
  string endingAfter = 5;
  string startingBefore = 6;
}

message TransactionHistoryResponse {
  string transactionID = 1;
  string batchID = 2;
  string displayName = 3;
  string iconURL = 4;
  int64 amount = 5 [(gxs.api.noomit) = true];
  string currency = 6;
  string status = 7;
  google.protobuf.Timestamp creationTimestamp = 8;
  Category category = 9;
}

message GetTransactionsHistoryResponse{
  map<string, string> links = 1 [(gxs.api.noomit) = true];
  repeated TransactionHistoryResponse data = 2 [(gxs.api.noomit) = true];
}

message AccountCalendarActivityRequest {
}

message Date {
  string year = 1;
  repeated string months = 2;
}

message AccountCalendarActivityResponse {
  repeated Date dates = 1 [(gxs.api.noomit) = true];
}

message GetTransactionDetailRequest {
  string accountID = 1  [(gxs.api.validate) = "regex,pattern=^[0-9]*$"];
  string transactionID = 2 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
}

message CounterParty {
  string displayName = 1;
  string iconURL = 2;
  map<string, string> transactionDetails = 3;
  string swiftCode = 4;
  string accountNumber = 5;
}

message Account {
  string displayName = 1;
}

message Category {
  string id = 1;
  string name = 2;
}

message GetTransactionDetailResponse {
  int64 amount = 1 [(gxs.api.noomit) = true];
  string currency = 2;
  string transactionID = 3;
  string batchID = 4;
  string sourceOfFund = 5;
  string transactionDescription = 6;
  string transactionRemarks = 7;
  string status = 8;
  string statusDescription = 9;
  Category category = 10;
  CounterParty counterParty = 11;
  google.protobuf.Timestamp transactionTimestamp = 12;
  CardTransactionDetail cardTransactionDetail = 13;
  string formattedLocalAmount = 14;
  string formattedForeignAmount = 15;
  bool hasReceipt = 16 [(gxs.api.noomit) = true]; //This field should not be omitted when empty
}

message CardTransactionDetail {
  string cardID = 1;
  string tailCardNumber = 2;
  google.protobuf.Timestamp settlementDate = 3;
  string exchangeRate = 4;
  string bankFee = 5;
  string mcc  = 6;
  string maskedCardNumber  = 7;
  string transactionCountry  = 8;
  string transactionCategory  = 9;
  string transactionSubCategory  = 10;
  string networkID  = 11;
  string threeDsValidation  = 12;
  string pinValidation  = 13;
  string retrievalReferenceNumber  = 14;
  string cardProxyNumber = 15;
  string merchantName = 16;
  string authCode = 17;
  string acquirerReferenceData = 18;
}


message GetCASATransactionsSummaryRequest {
  string accountID = 1;
}

message GetCASATransactionsSummaryResponse {
  Money totalInterestEarned = 1 [(gxs.api.noomit) = true];
  CurrentMonthMoneySpent currentMonthMoneySpent = 2;
  CurrentMonthMoneyReceived currentMonthMoneyReceived = 3;
}

message CurrentMonthMoneySpent {
  string month = 1 [(gxs.api.noomit) = true];
  Money moneySpent = 2 [(gxs.api.noomit) = true];
}

message CurrentMonthMoneyReceived {
  string month = 1 [(gxs.api.noomit) = true];
  Money moneyReceived = 2 [(gxs.api.noomit) = true];
}

message GetPocketInterestEarnedRequest {
  string pocketID = 1;
  string pocketType = 2;
}

message GetPocketInterestEarnedResponse {
  Money totalInterestEarned = 1 [(gxs.api.noomit) = true];
}

message GetAccountCalendarActivityRequest {
  string accountID = 1;
}

message GetPocketActivitiesRequest {
  string pocketID = 1;
  string pocketType = 2;
  string endDate = 3;
  string startDate = 4;
  int64 pageSize = 5;
  string endingAfter = 6;
  string startingBefore = 7;
}

message GetPocketActivitiesResponse {
  map<string, string> links = 1 [(gxs.api.noomit) = true];
  repeated TransactionHistoryResponse data = 2 [(gxs.api.noomit) = true];
}

message GetPocketActivityDetailRequest {
  string pocketID = 1;
  string pocketType = 2;
  string transactionID = 3;
}

message GetPocketActivityDetailResponse {
  int64 amount = 1 [(gxs.api.noomit) = true];
  string currency = 2;
  string transactionDescription = 3;
  string clientTransactionID = 4;
  string transactionRemarks = 5;
  string status = 6;
  string statusDescription = 7;
  Category category = 8;
  CounterParty counterParty = 9;
  google.protobuf.Timestamp transactionTimestamp = 10;
}

message GetCASAInterestEarnedRequest {
  string accountID = 1;
}

message GetCASAInterestEarnedResponse {
  Money totalInterestEarned = 1 [(gxs.api.noomit) = true];
}

// Represents an amount of money with its currency type.
message Money {
  // The three-letter currency code defined in ISO 4217.
  string currencyCode = 1 [(gxs.api.noomit) = true];
  // Monetary value using a currency's smallest unit
  int64 val = 2 [(gxs.api.noomit) = true];
}

// GetSTM453TransactionInfoRequest ...
message GetSTM453TransactionInfoRequest {
  string       transactionID       = 1;
}

// GetSTM453TransactionInfoResponse ...
message GetSTM453TransactionInfoResponse {
  string                      transactionType         = 1;
  Money                       amount                  = 2;
  string                      transactionSubtype      = 3;
  google.protobuf.Timestamp   transactionTimestamp    = 4;
  string                      fastTransactionID       = 5;
  string                      senderAccountID         = 6;
  string                      receiverAccountID       = 7;
  string                      recipientBank           = 8;
}

// GetInternalTransactionDetailRequest ...
message GetInternalTransactionDetailRequest {
  string accountID = 1  [(gxs.api.validate) = "regex,pattern=^[0-9]*$"];
  string transactionID = 2 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
  string batchID = 3 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
}

// GetInternalTransactionDetailResponse ...
message GetInternalTransactionDetailResponse {
  int64 amount = 1;
  string currency = 2;
  string transactionID = 3;
  string creditOrDebit = 4;
  string status = 5;
  CounterParty counterParty = 6;
  google.protobuf.Timestamp transactionTimestamp = 7;
  string transactionType = 8;
  string transactionSubtype = 9;
  string externalTransactionID = 10;
  string transactionDescription = 11;
  Account account = 12;
  string formattedLocalAmount = 13;
  string formattedForeignAmount = 14;
  string accountID = 15;
  CardTransactionDetail cardTransactionDetail = 16;
  string cashAccountCode = 17;
  string serviceType = 18;
  bool isPartialSettlement = 19 [(gxs.api.noomit) = true];
  int64 capturedAmountTillDate = 20 [(gxs.api.noomit) = true];
  int64 capturedOriginalAmountTillDate = 21 [(gxs.api.noomit) = true];
}

// GetOpsSearchRequest ...
message GetOpsSearchRequest {
  string accountID = 1  [(gxs.api.validate) = "regex,pattern=^[0-9]*$"];
  string transactionID = 2 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
  string batchID = 3 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
  string externalID = 4 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9-_]*$"];
  int64 fromAmount = 5;
  int64 toAmount = 6;
  string transactionType = 7;
  string transactionSubtype = 8;
  string status = 9;
  string endDate = 10 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9:-]*$"];
  string startDate = 11 [(gxs.api.validate) = "regex,pattern=^[a-zA-Z0-9:-]*$"];
  int64 pageSize = 12;
  string endingAfter = 13;
  string startingBefore = 14;
}

message OpsSearchResponse {
  int64 amount = 1;
  string currency = 2;
  string transactionID = 3;
  string batchID = 4;
  string creditOrDebit = 5;
  string status = 6;
  CounterParty counterParty = 7;
  google.protobuf.Timestamp transactionTimestamp = 8;
  string transactionType = 9;
  string transactionSubtype = 10;
  string externalTransactionID = 11;
  string transactionDescription = 12;
  string transactionRemarks = 13;
  string accountID = 14;
  bool isPartialSettlement = 15 [(gxs.api.noomit) = true];
  int64 capturedAmountTillDate = 16 [(gxs.api.noomit) = true];
  int64 capturedOriginalAmountTillDate = 17 [(gxs.api.noomit) = true];
  repeated CounterParty counterParties = 18;
}

// GetOpsSearchResponse ...
message GetOpsSearchResponse {
  map<string, string> links = 1 [(gxs.api.noomit) = true];
  repeated OpsSearchResponse data = 2 [(gxs.api.noomit) = true];
}

// GetLendingTransactionDetailRequest ...
message GetLendingTransactionDetailRequest {
  string accountID = 1;
  string transactionID = 2;
  string productVariantCode = 3;
}

// GetLendingTransactionDetailResponse ...
message GetLendingTransactionDetailResponse {
  Money amount = 1;
  string transactionDescription = 2;
  string status = 3;
  string statusDescription = 4;
  google.protobuf.Timestamp transactionTimestamp = 5;
  DrawDownDetails drawdownDetails = 6;
  RepaymentDetails repaymentDetails = 7;
  LendingCounterParty counterParty = 8;
}

message DrawDownDetails {
  string withdrawalID = 1;
  string transferID = 2;
  string loanName = 3;
  RepaymentPeriod repaymentPeriod = 4;
  MonthlyRepayment monthlyRepayment = 5;
  TotalPayment totalPayment = 6;
  string productVariantCode = 7;
}

message RepaymentPeriod {
  int64 timePeriodInMonths = 1;
  string startDate = 2;
  string endDate = 3;
  int64 installmentCount = 4;
}

message LendingCounterParty {
  string displayName = 1;
  string iconURL = 2;
  string accountID = 3;
  ProxyDetail proxyDetail = 4;
}

message ProxyDetail {
  string channel = 1;
  string type = 2;
  string value = 3;
}

message MonthlyRepayment {
  repeated string descriptions = 1;
  Money emi = 2;
  Money odi = 3;
  Money lastEmi = 4;
}

message TotalPayment {
  Money totalAmount = 1;
  Money interestAmount = 2;
  float interestRate = 3;
  float effectiveInterestRate = 4;
  float penalInterestRate = 5;
  Money disbursedAmount = 6;
  float processingFeeRate = 7;
  Money processingFeeAmount = 8;
}

message RepaymentDetails {
  string repaymentID = 1;
  string transferID = 2;
  PaymentForInstalments paymentForInstalments = 3;
  PaymentForOverdue paymentForOverdue = 4;
  LastRepaymentOverpaid lastRepaymentOverpaid = 5;
}

message PaymentForInstalments {
  Money totalInterestSaved = 1;
  repeated RepaymentSummary repaymentSummary = 2;
}

message PaymentForOverdue {
  Money totalOverdueInterestPaid = 1;
  repeated RepaymentSummary repaymentSummary = 2;
}

message LastRepaymentOverpaid {
  string title = 1;
  string description = 2;
}

message RepaymentSummary {
  string loanName = 1;
  Money paidAmount = 2;
  Money remainingPayableBeforePayment = 3;
  Money remainingPayable = 4;
  Money interestSavedAmount = 5;
  Money overdueInterestPaidAmount = 6;
}

message GetTransactionsHistoryCXRequest {
  string accountID = 1;
  string endDateTimeStamp = 2;
  string startDateTimeStamp = 3;
  int64 pageSize = 4;
  string endingAfter = 5;
  string startingBefore = 6;
  string transactionDomain = 7;
  string transactionType = 8;
  string transactionSubtype = 9;
}

message AccountIconDetail{
  string accountID = 1;
  string accountIconURL = 2;
}

message TransactionCode {
  string domain = 1;
  string type = 2;
  string subType = 3;
}

message TransactionHistoryCXResponse {
  string transactionID = 1;
  string batchID = 2;
  string iconURL = 3;
  AccountIconDetail accountIconDetail = 4;
  int64 amount = 5;
  string currency = 6;
  string status = 7;
  TransactionType transactionType = 8;
  google.protobuf.Timestamp creationTimestamp = 9;
  TransactionCode transactionCode = 10;
  CxCardTransactionDetail cardTransactionDetail = 11;
  repeated TransactionTags tags = 12;
  string transactionDescription = 13;
  string transactionRemarks = 14;
  CounterParty counterParty = 15;
  bool isPartialSettlement = 16 [(gxs.api.noomit) = true];
  int64 capturedAmountTillDate = 17 [(gxs.api.noomit) = true];
  int64 capturedOriginalAmountTillDate = 18 [(gxs.api.noomit) = true];
  repeated CounterParty counterParties = 19;
}

message CxCardTransactionDetail {
  string cardID = 1;
  string tailCardNumber = 2;
  string exchangeRate = 3;
  string bankFee = 4;
  CardRefundDetail cardRefundDetail = 5;
  google.protobuf.Timestamp settlementDate = 6;
}

message CardRefundDetail{
  string originalChargeID = 1;
}

message GetTransactionsHistoryCXResponse{
  map<string, string> links = 1 [(gxs.api.noomit) = true];
  repeated TransactionHistoryCXResponse data = 2 [(gxs.api.noomit) = true];
}

message GetLendingTransactionSearchRequest {
  string accountID = 1;
  string endDate = 2;
  string startDate = 3;
  int64 pageSize = 4;
  string startingBefore = 5;
  string productVariantCode = 6;
}

message GetLendingTransactionSearchForCRMRequest {
  string accountID = 1;
  string endDate = 2;
  string startDate = 3;
  int64 pageSize = 4;
  string startingBefore = 5;
  string endingAfter = 6;
  string status = 7;
  string transactionType = 8;
  string transactionSubtype = 9;
  string productVariantCode = 10;
}

message GetLendingTransactionSearchResponse {
  map<string, string> links = 1 [(gxs.api.noomit) = true];
  repeated LendingTransactionSearchData data = 2 [(gxs.api.noomit) = true];
}

message LendingTransactionSearchData {
  string transactionID = 1;
  string description = 2;
  string iconURL = 3;
  Money amount = 4;
  string status = 5;
  google.protobuf.Timestamp transactionTimestamp = 6;
  string transactionType = 7;
  string transactionSubtype = 8;
  repeated string loanNames = 9;
}

message GetLendingTransactionDetailForCRMRequest {
  string accountID = 1;
  string transactionID = 2;
  string productVariantCode = 3;
}

service TxHistory {

  // GetAccountTransactionsSearch: API to fetch all transactions for customer (currently CASA account only).
  rpc GetAccountTransactionsSearch(GetTransactionsHistoryRequest) returns (GetTransactionsHistoryResponse) {
    option (google.api.http) = {
      post: "/v1/transactions-search",
      body: "*",
    };
  }

  // GetAccountCalendarActivity: API to fetch year and month for all accounts.
  rpc GetAccountCalendarActivity(GetAccountCalendarActivityRequest) returns (AccountCalendarActivityResponse) {
    option (google.api.http) = {
      post : "/v2/account-calendar-activity",
      body : "*",
    };
  }

  // GetTransactionDetail: API to fetch transaction detail (currently only for CASA with pockets)
  rpc GetTransactionDetail(GetTransactionDetailRequest) returns (GetTransactionDetailResponse) {
    option (google.api.http) = {
      post : "/v2/transactions/get",
      body : "*",
    };
  }

  // GetCASATransactionsSummary: API to share detail of monthly spent and interest earned for CASA account only.
  rpc GetCASATransactionsSummary(GetCASATransactionsSummaryRequest) returns (GetCASATransactionsSummaryResponse) {
    option (google.api.http) = {
      post : "/v1/casa-transactions-summary",
      body : "*",
    };
  }

  // GetPocketInterestEarned: API to fetch total interest earned on the pocket
  rpc GetPocketInterestEarned(GetPocketInterestEarnedRequest) returns (GetPocketInterestEarnedResponse) {
    option (google.api.http) = {
      post : "/v1/pocket-total-interest-earned",
      body : "*",
    };
  }
  // GetPocketActivities: fetch all transactions related to pockets
  rpc GetPocketActivities(GetPocketActivitiesRequest) returns (GetPocketActivitiesResponse) {
    option (google.api.http) = {
      post : "/v1/pocket-activities",
      body : "*",
    };
  }
  // GetPocketActivityDetail: API to fetch transaction detail for pocket transactions
  rpc GetPocketActivityDetail(GetPocketActivityDetailRequest) returns (GetPocketActivityDetailResponse) {
    option (google.api.http) = {
      post : "/v1/pocket-activity-detail",
      body : "*",
    };
  }
  // GetCASAInterestEarned: API to fetch total interest earned on the CASA account
  rpc GetCASAInterestEarned(GetCASAInterestEarnedRequest) returns (GetCASAInterestEarnedResponse) {
    option (google.api.http) = {
      post : "/v1/casa-total-interest-earned",
      body : "*",
    };
  }

  // GetSTM453TransactionInfo: API to fetch STM453 Transaction Info
  rpc GetSTM453TransactionInfo(GetSTM453TransactionInfoRequest) returns (GetSTM453TransactionInfoResponse) {
    option (google.api.http) = {
      post: "/v1/stm453-transaction-info",
      body: "*",
    };
  }

  // ListAccountTransactionsSearch: API to fetch transactions for customer
  rpc ListAccountTransactionsSearch(GetTransactionsHistoryRequest) returns (GetTransactionsHistoryResponse) {
    option (google.api.http) = {
      post: "/v2/transactions/list",
      body: "*",
    };
  }

  // GetInternalTransactionDetail: API to fetch transaction detail for internal usage
  rpc GetInternalTransactionDetail(GetInternalTransactionDetailRequest) returns (GetInternalTransactionDetailResponse) {
    option (google.api.http) = {
      post: "/v1/internal/transactions/get",
      body: "*",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.CustomerPortal", "servicename.TransactionStatements", "servicename.PaymentConfig"]
    };
  }

  // GetOpsSearch: API to fetch transaction detail for customer portal usage
  rpc GetOpsSearch(GetOpsSearchRequest) returns (GetOpsSearchResponse) {
    option (google.api.http) = {
      post: "/v1/internal/transactions/list",
      body: "*",
    };
  }

  // GetAccountTransactionsSearchForCX: API to fetch all transactions for customer (currently CASA account only) with transaction domain filter for CX.
  rpc GetAccountTransactionsSearchForCX(GetTransactionsHistoryCXRequest) returns (GetTransactionsHistoryCXResponse) {
    option (google.api.http) = {
      post: "/v1/transactions-search-cx",
      body: "*",
    };
  }

  // GetLendingTransactionDetail: API to fetch information about a particular loan transaction.
  rpc GetLendingTransactionDetail(GetLendingTransactionDetailRequest) returns (GetLendingTransactionDetailResponse) {
    option (google.api.http) = {
      post: "/v1/lending/transaction-detail",
      body: "*",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.SentryT6"]
    };
  }

  // GetLendingTransactionSearch: API to fetch all transactions for customer (Flexi Loan Account only).
  rpc GetLendingTransactionSearch(GetLendingTransactionSearchRequest) returns (GetLendingTransactionSearchResponse) {
    option (google.api.http) = {
      post: "/v1/lending/transactions-search",
      body: "*",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.SentryT6"]
    };
  }

  // GetLendingTransactionSearchForCRM: API to fetch all transactions for customer from CRM (Flexi Loan Account only).
  rpc GetLendingTransactionSearchForCRM(GetLendingTransactionSearchForCRMRequest) returns (GetLendingTransactionSearchResponse) {
    option (google.api.http) = {
      post: "/v1/lending/transactions-search-cx",
      body: "*",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.CustomerPortal"]
    };
  }

  // GetLendingTransactionDetailForCRM: API to fetch information about a particular loan transaction from CRM.
  rpc GetLendingTransactionDetailForCRM(GetLendingTransactionDetailForCRMRequest) returns (GetLendingTransactionDetailResponse) {
    option (google.api.http) = {
      post: "/v1/lending/transaction-detail-cx",
      body: "*",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.CustomerPortal", "servicename.SentryPartnerT6"]
    };
  }
}

