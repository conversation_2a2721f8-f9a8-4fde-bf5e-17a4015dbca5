// Code generated by mockery v2.45.0. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/transaction-history/api"

	mock "github.com/stretchr/testify/mock"
)

// TxHistory is an autogenerated mock type for the TxHistory type
type TxHistory struct {
	mock.Mock
}

// GetAccountCalendarActivity provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetAccountCalendarActivity(ctx context.Context, req *api.GetAccountCalendarActivityRequest) (*api.AccountCalendarActivityResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountCalendarActivity")
	}

	var r0 *api.AccountCalendarActivityResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetAccountCalendarActivityRequest) (*api.AccountCalendarActivityResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetAccountCalendarActivityRequest) *api.AccountCalendarActivityResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AccountCalendarActivityResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetAccountCalendarActivityRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAccountTransactionsSearch provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetAccountTransactionsSearch(ctx context.Context, req *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountTransactionsSearch")
	}

	var r0 *api.GetTransactionsHistoryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryRequest) *api.GetTransactionsHistoryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTransactionsHistoryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTransactionsHistoryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAccountTransactionsSearchForCX provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetAccountTransactionsSearchForCX(ctx context.Context, req *api.GetTransactionsHistoryCXRequest) (*api.GetTransactionsHistoryCXResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountTransactionsSearchForCX")
	}

	var r0 *api.GetTransactionsHistoryCXResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryCXRequest) (*api.GetTransactionsHistoryCXResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryCXRequest) *api.GetTransactionsHistoryCXResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTransactionsHistoryCXResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTransactionsHistoryCXRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCASAInterestEarned provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetCASAInterestEarned(ctx context.Context, req *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCASAInterestEarned")
	}

	var r0 *api.GetCASAInterestEarnedResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCASAInterestEarnedRequest) *api.GetCASAInterestEarnedResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCASAInterestEarnedResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCASAInterestEarnedRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCASATransactionsSummary provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetCASATransactionsSummary(ctx context.Context, req *api.GetCASATransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCASATransactionsSummary")
	}

	var r0 *api.GetCASATransactionsSummaryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCASATransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCASATransactionsSummaryRequest) *api.GetCASATransactionsSummaryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCASATransactionsSummaryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCASATransactionsSummaryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInternalTransactionDetail provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetInternalTransactionDetail(ctx context.Context, req *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetInternalTransactionDetail")
	}

	var r0 *api.GetInternalTransactionDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInternalTransactionDetailRequest) *api.GetInternalTransactionDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetInternalTransactionDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetInternalTransactionDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLendingTransactionDetail provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetLendingTransactionDetail(ctx context.Context, req *api.GetLendingTransactionDetailRequest) (*api.GetLendingTransactionDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetLendingTransactionDetail")
	}

	var r0 *api.GetLendingTransactionDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionDetailRequest) (*api.GetLendingTransactionDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionDetailRequest) *api.GetLendingTransactionDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLendingTransactionDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLendingTransactionDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLendingTransactionDetailForCRM provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetLendingTransactionDetailForCRM(ctx context.Context, req *api.GetLendingTransactionDetailForCRMRequest) (*api.GetLendingTransactionDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetLendingTransactionDetailForCRM")
	}

	var r0 *api.GetLendingTransactionDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionDetailForCRMRequest) (*api.GetLendingTransactionDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionDetailForCRMRequest) *api.GetLendingTransactionDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLendingTransactionDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLendingTransactionDetailForCRMRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLendingTransactionSearch provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetLendingTransactionSearch(ctx context.Context, req *api.GetLendingTransactionSearchRequest) (*api.GetLendingTransactionSearchResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetLendingTransactionSearch")
	}

	var r0 *api.GetLendingTransactionSearchResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionSearchRequest) (*api.GetLendingTransactionSearchResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionSearchRequest) *api.GetLendingTransactionSearchResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLendingTransactionSearchResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLendingTransactionSearchRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLendingTransactionSearchForCRM provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetLendingTransactionSearchForCRM(ctx context.Context, req *api.GetLendingTransactionSearchForCRMRequest) (*api.GetLendingTransactionSearchResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetLendingTransactionSearchForCRM")
	}

	var r0 *api.GetLendingTransactionSearchResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionSearchForCRMRequest) (*api.GetLendingTransactionSearchResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLendingTransactionSearchForCRMRequest) *api.GetLendingTransactionSearchResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLendingTransactionSearchResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLendingTransactionSearchForCRMRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOpsSearch provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetOpsSearch(ctx context.Context, req *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetOpsSearch")
	}

	var r0 *api.GetOpsSearchResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetOpsSearchRequest) *api.GetOpsSearchResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetOpsSearchResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetOpsSearchRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPocketActivities provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetPocketActivities(ctx context.Context, req *api.GetPocketActivitiesRequest) (*api.GetPocketActivitiesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPocketActivities")
	}

	var r0 *api.GetPocketActivitiesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketActivitiesRequest) (*api.GetPocketActivitiesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketActivitiesRequest) *api.GetPocketActivitiesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPocketActivitiesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPocketActivitiesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPocketActivityDetail provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetPocketActivityDetail(ctx context.Context, req *api.GetPocketActivityDetailRequest) (*api.GetPocketActivityDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPocketActivityDetail")
	}

	var r0 *api.GetPocketActivityDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketActivityDetailRequest) (*api.GetPocketActivityDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketActivityDetailRequest) *api.GetPocketActivityDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPocketActivityDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPocketActivityDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPocketInterestEarned provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetPocketInterestEarned(ctx context.Context, req *api.GetPocketInterestEarnedRequest) (*api.GetPocketInterestEarnedResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPocketInterestEarned")
	}

	var r0 *api.GetPocketInterestEarnedResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketInterestEarnedRequest) (*api.GetPocketInterestEarnedResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketInterestEarnedRequest) *api.GetPocketInterestEarnedResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPocketInterestEarnedResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPocketInterestEarnedRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSTM453TransactionInfo provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetSTM453TransactionInfo(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetSTM453TransactionInfo")
	}

	var r0 *api.GetSTM453TransactionInfoResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSTM453TransactionInfoRequest) *api.GetSTM453TransactionInfoResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetSTM453TransactionInfoResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetSTM453TransactionInfoRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTransactionDetail provides a mock function with given fields: ctx, req
func (_m *TxHistory) GetTransactionDetail(ctx context.Context, req *api.GetTransactionDetailRequest) (*api.GetTransactionDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTransactionDetail")
	}

	var r0 *api.GetTransactionDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionDetailRequest) (*api.GetTransactionDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionDetailRequest) *api.GetTransactionDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTransactionDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTransactionDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAccountTransactionsSearch provides a mock function with given fields: ctx, req
func (_m *TxHistory) ListAccountTransactionsSearch(ctx context.Context, req *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListAccountTransactionsSearch")
	}

	var r0 *api.GetTransactionsHistoryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionsHistoryRequest) *api.GetTransactionsHistoryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTransactionsHistoryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTransactionsHistoryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTxHistory creates a new instance of TxHistory. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTxHistory(t interface {
	mock.TestingT
	Cleanup(func())
}) *TxHistory {
	mock := &TxHistory{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
