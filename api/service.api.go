// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package api

import (
	context "context"
	time "time"
)

type TransactionType string

const (
	TransactionType_QR_PAYMENT TransactionType = "QR_PAYMENT"
)

type TransactionTags string

const (
	TransactionTags_OVERDRAFT TransactionTags = "OVERDRAFT"
)

type GetTransactionsHistoryRequest struct {
	AccountID      string `json:"accountID,omitempty" validate:"regex,pattern=^[0-9]*$"`
	EndDate        string `json:"endDate,omitempty" validate:"regex,pattern=^[a-zA-Z0-9:-]*$"`
	StartDate      string `json:"startDate,omitempty" validate:"regex,pattern=^[a-zA-Z0-9:-]*$"`
	PageSize       int64  `json:"pageSize,omitempty"`
	EndingAfter    string `json:"endingAfter,omitempty"`
	StartingBefore string `json:"startingBefore,omitempty"`
}

type TransactionHistoryResponse struct {
	TransactionID     string    `json:"transactionID,omitempty"`
	BatchID           string    `json:"batchID,omitempty"`
	DisplayName       string    `json:"displayName,omitempty"`
	IconURL           string    `json:"iconURL,omitempty"`
	Amount            int64     `json:"amount"`
	Currency          string    `json:"currency,omitempty"`
	Status            string    `json:"status,omitempty"`
	CreationTimestamp time.Time `json:"creationTimestamp,omitempty"`
	Category          *Category `json:"category,omitempty"`
}

type GetTransactionsHistoryResponse struct {
	Links map[string]string            `json:"links"`
	Data  []TransactionHistoryResponse `json:"data"`
}

type AccountCalendarActivityRequest struct {
}

type Date struct {
	Year   string   `json:"year,omitempty"`
	Months []string `json:"months,omitempty"`
}

type AccountCalendarActivityResponse struct {
	Dates []Date `json:"dates"`
}

type GetTransactionDetailRequest struct {
	AccountID     string `json:"accountID,omitempty" validate:"regex,pattern=^[0-9]*$"`
	TransactionID string `json:"transactionID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
}

type CounterParty struct {
	DisplayName        string            `json:"displayName,omitempty"`
	IconURL            string            `json:"iconURL,omitempty"`
	TransactionDetails map[string]string `json:"transactionDetails,omitempty"`
	SwiftCode          string            `json:"swiftCode,omitempty"`
	AccountNumber      string            `json:"accountNumber,omitempty"`
}

type Account struct {
	DisplayName string `json:"displayName,omitempty"`
}

type Category struct {
	Id   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type GetTransactionDetailResponse struct {
	Amount                 int64                  `json:"amount"`
	Currency               string                 `json:"currency,omitempty"`
	TransactionID          string                 `json:"transactionID,omitempty"`
	BatchID                string                 `json:"batchID,omitempty"`
	SourceOfFund           string                 `json:"sourceOfFund,omitempty"`
	TransactionDescription string                 `json:"transactionDescription,omitempty"`
	TransactionRemarks     string                 `json:"transactionRemarks,omitempty"`
	Status                 string                 `json:"status,omitempty"`
	StatusDescription      string                 `json:"statusDescription,omitempty"`
	Category               *Category              `json:"category,omitempty"`
	CounterParty           *CounterParty          `json:"counterParty,omitempty"`
	TransactionTimestamp   *time.Time             `json:"transactionTimestamp,omitempty"`
	CardTransactionDetail  *CardTransactionDetail `json:"cardTransactionDetail,omitempty"`
	FormattedLocalAmount   string                 `json:"formattedLocalAmount,omitempty"`
	FormattedForeignAmount string                 `json:"formattedForeignAmount,omitempty"`
	HasReceipt             bool                   `json:"hasReceipt"`
}

type CardTransactionDetail struct {
	CardID                   string    `json:"cardID,omitempty"`
	TailCardNumber           string    `json:"tailCardNumber,omitempty"`
	SettlementDate           *time.Time `json:"settlementDate,omitempty"`
	ExchangeRate             string    `json:"exchangeRate,omitempty"`
	BankFee                  string    `json:"bankFee,omitempty"`
	Mcc                      string    `json:"mcc,omitempty"`
	MaskedCardNumber         string    `json:"maskedCardNumber,omitempty"`
	TransactionCountry       string    `json:"transactionCountry,omitempty"`
	TransactionCategory      string    `json:"transactionCategory,omitempty"`
	TransactionSubCategory   string    `json:"transactionSubCategory,omitempty"`
	NetworkID                string    `json:"networkID,omitempty"`
	ThreeDsValidation        string    `json:"threeDsValidation,omitempty"`
	PinValidation            string    `json:"pinValidation,omitempty"`
	RetrievalReferenceNumber string    `json:"retrievalReferenceNumber,omitempty"`
	CardProxyNumber          string    `json:"cardProxyNumber,omitempty"`
	MerchantName             string    `json:"merchantName,omitempty"`
	AuthCode                 string    `json:"authCode,omitempty"`
	AcquirerReferenceData    string    `json:"acquirerReferenceData,omitempty"`
}

type GetCASATransactionsSummaryRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetCASATransactionsSummaryResponse struct {
	TotalInterestEarned       *Money                     `json:"totalInterestEarned"`
	CurrentMonthMoneySpent    *CurrentMonthMoneySpent    `json:"currentMonthMoneySpent,omitempty"`
	CurrentMonthMoneyReceived *CurrentMonthMoneyReceived `json:"currentMonthMoneyReceived,omitempty"`
}

type CurrentMonthMoneySpent struct {
	Month      string `json:"month"`
	MoneySpent *Money `json:"moneySpent"`
}

type CurrentMonthMoneyReceived struct {
	Month         string `json:"month"`
	MoneyReceived *Money `json:"moneyReceived"`
}

type GetPocketInterestEarnedRequest struct {
	PocketID   string `json:"pocketID,omitempty"`
	PocketType string `json:"pocketType,omitempty"`
}

type GetPocketInterestEarnedResponse struct {
	TotalInterestEarned *Money `json:"totalInterestEarned"`
}

type GetAccountCalendarActivityRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetPocketActivitiesRequest struct {
	PocketID       string `json:"pocketID,omitempty"`
	PocketType     string `json:"pocketType,omitempty"`
	EndDate        string `json:"endDate,omitempty"`
	StartDate      string `json:"startDate,omitempty"`
	PageSize       int64  `json:"pageSize,omitempty"`
	EndingAfter    string `json:"endingAfter,omitempty"`
	StartingBefore string `json:"startingBefore,omitempty"`
}

type GetPocketActivitiesResponse struct {
	Links map[string]string            `json:"links"`
	Data  []TransactionHistoryResponse `json:"data"`
}

type GetPocketActivityDetailRequest struct {
	PocketID      string `json:"pocketID,omitempty"`
	PocketType    string `json:"pocketType,omitempty"`
	TransactionID string `json:"transactionID,omitempty"`
}

type GetPocketActivityDetailResponse struct {
	Amount                 int64         `json:"amount"`
	Currency               string        `json:"currency,omitempty"`
	TransactionDescription string        `json:"transactionDescription,omitempty"`
	ClientTransactionID    string        `json:"clientTransactionID,omitempty"`
	TransactionRemarks     string        `json:"transactionRemarks,omitempty"`
	Status                 string        `json:"status,omitempty"`
	StatusDescription      string        `json:"statusDescription,omitempty"`
	Category               *Category     `json:"category,omitempty"`
	CounterParty           *CounterParty `json:"counterParty,omitempty"`
	// TransactionTimestamp was manually changed to *time.Time instead of generated time.Time, in order to have empty zero value
	TransactionTimestamp *time.Time `json:"transactionTimestamp,omitempty"`
}

type GetCASAInterestEarnedRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetCASAInterestEarnedResponse struct {
	TotalInterestEarned *Money `json:"totalInterestEarned"`
}

// Represents an amount of money with its currency type.
type Money struct {
	CurrencyCode string `json:"currencyCode"`
	Val          int64  `json:"val"`
}

// GetSTM453TransactionInfoRequest ...
type GetSTM453TransactionInfoRequest struct {
	TransactionID string `json:"transactionID,omitempty"`
}

// GetSTM453TransactionInfoResponse ...
type GetSTM453TransactionInfoResponse struct {
	TransactionType      string    `json:"transactionType,omitempty"`
	Amount               *Money    `json:"amount,omitempty"`
	TransactionSubtype   string    `json:"transactionSubtype,omitempty"`
	TransactionTimestamp time.Time `json:"transactionTimestamp,omitempty"`
	FastTransactionID    string    `json:"fastTransactionID,omitempty"`
	SenderAccountID      string    `json:"senderAccountID,omitempty"`
	ReceiverAccountID    string    `json:"receiverAccountID,omitempty"`
	RecipientBank        string    `json:"recipientBank,omitempty"`
}

// GetInternalTransactionDetailRequest ...
type GetInternalTransactionDetailRequest struct {
	AccountID     string `json:"accountID,omitempty" validate:"regex,pattern=^[0-9]*$"`
	TransactionID string `json:"transactionID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
	BatchID       string `json:"batchID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
}

// GetInternalTransactionDetailResponse ...
type GetInternalTransactionDetailResponse struct {
	Amount                         int64                  `json:"amount,omitempty"`
	Currency                       string                 `json:"currency,omitempty"`
	TransactionID                  string                 `json:"transactionID,omitempty"`
	CreditOrDebit                  string                 `json:"creditOrDebit,omitempty"`
	Status                         string                 `json:"status,omitempty"`
	CounterParty                   *CounterParty          `json:"counterParty,omitempty"`
	TransactionTimestamp           *time.Time             `json:"transactionTimestamp,omitempty"`
	TransactionType                string                 `json:"transactionType,omitempty"`
	TransactionSubtype             string                 `json:"transactionSubtype,omitempty"`
	ExternalTransactionID          string                 `json:"externalTransactionID,omitempty"`
	TransactionDescription         string                 `json:"transactionDescription,omitempty"`
	Account                        *Account               `json:"account,omitempty"`
	FormattedLocalAmount           string                 `json:"formattedLocalAmount,omitempty"`
	FormattedForeignAmount         string                 `json:"formattedForeignAmount,omitempty"`
	AccountID                      string                 `json:"accountID,omitempty"`
	CardTransactionDetail          *CardTransactionDetail `json:"cardTransactionDetail,omitempty"`
	CashAccountCode                string                 `json:"cashAccountCode,omitempty"`
	ServiceType                    string                 `json:"serviceType,omitempty"`
	IsPartialSettlement            bool                   `json:"isPartialSettlement"`
	CapturedAmountTillDate         int64                  `json:"capturedAmountTillDate"`
	CapturedOriginalAmountTillDate int64                  `json:"capturedOriginalAmountTillDate"`
}

// GetOpsSearchRequest ...
type GetOpsSearchRequest struct {
	AccountID          string `json:"accountID,omitempty" validate:"regex,pattern=^[0-9]*$"`
	TransactionID      string `json:"transactionID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
	BatchID            string `json:"batchID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
	ExternalID         string `json:"externalID,omitempty" validate:"regex,pattern=^[a-zA-Z0-9-_]*$"`
	FromAmount         int64  `json:"fromAmount,omitempty"`
	ToAmount           int64  `json:"toAmount,omitempty"`
	TransactionType    string `json:"transactionType,omitempty"`
	TransactionSubtype string `json:"transactionSubtype,omitempty"`
	Status             string `json:"status,omitempty"`
	EndDate            string `json:"endDate,omitempty" validate:"regex,pattern=^[a-zA-Z0-9:-]*$"`
	StartDate          string `json:"startDate,omitempty" validate:"regex,pattern=^[a-zA-Z0-9:-]*$"`
	PageSize           int64  `json:"pageSize,omitempty"`
	EndingAfter        string `json:"endingAfter,omitempty"`
	StartingBefore     string `json:"startingBefore,omitempty"`
}

type OpsSearchResponse struct {
	Amount                         int64          `json:"amount,omitempty"`
	Currency                       string         `json:"currency,omitempty"`
	TransactionID                  string         `json:"transactionID,omitempty"`
	BatchID                        string         `json:"batchID,omitempty"`
	CreditOrDebit                  string         `json:"creditOrDebit,omitempty"`
	Status                         string         `json:"status,omitempty"`
	CounterParty                   *CounterParty  `json:"counterParty,omitempty"`
	TransactionTimestamp           time.Time      `json:"transactionTimestamp,omitempty"`
	TransactionType                string         `json:"transactionType,omitempty"`
	TransactionSubtype             string         `json:"transactionSubtype,omitempty"`
	ExternalTransactionID          string         `json:"externalTransactionID,omitempty"`
	TransactionDescription         string         `json:"transactionDescription,omitempty"`
	TransactionRemarks             string         `json:"transactionRemarks,omitempty"`
	AccountID                      string         `json:"accountID,omitempty"`
	IsPartialSettlement            bool           `json:"isPartialSettlement"`
	CapturedAmountTillDate         int64          `json:"capturedAmountTillDate"`
	CapturedOriginalAmountTillDate int64          `json:"capturedOriginalAmountTillDate"`
	CounterParties                 []CounterParty `json:"counterParties,omitempty"`
}

// GetOpsSearchResponse ...
type GetOpsSearchResponse struct {
	Links map[string]string   `json:"links"`
	Data  []OpsSearchResponse `json:"data"`
}

// GetLendingTransactionDetailRequest ...
type GetLendingTransactionDetailRequest struct {
	AccountID          string `json:"accountID,omitempty"`
	TransactionID      string `json:"transactionID,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

// GetLendingTransactionDetailResponse ...
type GetLendingTransactionDetailResponse struct {
	Amount                 *Money               `json:"amount,omitempty"`
	TransactionDescription string               `json:"transactionDescription,omitempty"`
	Status                 string               `json:"status,omitempty"`
	StatusDescription      string               `json:"statusDescription,omitempty"`
	TransactionTimestamp   time.Time            `json:"transactionTimestamp,omitempty"`
	DrawdownDetails        *DrawDownDetails     `json:"drawdownDetails,omitempty"`
	RepaymentDetails       *RepaymentDetails    `json:"repaymentDetails,omitempty"`
	CounterParty           *LendingCounterParty `json:"counterParty,omitempty"`
}

type DrawDownDetails struct {
	WithdrawalID       string            `json:"withdrawalID,omitempty"`
	TransferID         string            `json:"transferID,omitempty"`
	LoanName           string            `json:"loanName,omitempty"`
	RepaymentPeriod    *RepaymentPeriod  `json:"repaymentPeriod,omitempty"`
	MonthlyRepayment   *MonthlyRepayment `json:"monthlyRepayment,omitempty"`
	TotalPayment       *TotalPayment     `json:"totalPayment,omitempty"`
	ProductVariantCode string            `json:"productVariantCode,omitempty"`
}

type RepaymentPeriod struct {
	TimePeriodInMonths int64  `json:"timePeriodInMonths,omitempty"`
	StartDate          string `json:"startDate,omitempty"`
	EndDate            string `json:"endDate,omitempty"`
	InstallmentCount   int64  `json:"installmentCount,omitempty"`
}

type LendingCounterParty struct {
	DisplayName string       `json:"displayName,omitempty"`
	IconURL     string       `json:"iconURL,omitempty"`
	AccountID   string       `json:"accountID,omitempty"`
	ProxyDetail *ProxyDetail `json:"proxyDetail,omitempty"`
}

type ProxyDetail struct {
	Channel string `json:"channel,omitempty"`
	Type    string `json:"type,omitempty"`
	Value   string `json:"value,omitempty"`
}

type MonthlyRepayment struct {
	Descriptions []string `json:"descriptions,omitempty"`
	Emi          *Money   `json:"emi,omitempty"`
	Odi          *Money   `json:"odi,omitempty"`
	LastEmi      *Money   `json:"lastEmi,omitempty"`
}

type TotalPayment struct {
	TotalAmount           *Money  `json:"totalAmount,omitempty"`
	InterestAmount        *Money  `json:"interestAmount,omitempty"`
	InterestRate          float32 `json:"interestRate,omitempty"`
	EffectiveInterestRate float32 `json:"effectiveInterestRate,omitempty"`
	PenalInterestRate     float32 `json:"penalInterestRate,omitempty"`
	DisbursedAmount       *Money  `json:"disbursedAmount,omitempty"`
	ProcessingFeeRate     float32 `json:"processingFeeRate,omitempty"`
	ProcessingFeeAmount   *Money  `json:"processingFeeAmount,omitempty"`
}

type RepaymentDetails struct {
	RepaymentID           string                 `json:"repaymentID,omitempty"`
	TransferID            string                 `json:"transferID,omitempty"`
	PaymentForInstalments *PaymentForInstalments `json:"paymentForInstalments,omitempty"`
	PaymentForOverdue     *PaymentForOverdue     `json:"paymentForOverdue,omitempty"`
	LastRepaymentOverpaid *LastRepaymentOverpaid `json:"lastRepaymentOverpaid,omitempty"`
}

type PaymentForInstalments struct {
	TotalInterestSaved *Money             `json:"totalInterestSaved,omitempty"`
	RepaymentSummary   []RepaymentSummary `json:"repaymentSummary,omitempty"`
}

type PaymentForOverdue struct {
	TotalOverdueInterestPaid *Money             `json:"totalOverdueInterestPaid,omitempty"`
	RepaymentSummary         []RepaymentSummary `json:"repaymentSummary,omitempty"`
}

type LastRepaymentOverpaid struct {
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
}

type RepaymentSummary struct {
	LoanName                      string `json:"loanName,omitempty"`
	PaidAmount                    *Money `json:"paidAmount,omitempty"`
	RemainingPayableBeforePayment *Money `json:"remainingPayableBeforePayment,omitempty"`
	RemainingPayable              *Money `json:"remainingPayable,omitempty"`
	InterestSavedAmount           *Money `json:"interestSavedAmount,omitempty"`
	OverdueInterestPaidAmount     *Money `json:"overdueInterestPaidAmount,omitempty"`
}

type GetTransactionsHistoryCXRequest struct {
	AccountID          string `json:"accountID,omitempty"`
	EndDateTimeStamp   string `json:"endDateTimeStamp,omitempty"`
	StartDateTimeStamp string `json:"startDateTimeStamp,omitempty"`
	PageSize           int64  `json:"pageSize,omitempty"`
	EndingAfter        string `json:"endingAfter,omitempty"`
	StartingBefore     string `json:"startingBefore,omitempty"`
	TransactionDomain  string `json:"transactionDomain,omitempty"`
	TransactionType    string `json:"transactionType,omitempty"`
	TransactionSubtype string `json:"transactionSubtype,omitempty"`
}

type AccountIconDetail struct {
	AccountID      string `json:"accountID,omitempty"`
	AccountIconURL string `json:"accountIconURL,omitempty"`
}

type TransactionCode struct {
	Domain  string `json:"domain,omitempty"`
	Type    string `json:"type,omitempty"`
	SubType string `json:"subType,omitempty"`
}

type TransactionHistoryCXResponse struct {
	TransactionID                  string                   `json:"transactionID,omitempty"`
	BatchID                        string                   `json:"batchID,omitempty"`
	IconURL                        string                   `json:"iconURL,omitempty"`
	AccountIconDetail              *AccountIconDetail       `json:"accountIconDetail,omitempty"`
	Amount                         int64                    `json:"amount,omitempty"`
	Currency                       string                   `json:"currency,omitempty"`
	Status                         string                   `json:"status,omitempty"`
	TransactionType                TransactionType          `json:"transactionType,omitempty"`
	CreationTimestamp              time.Time                `json:"creationTimestamp,omitempty"`
	TransactionCode                *TransactionCode         `json:"transactionCode,omitempty"`
	CardTransactionDetail          *CxCardTransactionDetail `json:"cardTransactionDetail,omitempty"`
	Tags                           []TransactionTags        `json:"tags,omitempty"`
	TransactionDescription         string                   `json:"transactionDescription,omitempty"`
	TransactionRemarks             string                   `json:"transactionRemarks,omitempty"`
	CounterParty                   *CounterParty            `json:"counterParty,omitempty"`
	IsPartialSettlement            bool                     `json:"isPartialSettlement"`
	CapturedAmountTillDate         int64                    `json:"capturedAmountTillDate"`
	CapturedOriginalAmountTillDate int64                    `json:"capturedOriginalAmountTillDate"`
	CounterParties                 []CounterParty           `json:"counterParties,omitempty"`
}

type CxCardTransactionDetail struct {
	CardID           string            `json:"cardID,omitempty"`
	TailCardNumber   string            `json:"tailCardNumber,omitempty"`
	ExchangeRate     string            `json:"exchangeRate,omitempty"`
	BankFee          string            `json:"bankFee,omitempty"`
	CardRefundDetail *CardRefundDetail `json:"cardRefundDetail,omitempty"`
	SettlementDate   *time.Time        `json:"settlementDate,omitempty"`
}

type CardRefundDetail struct {
	OriginalChargeID string `json:"originalChargeID,omitempty"`
}

type GetTransactionsHistoryCXResponse struct {
	Links map[string]string              `json:"links"`
	Data  []TransactionHistoryCXResponse `json:"data"`
}

type GetLendingTransactionSearchRequest struct {
	AccountID          string `json:"accountID,omitempty"`
	EndDate            string `json:"endDate,omitempty"`
	StartDate          string `json:"startDate,omitempty"`
	PageSize           int64  `json:"pageSize,omitempty"`
	StartingBefore     string `json:"startingBefore,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

type GetLendingTransactionSearchForCRMRequest struct {
	AccountID          string `json:"accountID,omitempty"`
	EndDate            string `json:"endDate,omitempty"`
	StartDate          string `json:"startDate,omitempty"`
	PageSize           int64  `json:"pageSize,omitempty"`
	StartingBefore     string `json:"startingBefore,omitempty"`
	EndingAfter        string `json:"endingAfter,omitempty"`
	Status             string `json:"status,omitempty"`
	TransactionType    string `json:"transactionType,omitempty"`
	TransactionSubtype string `json:"transactionSubtype,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

type GetLendingTransactionSearchResponse struct {
	Links map[string]string              `json:"links"`
	Data  []LendingTransactionSearchData `json:"data"`
}

type LendingTransactionSearchData struct {
	TransactionID        string    `json:"transactionID,omitempty"`
	Description          string    `json:"description,omitempty"`
	IconURL              string    `json:"iconURL,omitempty"`
	Amount               *Money    `json:"amount,omitempty"`
	LoanNames            []string  `json:"loanNames,omitempty"`
	Status               string    `json:"status,omitempty"`
	TransactionTimestamp time.Time `json:"transactionTimestamp,omitempty"`
	TransactionType      string    `json:"transactionType,omitempty"`
	TransactionSubtype   string    `json:"transactionSubtype,omitempty"`
}

type GetLendingTransactionDetailForCRMRequest struct {
	AccountID          string `json:"accountID,omitempty"`
	TransactionID      string `json:"transactionID,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

type TxHistory interface {
	// GetAccountTransactionsSearch: API to fetch all transactions for customer (currently CASA account only).
	GetAccountTransactionsSearch(ctx context.Context, req *GetTransactionsHistoryRequest) (*GetTransactionsHistoryResponse, error)
	// GetAccountCalendarActivity: API to fetch year and month for all accounts.
	GetAccountCalendarActivity(ctx context.Context, req *GetAccountCalendarActivityRequest) (*AccountCalendarActivityResponse, error)
	// GetTransactionDetail: API to fetch transaction detail (currently only for CASA with pockets)
	GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*GetTransactionDetailResponse, error)
	// GetCASATransactionsSummary: API to share detail of monthly spent and interest earned for CASA account only.
	GetCASATransactionsSummary(ctx context.Context, req *GetCASATransactionsSummaryRequest) (*GetCASATransactionsSummaryResponse, error)
	// GetPocketInterestEarned: API to fetch total interest earned on the pocket
	GetPocketInterestEarned(ctx context.Context, req *GetPocketInterestEarnedRequest) (*GetPocketInterestEarnedResponse, error)
	// GetPocketActivities: fetch all transactions related to pockets
	GetPocketActivities(ctx context.Context, req *GetPocketActivitiesRequest) (*GetPocketActivitiesResponse, error)
	// GetPocketActivityDetail: API to fetch transaction detail for pocket transactions
	GetPocketActivityDetail(ctx context.Context, req *GetPocketActivityDetailRequest) (*GetPocketActivityDetailResponse, error)
	// GetCASAInterestEarned: API to fetch total interest earned on the CASA account
	GetCASAInterestEarned(ctx context.Context, req *GetCASAInterestEarnedRequest) (*GetCASAInterestEarnedResponse, error)
	// GetSTM453TransactionInfo: API to fetch STM453 Transaction Info
	GetSTM453TransactionInfo(ctx context.Context, req *GetSTM453TransactionInfoRequest) (*GetSTM453TransactionInfoResponse, error)
	// ListAccountTransactionsSearch: API to fetch transactions for customer
	ListAccountTransactionsSearch(ctx context.Context, req *GetTransactionsHistoryRequest) (*GetTransactionsHistoryResponse, error)
	// GetInternalTransactionDetail: API to fetch transaction detail for internal usage
	GetInternalTransactionDetail(ctx context.Context, req *GetInternalTransactionDetailRequest) (*GetInternalTransactionDetailResponse, error)
	// GetOpsSearch: API to fetch transaction detail for customer portal usage
	GetOpsSearch(ctx context.Context, req *GetOpsSearchRequest) (*GetOpsSearchResponse, error)
	// GetAccountTransactionsSearchForCX: API to fetch all transactions for customer (currently CASA account only) with transaction domain filter for CX.
	GetAccountTransactionsSearchForCX(ctx context.Context, req *GetTransactionsHistoryCXRequest) (*GetTransactionsHistoryCXResponse, error)
	// GetLendingTransactionDetail: API to fetch information about a particular loan transaction.
	GetLendingTransactionDetail(ctx context.Context, req *GetLendingTransactionDetailRequest) (*GetLendingTransactionDetailResponse, error)
	// GetLendingTransactionSearch: API to fetch all transactions for customer (Flexi Loan Account only).
	GetLendingTransactionSearch(ctx context.Context, req *GetLendingTransactionSearchRequest) (*GetLendingTransactionSearchResponse, error)
	// GetLendingTransactionSearchForCRM: API to fetch all transactions for customer from CRM (Flexi Loan Account only).
	GetLendingTransactionSearchForCRM(ctx context.Context, req *GetLendingTransactionSearchForCRMRequest) (*GetLendingTransactionSearchResponse, error)
	// GetLendingTransactionDetailForCRM: API to fetch information about a particular loan transaction from CRM.
	GetLendingTransactionDetailForCRM(ctx context.Context, req *GetLendingTransactionDetailForCRMRequest) (*GetLendingTransactionDetailResponse, error)
}
