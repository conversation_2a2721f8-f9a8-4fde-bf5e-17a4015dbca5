// Package metrics ...
package metrics

// Metrics name
const (
	// PaymentEngineKafkaMessage ....
	PaymentEngineKafkaMessage = "payment_engine_stream"
	// DepositsCoreKafkaMessage ...
	DepositsCoreKafkaMessage = "deposits_core_stream"
	// DepositsCoreBalanceKafkaMessage ...
	DepositsCoreBalanceKafkaMessage = "deposits_core_balance_stream"
	// DigicardTxnKafkaMessage ...
	DigicardTxnKafkaMessage = "digicard_txn_stream"
	// LoanCoreTxKafkaMessage ...
	LoanCoreTxKafkaMessage = "loan_core_tx_stream"
	// InterestAggMessage ...
	InterestAggMessage = "interest_agg_stream"

	// WorkerMetricTag ...
	WorkerMetricTag = "worker"
	// InterestAggregateOneTimerDurationMetric ...
	InterestAggregateOneTimerDurationMetric = "interest_aggregate_one_timer_duration"
	// InterestAggregateOneTimerAccountCountMetric ...
	InterestAggregateOneTimerAccountCountMetric = "interest_aggregate_one_timer_account_count"
	// DailyInterestAggregateDurationMetric ...
	DailyInterestAggregateDurationMetric = "daily_interest_aggregate_duration"
	// DailyInterestAggregateCountMetric ...
	DailyInterestAggregateCountMetric = "daily_interest_aggregate_count"
	// StreamConsumeLatencyMetric ...
	StreamConsumeLatencyMetric = "stream_consume_latency"
	// StreamProcessLatencyMetric ...
	StreamProcessLatencyMetric = "stream_process_latency"

	// PostingInstructionForLending ...
	PostingInstructionForLending = "posting_instruction_lending"
)

// Operation ID
const (
	// CalculateAggregatedInterestOpID ...
	CalculateAggregatedInterestOpID = "calculate_aggregated_interest"
	// CalculateBatchAggregatedInterestOpID ...
	CalculateBatchAggregatedInterestOpID = "calculate_batch_aggregated_interest"
	// UpsertAggregatedInterestOpID ...
	UpsertAggregatedInterestOpID = "upsert_aggregated_interest"
	// DepositsCoreKafkaMessageForInterestAgg ...
	DepositsCoreKafkaMessageForInterestAgg = "deposits_core_stream_for_interest_agg"
)

// Metrics tags
const (
	// Action ...
	Action = "action:"
	// Duplicate ...
	Duplicate = "duplicate"
	// CountMismatch ...
	CountMismatch = "count_mismatch"
	// SuccessTag ...
	SuccessTag = "status:success"
	// FailTag ...
	FailedTag = "status:failed"
	// StatusReasonTag ...
	StatusReasonTag = "status_reason:"
	// OperationIDTag ...
	OperationIDTag = "operation_id:"
	// WorkerIDTag ...
	WorkerIDTag = "worker_id:"
)
