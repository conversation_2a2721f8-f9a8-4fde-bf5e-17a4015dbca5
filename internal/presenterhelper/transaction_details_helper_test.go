package presenterhelper

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/common/tenants"
)

func TestGetCounterPartyNameForCardOpsTransactions(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc     string
		txnType  string
		expected string
	}{
		{
			desc:     "ManualDebitReconTransactionType",
			txnType:  constants.ManualDebitReconTransactionType,
			expected: "Bank adjustment",
		},
		{
			desc:     "ManualCreditReconTransactionType",
			txnType:  constants.ManualCreditReconTransactionType,
			expected: "Bank adjustment",
		},
		{
			desc:     "DisputeChargeBackTransactionType",
			txnType:  constants.DisputeChargeBackTransactionType,
			expected: "Bank refund",
		},
		{
			desc:     "DisputeBankRefundTransactionType",
			txnType:  constants.DisputeBankRefundTransactionType,
			expected: "Bank refund",
		},
		{
			desc:     "DisputeBankFraudRefundTransactionType",
			txnType:  constants.DisputeBankFraudRefundTransactionType,
			expected: "Bank refund",
		},
		{
			desc:     "ProvisionalCreditTransactionType",
			txnType:  constants.ProvisionalCreditTransactionType,
			expected: "Provisional credit",
		},
		{
			desc:     "ProvisionalCreditReversalTransactionType",
			txnType:  constants.ProvisionalCreditReversalTransactionType,
			expected: "Provisional credit reversal",
		},
		{
			desc:     "OperationalLossTransactionType",
			txnType:  constants.OperationalLossTransactionType,
			expected: "Bank refund",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			counterPartyDisplayName := getCounterPartyNameForCardOpsTransactions(scenario.txnType)
			assert.Equal(t, scenario.expected, counterPartyDisplayName)
		})
	}
}

func TestGetPaymentCasaTransactionStatus(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc                     string
		transaction              *storage.TransactionsData
		transactionPaymentDetail *storage.PaymentDetail
		expected                 string
	}{
		{
			desc: "IsInterestPayoutTxn - rejected",
			transaction: &storage.TransactionsData{
				TransactionType: constants.InterestPayoutTransactionType,
				BatchStatus:     constants.BatchStatusRejected,
			},
			transactionPaymentDetail: &storage.PaymentDetail{},
			expected:                 constants.FailedStatus,
		},
		{
			desc: "IsInterestPayoutTxn - accepted",
			transaction: &storage.TransactionsData{
				TransactionType: constants.InterestPayoutTransactionType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{},
			expected:                 constants.CompletedStatus,
		},
		{
			desc: "ApplyEarmarkTransactionType",
			transaction: &storage.TransactionsData{
				TransactionType: constants.ApplyEarmarkTransactionType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{},
			expected:                 constants.ProcessingStatus,
		},
		{
			desc: "ReleaseEarmarkTransactionType",
			transaction: &storage.TransactionsData{
				TransactionType: constants.ReleaseEarmarkTransactionType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{},
			expected:                 constants.CanceledStatus,
		},
		{
			desc: "PaymentTransaction",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SendMoneyTxType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{
				Status: "COMPLETED",
			},
			expected: constants.CompletedStatus,
		},
		{
			desc: "SpendMoneyRevTxType",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendMoneyRevTxType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{
				Status: "REFUNDED",
			},
			expected: constants.RefundedStatus,
		},
		{
			desc: "CardIssuanceFeeProcessing",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.OutboundAuthorisation,
			},
			expected: constants.ProcessingStatus,
		},
		{
			desc: "CardIssuanceFeeRejected",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusRejected,
				TmTransactionType: constants.OutboundAuthorisation,
			},
			expected: constants.FailedStatus,
		},
		{
			desc: "CardIssuanceFeeCompleted",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.SettlementTxType,
			},
			expected: constants.CompletedStatus,
		},
		{
			desc: "CardIssuanceFeeCancelled",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.Release,
			},
			expected: constants.CanceledStatus,
		},
		{
			desc: "CardIssuanceFeeWaiverCompleted",
			transaction: &storage.TransactionsData{
				TransactionType: constants.NewCardIssuanceFeeWaiverTransactionType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			expected: constants.CompletedStatus,
		},
		{
			desc: "InsuranceAuthorisedTransaction",
			transaction: &storage.TransactionsData{
				TransactionType: constants.InsurPremiumTxType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{
				Status: "AUTHORISED",
			},
			expected: constants.ProcessingStatus,
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			counterPartyDisplayName := GetPaymentCasaTransactionStatus(scenario.transaction, scenario.transactionPaymentDetail)
			assert.Equal(t, scenario.expected, counterPartyDisplayName)
		})
	}
}

func TestGetCounterPartyDisplayNameForGrabTxn(t *testing.T) {
	ctx := context.Background()
	scenarios := []struct {
		desc     string
		txnType  string
		metadata string
		expected string
	}{
		{
			desc:     "Grab Refund",
			txnType:  constants.SpendMoneyRevTxType,
			metadata: `{"grab_activity_type": "DEFAULT"}`,
			expected: "Grab",
		},
		{
			desc:     "Known Grab Activity Type",
			txnType:  constants.SpendMoneyTxType,
			metadata: `{"grab_activity_type": "FOOD"}`,
			expected: "Grab Food",
		},
		{
			desc:     "Unknown Grab Activity Type",
			txnType:  constants.SpendMoneyTxType,
			metadata: `{"grab_activity_type": "UNKNOWN"}`,
			expected: "Grab",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.desc, func(t *testing.T) {
			paymentDetail := &storage.PaymentDetail{
				TransactionType: scenario.txnType,
				Metadata:        []byte(scenario.metadata),
			}
			displayName := GetCounterPartyDisplayNameForGrabTxn(ctx, paymentDetail)
			assert.Equal(t, scenario.expected, displayName)
		})
	}
}

func TestHasReceipt(t *testing.T) {
	InitTransactionResponseHelperFuncs(constants.IconURLMap)
	scenarios := []struct {
		desc     string
		key      string
		status   string
		expected bool
	}{
		{
			desc:     "FailedStatus",
			key:      constants.TransferIntrabankDebitScenarioKey,
			status:   constants.FailedStatus,
			expected: false,
		},
		{
			desc:     "ProcessingStatus",
			key:      constants.TransferIntrabankDebitScenarioKey,
			status:   constants.ProcessingStatus,
			expected: false,
		},
		{
			desc:     "CompletedStatus",
			key:      constants.TransferIntrabankDebitScenarioKey,
			status:   constants.CompletedStatus,
			expected: true,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.desc, func(t *testing.T) {
			result := HasReceipt(scenario.key, scenario.status)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestGetPaymentCasaTransactionStatusForOpsSearch(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc                     string
		transaction              *storage.TransactionsData
		transactionPaymentDetail *storage.PaymentDetail
		expected                 string
	}{
		{
			desc: "CardIssuanceFeeProcessing",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.OutboundAuthorisation,
			},
			expected: constants.ProcessingStatus,
		},
		{
			desc: "CardIssuanceFeeRejected",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusRejected,
				TmTransactionType: constants.OutboundAuthorisation,
			},
			expected: constants.FailedStatus,
		},
		{
			desc: "CardIssuanceFeeCompleted",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.SettlementTxType,
			},
			expected: constants.CompletedStatus,
		},
		{
			desc: "CardIssuanceFeeCancelled",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.NewCardIssuanceFeeTransactionType,
				BatchStatus:       constants.BatchStatusAccepted,
				TmTransactionType: constants.Release,
			},
			expected: constants.CanceledStatus,
		},
		{
			desc: "CardIssuanceFeeWaiverCompleted",
			transaction: &storage.TransactionsData{
				TransactionType: constants.NewCardIssuanceFeeWaiverTransactionType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			expected: constants.CompletedStatus,
		},
		{
			desc: "InsuranceAuthorisedTransaction",
			transaction: &storage.TransactionsData{
				TransactionType: constants.InsurPremiumTxType,
				BatchStatus:     constants.BatchStatusAccepted,
			},
			transactionPaymentDetail: &storage.PaymentDetail{
				Status: "AUTHORISED",
			},
			expected: constants.ProcessingStatus,
		},
	}
	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			counterPartyDisplayName := GetPaymentCasaTransactionStatusForOpsSearch(scenario.transaction, scenario.transactionPaymentDetail)
			assert.Equal(t, scenario.expected, counterPartyDisplayName)
		})
	}
}

func Test_getCounterPartyDisplayNameForPocketDBMY(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)

	type args struct {
		transaction       *storage.TransactionsData
		batchDetails      map[string]string
		isChildAccount    bool
		pocketIDToNameMap map[string]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Pocket activity - Money added to Boost Pocket, isChildAccount true, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "credit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET"},
				isChildAccount:    true,
				pocketIDToNameMap: nil,
			},
			want: "Money added",
		},
		{
			name: "Pocket activity - Money withdrawn from Boost Pocket, isChildAccount true, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "debit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET"},
				isChildAccount:    true,
				pocketIDToNameMap: nil,
			},
			want: "Money withdrawn",
		},
		{
			name: "CASA activity - Money added to Boost Pocket, isChildAccount false, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "credit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET", "pocket_id": "*************"},
				isChildAccount:    false,
				pocketIDToNameMap: map[string]string{"*************": "dummy-pocket-name"},
			},
			want: "dummy-pocket-name",
		},
		{
			name: "CASA activity - Money withdrawn from Boost Pocket, isChildAccount true, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "debit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET", "pocket_id": "*************"},
				isChildAccount:    false,
				pocketIDToNameMap: map[string]string{"*************": "dummy-pocket-name"},
			},
			want: "dummy-pocket-name",
		},
		{
			name: "CASA activity - Money added to Boost Pocket, isChildAccount false, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "credit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET", "pocket_id": "*************"},
				isChildAccount:    false,
				pocketIDToNameMap: nil,
			},
			want: "Some Boost",
		},
		{
			name: "CASA activity - Money withdrawn from Boost Pocket, isChildAccount true, nil pocketIDToNameMap",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "debit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET", "pocket_id": "*************"},
				isChildAccount:    false,
				pocketIDToNameMap: nil,
			},
			want: "Some Boost",
		},
		{
			name: "CASA activity - Money added to Boost Pocket, isChildAccount false, batchDetails does not contain pocket_id",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "credit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET"},
				isChildAccount:    false,
				pocketIDToNameMap: nil,
			},
			want: "Some Boost",
		},
		{
			name: "CASA activity - Money withdrawn from Boost Pocket, isChildAccount true, batchDetails does not contain pocket_id",
			args: args{
				transaction:       &storage.TransactionsData{DebitOrCredit: "debit"},
				batchDetails:      map[string]string{"sub_account_type": "BOOST_POCKET"},
				isChildAccount:    false,
				pocketIDToNameMap: nil,
			},
			want: "Some Boost",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getCounterPartyDisplayNameForPocketDBMY(tt.args.transaction, tt.args.batchDetails, tt.args.isChildAccount, tt.args.pocketIDToNameMap), "getCounterPartyDisplayNameForPocketDBMY(%v, %v, %v, %v)", tt.args.transaction, tt.args.batchDetails, tt.args.isChildAccount, tt.args.pocketIDToNameMap)
		})
	}
}
