package presenterhelper

import (
	"context"
	"encoding/json"
	"sort"

	"github.com/shopspring/decimal"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

var (
	mapTypeToPriority = map[string]int64{
		constants.Transfer:              1,
		constants.OutboundAuthorisation: 2,
		constants.Release:               3,
		constants.Settlement:            4,
	}
)

// FilterInvalidTransactions Filter out the transactions based on the account phase and type
func FilterInvalidTransactions(transactions []*storage.TransactionsData) *storage.TransactionsData {
	var validDrawDownTx, validRepaymentTx *storage.TransactionsData
	SortTransactions(transactions)
	for _, transaction := range transactions {
		if IsRepayment(transaction) {
			validRepaymentTx = validRepaymentTransaction(transaction)
			if validRepaymentTx != nil {
				return validRepaymentTx
			}
		} else if IsDrawDown(transaction) {
			validDrawDownTx = validDrawDownTransaction(transaction)
			if validDrawDownTx != nil {
				return validDrawDownTx
			}
		}
	}
	return nil
}

// SortTransactions will sort transactions in the order of tmTransactionType priority defined ...
func SortTransactions(transactions []*storage.TransactionsData) {
	sort.Slice(transactions, func(i, j int) bool {
		return mapTypeToPriority[transactions[i].TmTransactionType] < mapTypeToPriority[transactions[j].TmTransactionType]
	})
}

// GetPaymentTransactionID returns paymentTransactionID from transaction when loanDetail has the field missing for PROCESSING transaction ...
func GetPaymentTransactionID(transaction *storage.TransactionsData, loanDetail *storage.LoanDetail) string {
	if loanDetail.PaymentTransactionID != "" {
		return loanDetail.PaymentTransactionID
	}
	return transaction.ClientBatchID
}

// validRepaymentTransaction filters out repayment transactions to show in txn history
func validRepaymentTransaction(txn *storage.TransactionsData) *storage.TransactionsData {
	if txn.AccountAddress == constants.RepaymentMadeAccountAddress && txn.DebitOrCredit == constants.CREDIT && txn.AccountPhase == constants.PostingPhaseCommitted {
		return txn
	}
	return nil
}

// validDrawDownTransaction filters out drawdown transactions to show in txn history
func validDrawDownTransaction(txn *storage.TransactionsData) *storage.TransactionsData {
	if txn.TmTransactionType == constants.Release || txn.TmTransactionType == constants.Transfer || txn.TmTransactionType == constants.OutboundAuthorisation {
		return txn
	}
	return nil
}

// IsRepayment ...
func IsRepayment(transaction *storage.TransactionsData) bool {
	return transaction.TransactionType == constants.RepaymentTransactionType
}

// IsDrawDown ...
func IsDrawDown(transaction *storage.TransactionsData) bool {
	if transaction.TransactionType == constants.DrawdownTransactionType ||
		transaction.TmTransactionType == constants.Release ||
		transaction.TmTransactionType == constants.OutboundAuthorisation {
		return true
	}
	return false
}

// GetLoanTxnIDFromTransaction returns the loan transaction id for a particular transaction
func GetLoanTxnIDFromTransaction(transaction *storage.TransactionsData) (string, error) {
	var details = make(map[string]string)
	if transaction.TransactionDetails != nil {
		err := json.Unmarshal(transaction.TransactionDetails, &details)
		if err != nil {
			return "", err
		}
	}
	loanTxnID := details["loanTransactionID"]
	if loanTxnID == "" && transaction.BatchDetails != nil {
		err := json.Unmarshal(transaction.BatchDetails, &details)
		if err != nil {
			return "", err
		}
	}
	loanTxnID = details["loanTransactionID"]
	return loanTxnID, nil
}

// ConvertToMoneyObj return a money Object
func ConvertToMoneyObj(amount decimal.Decimal) *api.Money {
	return &api.Money{
		CurrencyCode: "MYR",
		Val:          ConvertAmountToCents(amount),
	}
}

// ConvertAmountToCents Returns decimal amount converted to cents with the given precision
func ConvertAmountToCents(amount decimal.Decimal) int64 {
	return amount.Mul(decimal.NewFromFloat(100)).Round(0).IntPart()
}

// ConvertValueToFloat Returns decimal amount converted to cents with the given precision
func ConvertValueToFloat(value decimal.Decimal) float32 {
	floatValue, _ := value.Float64()
	return float32(floatValue)
}

// CreateTransactionFromLoanDetailForWriteOff will return a transaction for write off from loan detail
func CreateTransactionFromLoanDetailForWriteOff(loanDetail *storage.LoanDetail, accountID string, repaymentDetails dto.RepaymentDetail) (*storage.TransactionsData, error) {
	transactionDetails := map[string]string{
		"loanTransactionID": loanDetail.LoanTransactionID,
	}
	txnDetails, err := json.Marshal(transactionDetails)
	if err != nil {
		return nil, err
	}

	transaction := &storage.TransactionsData{
		ClientBatchID:       loanDetail.PaymentTransactionID,
		TransactionDomain:   loanDetail.TransactionDomain,
		TransactionType:     loanDetail.TransactionType,
		TransactionSubtype:  loanDetail.TransactionSubType,
		BatchValueTimestamp: loanDetail.CreatedAt,
		AccountID:           accountID,
		DebitOrCredit:       constants.CREDIT,
		AccountAddress:      constants.RepaymentMadeAccountAddress,
		AccountPhase:        constants.PostingPhaseCommitted,
		TransactionAmount:   repaymentDetails.TotalRepaymentAmount.String(),
		TransactionDetails:  txnDetails,
		TransactionCurrency: loanDetail.Currency,
		CreatedAt:           loanDetail.CreatedAt,
		UpdatedAt:           loanDetail.UpdatedAt,
	}
	return transaction, nil
}

// GetLoanTransactionDisplayNameForListing contains logic to return transaction display name in transactions list for both Retail and Biz Lending
func GetLoanTransactionDisplayNameForListing(transaction *storage.TransactionsData) string {
	if transaction.IsBizLendingDomain() {
		return GetBizLendingTransactionDisplayNameForListing(transaction)
	}
	if transaction.TransactionType == constants.DrawdownTransactionType {
		return constants.FlexiCreditDisplayName
	}
	return constants.FlexiCreditRepaymentDisplayName
}

// GetBizLendingTransactionDisplayNameForListing contains logic to return transaction display name in transactions list
func GetBizLendingTransactionDisplayNameForListing(transaction *storage.TransactionsData) string {
	if transaction.TransactionType == constants.DrawdownTransactionType {
		return constants.BizFlexiLoanDisplayName
	}
	return constants.BizFlexiLoanRepaymentDisplayName
}

// GetLoanTransactionDisplayNameForOpsSearch contains logic to return transaction display name in ops search
func GetLoanTransactionDisplayNameForOpsSearch(ctx context.Context, transaction *storage.TransactionsData, transactionLoanDetail *storage.LoanDetail) string {
	loanName := transactionLoanDetail.GetLoanName(ctx)
	if loanName != "" {
		return loanName
	}
	return GetLoanTransactionDisplayNameForListing(transaction)
}

// GetLoanCasaTransactionStatus contains logic to return transaction status based on DB data
func GetLoanCasaTransactionStatus(transaction *storage.TransactionsData, transactionLoanDetail *storage.LoanDetail) string {
	var status string

	// For other Transactions(Currently Payments)
	if transactionLoanDetail != nil {
		status = transactionLoanDetail.Status
	} else { // For now, this will happen for manual posting only
		status = formatDepositsCoreInterest(transaction)
	}
	return status
}

// GetLoanCounterParties ...
func GetLoanCounterParties(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail, transactionLoanDetail *storage.LoanDetail) []dto.CounterParty {
	var counterParties []dto.CounterParty
	swiftCode := ""
	serviceType := ""
	if paymentDetail != nil {
		if accountDetails := paymentDetail.GetCounterPartyAccount(ctx); accountDetails != nil {
			swiftCode = accountDetails.SwiftCode
		}
		if paymentMetadata := paymentDetail.GetPaymentMetadata(ctx); paymentMetadata != nil {
			serviceType = paymentMetadata.ServiceType
		}
	}
	transactionDetails := map[string]string{
		"serviceType": serviceType,
	}

	switch transactionLoanDetail.TransactionType {
	case constants.DrawdownTransactionType:
		disbursementDetail := transactionLoanDetail.GetDisbursementDetail(ctx)
		if disbursementDetail != nil {
			counterParties = append(counterParties, dto.CounterParty{
				DisplayName:        disbursementDetail.LoanName,
				AccountNumber:      transactionLoanDetail.AccountID,
				SwiftCode:          swiftCode,
				TransactionDetails: transactionDetails,
			})
		} else {
			counterParties = append(counterParties, dto.CounterParty{
				DisplayName:        GetLoanTransactionDisplayNameForListing(transaction),
				AccountNumber:      transactionLoanDetail.AccountID,
				SwiftCode:          swiftCode,
				TransactionDetails: transactionDetails,
			})
		}
	case constants.RepaymentTransactionType:
		repaymentDetail := transactionLoanDetail.GetRepaymentDetail(ctx)
		if repaymentDetail != nil && len(repaymentDetail.LoanRepaymentDetail) > 0 {
			for _, loanRepaymentDetail := range repaymentDetail.LoanRepaymentDetail {
				counterParties = append(counterParties, dto.CounterParty{
					DisplayName:        loanRepaymentDetail.LoanName,
					AccountNumber:      loanRepaymentDetail.AccountID,
					SwiftCode:          swiftCode,
					TransactionDetails: transactionDetails,
				})
			}
		} else {
			counterParties = append(counterParties, dto.CounterParty{
				DisplayName:        GetLoanTransactionDisplayNameForListing(transaction),
				AccountNumber:      transactionLoanDetail.AccountID,
				SwiftCode:          swiftCode,
				TransactionDetails: transactionDetails,
			})
		}
	}

	return counterParties
}
