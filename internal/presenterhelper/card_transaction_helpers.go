package presenterhelper

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"github.com/Rhymond/go-money"
	"github.com/shopspring/decimal"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/currency"
)

var (
	errInvalidConversionRate = errors.New("invalid conversion rate")
	errParseConversionRate   = errors.New("error when parse conversion rate")
)

const (
	mcDe43MerchantNameLength  = 22
	sanDe43TerminalNameLength = 25
	precisionDecimal          = 6
	lenOfConversionRate       = 8
)

// GetCardTransactionDisplayName contains logic to return transaction display name in transaction details
func GetCardTransactionDisplayName(ctx context.Context, transaction *storage.TransactionsData, txnDetail *storage.CardTransactionDetail) string {
	switch transaction.TransactionType {
	case constants.SpendCardAtmTransactionType:
		return localise.Translate(constants.ATMWithdrawal)
	case constants.ATMCashWithdrawalRefundTransactionType, constants.SpendCardATMReversalTransactionType:
		return localise.Translate(constants.ATMWithdrawalReversal)
	case constants.SpendCardPresentReversalTransactionType, constants.SpendCardNotPresentReversalTransactionType:
		return fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardRefundPaymentFrom), txnDetail.MerchantDescription)
	// Card maintenance fee
	case constants.NewCardIssuanceFeeTransactionType, constants.NewCardIssuanceFeeWaiverTransactionType:
		return localise.Translate(constants.NewCardIssuanceFee)
	case constants.CardAnnualFeeTransactionType, constants.CardAnnualFeeWaiverTransactionType:
		return localise.Translate(constants.CardAnnualFee)
	case constants.CardReplacementFeeTransactionType, constants.CardReplacementFeeWaiverTransactionType:
		return localise.Translate(constants.CardReplacementFee)
	}
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		return localise.Translate(constants.ATMWithdrawalFee)
	}
	return GetMerchantName(ctx, txnDetail)
}

// GetCardTransactionDisplayNameForListing contains logic to return transaction display name in transactions list
func GetCardTransactionDisplayNameForListing(ctx context.Context, transaction *storage.TransactionsData, txnDetail *storage.CardTransactionDetail) string {
	switch transaction.TransactionType {
	case constants.SpendCardAtmTransactionType:
		return localise.Translate(constants.ATMWithdrawal)
	case constants.ATMCashWithdrawalRefundTransactionType, constants.SpendCardATMReversalTransactionType:
		return localise.Translate(constants.ATMWithdrawalReversal)
	case constants.DomesticAtmFeeTransactionType:
		return localise.Translate(constants.MEPSATMWithdrawalFee)
	case constants.DomesticAtmFeeWaiverTransactionType:
		return localise.Translate(constants.MEPSATMWithdrawalFeeWaived)
	case constants.ManualDebitReconTransactionType, constants.ManualCreditReconTransactionType:
		return localise.Translate(constants.BankAdjustment)
	case constants.DisputeBankRefundTransactionType, constants.DisputeBankFraudRefundTransactionType, constants.DisputeChargeBackTransactionType, constants.OperationalLossTransactionType:
		return localise.Translate(constants.BankRefund)
	case constants.ProvisionalCreditTransactionType:
		return localise.Translate(constants.ProvisionalCredit)
	case constants.ProvisionalCreditReversalTransactionType:
		return localise.Translate(constants.ProvisionalCreditReversal)
	case constants.SpendCardPresentReversalTransactionType, constants.SpendCardNotPresentReversalTransactionType:
		return fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardRefundPaymentFrom), txnDetail.MerchantDescription)
	// Card maintenance fee
	case constants.NewCardIssuanceFeeTransactionType:
		return localise.Translate(constants.NewCardIssuanceFee)
	case constants.NewCardIssuanceFeeWaiverTransactionType:
		return localise.Translate(constants.NewCardIssuanceFeeWaived)
	case constants.CardAnnualFeeTransactionType:
		return localise.Translate(constants.CardAnnualFee)
	case constants.CardAnnualFeeWaiverTransactionType:
		return localise.Translate(constants.CardAnnualFeeWaived)
	case constants.CardReplacementFeeTransactionType:
		return localise.Translate(constants.CardReplacementFee)
	case constants.CardReplacementFeeWaiverTransactionType:
		return localise.Translate(constants.CardReplacementFeeWaived)
	}
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		return localise.Translate(constants.ATMWithdrawalFee)
	}
	if retailerID := GetRetailerID(ctx, transaction, txnDetail); len(retailerID) > 0 {
		return retailerID
	}
	return GetMerchantName(ctx, txnDetail)
}

// GetMerchantName get merchant name from description
func GetMerchantName(ctx context.Context, txnDetail *storage.CardTransactionDetail) string {
	md := txnDetail.GetMetadata(ctx)
	merchantDescription := txnDetail.MerchantDescription
	// MDT and MCN have the same spec to set the merchant name
	if (md.NetworkID == constants.MCN || md.NetworkID == constants.MDT) && len(merchantDescription) > mcDe43MerchantNameLength {
		merchantName := merchantDescription[0:mcDe43MerchantNameLength]
		return strings.TrimSpace(merchantName)
	}

	if md.NetworkID == constants.SAN && len(merchantDescription) > sanDe43TerminalNameLength {
		merchantName := merchantDescription[0:sanDe43TerminalNameLength]
		return strings.TrimSpace(merchantName)
	}

	return strings.TrimSpace(merchantDescription)
}

// GetFormattedLocalAmount currently we only charge card transaction in local currency
func GetFormattedLocalAmount(ctx context.Context, transaction *storage.TransactionsData) string {
	localCurrency := currency.GetByCode(transaction.TransactionCurrency)
	amountInCents := money.New(utils.GetAmountInCents(ctx, transaction.TransactionAmount), transaction.TransactionCurrency).Amount()
	return formatMoney(localCurrency, amountInCents)
}

// GetFormattedSettlementAmount currently we only charge card transaction in local currency
func GetFormattedSettlementAmount(txnDetail *storage.CardTransactionDetail) string {
	localCurrency := currency.GetByCode(txnDetail.Currency)
	return formatMoney(localCurrency, txnDetail.CaptureAmount)
}

// GetFormattedForeignAmount only return when if the txn is from overseas
// only overseas transaction will have different original currency
func GetFormattedForeignAmount(txnDetail *storage.CardTransactionDetail) string {
	if txnDetail.OriginalCurrency != txnDetail.Currency {
		fxCurrency := currency.GetByCode(txnDetail.OriginalCurrency)
		return formatMoney(fxCurrency, txnDetail.OriginalAmount)
	}
	return ""
}

// GetFormattedAmount by on currency
// Using for CaptureAmountTillDate and CaptureOriginalAmountTillDate
func GetFormattedAmount(currencyStr string, amount int64) string {
	fxCurrency := currency.GetByCode(currencyStr)
	return formatMoney(fxCurrency, amount)
}

// GetSettlementDate return settlement date when the transaction is completed aka posted
func GetSettlementDate(txnDetail *storage.CardTransactionDetail) *time.Time {
	if txnDetail.IsCardMaintenanceFee() {
		return nil
	}
	if txnDetail.Status == constants.CompletedStatus {
		valueAtTS := utils.TruncateTillSecond(txnDetail.ValueTimestamp)
		return &valueAtTS
	}
	return nil
}

// return formatted amount
// return empty string if the currency doesn't have predefined format
func formatMoney(c currency.Currency, amount int64) string {
	if c.Format != "" {
		amountInFloat := float64(amount) / math.Pow(10, float64(c.Exponent))
		return fmt.Sprintf(c.Format, amountInFloat)
	}
	return ""
}

// GetBankFee return foreign txn fee is waived for overseas transaction including online transaction
func GetBankFee(ctx context.Context, _ *storage.TransactionsData, txnDetail *storage.CardTransactionDetail) string {
	if txnDetail.IsForeignTransaction(ctx) {
		return localise.Translate(constants.FeeWaived)
	}
	return ""
}

// GetRetailerID if transaction belong to Paynet, will try to get retailerID from metadata
func GetRetailerID(ctx context.Context, transaction *storage.TransactionsData, txnDetail *storage.CardTransactionDetail) string {
	if transaction.TransactionSubtype == constants.PaynetMydebitTransactionSubtype {
		md := txnDetail.GetMetadata(ctx)
		if len(md.RetailerID) > 0 {
			return md.RetailerID
		}
	}
	return ""
}

// GetDBMYCardAtmFeeTransactionDecription localize content for ATM transaction fees
func GetDBMYCardAtmFeeTransactionDecription(transaction *storage.TransactionsData, displayName string) string {
	var desc string
	switch transaction.TransactionType {
	case constants.DomesticAtmFeeWaiverTransactionType:
		desc = localise.Translate(constants.MEPSATMWithdrawalFeeWaived)
	case constants.DomesticAtmFeeTransactionType:
		desc = localise.Translate(constants.MEPSATMWithdrawalFee)
	case constants.ATMCashWithdrawalRefundTransactionType:
		desc = localise.Translate(constants.ATMWithdrawalReversal)
	}
	return desc
}

// GetExchangeRate get an exchange rate
func GetExchangeRate(ctx context.Context, transaction *storage.TransactionsData, txnDetail *storage.CardTransactionDetail) string {
	if !txnDetail.HasCurrencyConversion(ctx) {
		return ""
	}
	md := txnDetail.GetMetadata(ctx)
	conversionRate, err := parseConversionRate(ctx, md.MCConversionRate)
	if err != nil {
		return ""
	}
	if conversionRate.String() != "" {
		return fmt.Sprintf("%s1.00 = %s%s", txnDetail.OriginalCurrency, transaction.TransactionCurrency, conversionRate.String())
	}
	return ""
}

// parseConversionRate parse conversion rate from value in metadata
func parseConversionRate(ctx context.Context, conversionRateValueMeta int64) (decimal.Decimal, error) {
	strConversionRateMeta := strconv.FormatInt(conversionRateValueMeta, 10)
	if len(strConversionRateMeta) != lenOfConversionRate {
		return decimal.Zero, errInvalidConversionRate
	}
	numOfFractionalPart, err := strconv.ParseFloat(string(strConversionRateMeta[0]), 32)
	if err != nil || numOfFractionalPart > 7 {
		return decimal.Zero, errInvalidConversionRate
	}
	lenStr := len(strConversionRateMeta)
	var strConversionRate string
	if int(numOfFractionalPart) < (lenStr - 1) {
		strConversionRate = fmt.Sprintf("%s.%s", strConversionRateMeta[1:(lenStr-int(numOfFractionalPart))], strConversionRateMeta[(lenStr-int(numOfFractionalPart)):])
	} else if int(numOfFractionalPart) == (lenStr - 1) {
		strConversionRate = fmt.Sprintf("%s.%s", "0", strConversionRateMeta[(lenStr-int(numOfFractionalPart)):])
	} else {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "invalid conversion rate")
		return decimal.Zero, errInvalidConversionRate
	}
	conversionRate, err := decimal.NewFromString(strConversionRate)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "invalid conversion rate")
		return decimal.Zero, errParseConversionRate
	}
	return conversionRate.Round(precisionDecimal), nil
}
