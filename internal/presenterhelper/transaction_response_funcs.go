// Package presenterhelper ...
package presenterhelper

import (
	"context"
	"errors"
	"fmt"

	"github.com/enescakir/emoji"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

type transactionAttributeHelper struct {
	Category        *api.Category
	Icon            string
	DescriptionFunc func(ctx context.Context, dbData interface{}, displayName string) (string, error)
	DetailsFunc     func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string
	CardDetailsFunc func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string
	LoanDetailsFunc func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string
	HasReceipt      bool
}

const (
	txnScenarioKey        = "txn_scenario"
	recipientReferenceKey = "recipient_reference"
	serviceTypeKey        = "service_type"

	// this is only applicable to CASA <-> pocket transactions
	// in other cases we get counterparty accountID from the PaymentDetail table
	counterPartyAccountIDKey = "counterparty_account_id"
)

// TransactionResponseHelperFuncs ...
var TransactionResponseHelperFuncs = make(map[string]transactionAttributeHelper, 20)

// InitTransactionResponseHelperFuncs ...
// nolint: funlen
func InitTransactionResponseHelperFuncs(IconURLMap map[string]string) {
	TransactionResponseHelperFuncs = map[string]transactionAttributeHelper{
		constants.TransferIntrabankDebitScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYIntraBankTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.TransferMoneyTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.Remarks
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: true,
		},
		constants.TransferIntrabankCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getDBMYIntraBankTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.TransferMoneyTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.Remarks
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferMoneyReversalIntrabankDebitScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getTransferMoneyRevTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.TransferMoneyRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetOriginalTransactionIDFromMetadata(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferMoneyReversalIntrabankCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getTransferMoneyRevTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.TransferMoneyRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetOriginalTransactionIDFromMetadata(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferMainToPocketDebitScenarioKey: {
			Category:        &api.Category{Name: "Withdrawal"},
			Icon:            IconURLMap[constants.PocketFundingTransactionSubType],
			DescriptionFunc: getDBMYPocketTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.PocketFundingTransactionSubType
				txnDetails[serviceTypeKey] = txnData.GetPocketServiceType(ctx)
				txnDetails[counterPartyAccountIDKey] = txnData.GetPocketTxnCounterPartyAccountID(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferMainToPocketCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.PocketFundingTransactionSubType],
			DescriptionFunc: getDBMYPocketTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.PocketFundingTransactionSubType
				txnDetails[serviceTypeKey] = txnData.GetPocketServiceType(ctx)
				txnDetails[counterPartyAccountIDKey] = txnData.GetPocketTxnCounterPartyAccountID(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferPocketToMainDebitScenarioKey: {
			Category:        &api.Category{Name: "Withdrawal"},
			Icon:            IconURLMap[constants.PocketWithdrawalIconPocketView],
			DescriptionFunc: getDBMYPocketTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.PocketWithdrawalTransactionSubType
				txnDetails[serviceTypeKey] = txnData.GetPocketServiceType(ctx)
				txnDetails[counterPartyAccountIDKey] = txnData.GetPocketTxnCounterPartyAccountID(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.TransferPocketToMainCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.PocketWithdrawalIconMainAccountView],
			DescriptionFunc: getDBMYPocketTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.PocketWithdrawalTransactionSubType
				txnDetails[serviceTypeKey] = txnData.GetPocketServiceType(ctx)
				txnDetails[counterPartyAccountIDKey] = txnData.GetPocketTxnCounterPartyAccountID(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.FundInScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: GetRPPTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.FundInTxType
				txnDetails["bank_name"] = paymentMetadata.BankName
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.FundInReversalScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getFundInRevTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.FundInRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetOriginalTransactionIDFromMetadata(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.SendMoneyDebitScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: GetRPPTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				counterPartyAccount := paymentData.GetCounterPartyAccount(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SendMoneyTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.RecipientReference
				txnDetails["payment_description"] = paymentMetadata.PaymentDescription
				if !paymentData.IsQrTxn(ctx) {
					txnDetails["type"] = getRPPProxyType(counterPartyAccount)
				}
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: true,
		},
		constants.SendMoneyReversalCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getSendMoneyRevTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SendMoneyRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetOriginalTransactionIDFromMetadata(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.PaymentTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Merchant],
			DescriptionFunc: getQrTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.QrPaymentTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.RecipientReference
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: true,
		},
		constants.PaymentReversalTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Merchant],
			DescriptionFunc: getQrTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.QrPaymentReversalTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.RecipientReference
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.ReceiveMoneyCreditScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: GetRPPTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.ReceiveMoneyTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.RecipientReference
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.ReceiveMoneyReversalDebitScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getReceiveMoneyRevTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.ReceiveMoneyRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetOriginalTransactionIDFromMetadata(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.SpendMoneyScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.Grab],
			DescriptionFunc: getGrabTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendMoneyTxType
				txnDetails[recipientReferenceKey] = paymentData.GetGrabRecipientReferenceFromActivityType(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.SpendMoneyReversalScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.Grab],
			DescriptionFunc: getGrabTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendMoneyRevTxType
				txnDetails[recipientReferenceKey] = paymentData.GetGrabRefundRecipientReference(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.InterestPayoutScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.InterestEarned],
			DescriptionFunc: getInterestDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.InterestPayoutTransactionType
				if txnData.GetInterestCampaignDescription(ctx) != "" {
					txnDetails[recipientReferenceKey] = txnData.GetInterestCampaignDescription(ctx)
				}
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.BonusInterestPayoutScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.InterestEarned],
			DescriptionFunc: getBonusInterestDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.BonusInterestPayoutTransactionType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.BonusInterestPayoutReversalScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.InterestEarned],
			DescriptionFunc: getBonusInterestDescription,
			DetailsFunc: func(_ context.Context, _ *storage.TransactionsData, _ *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.BonusInterestPayoutReversalTransactionType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.AdjustmentBankInitiatedDebitScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.BankAdjustment],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.AdjustmentBankInitiatedCreditScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.BankAdjustment],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.RewardsCashbackScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Rewards],
			DescriptionFunc: getDBMYRewardsTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.GetRewardsCampaignDescription(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.RewardsCashbackReversalScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Rewards],
			DescriptionFunc: getDBMYRewardsTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.GetRewardsCampaignDescription(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.RewardsCashbackTransactionTypeScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Rewards],
			DescriptionFunc: getDBMYRewardsTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.GetRewardsCampaignDescription(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.RewardsCashbackReversalTransactionTypeScenarioKey: {
			Category:        &api.Category{},
			Icon:            IconURLMap[constants.Rewards],
			DescriptionFunc: getDBMYRewardsTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.GetRewardsCampaignDescription(ctx)
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.ApplyEarmarkScenarioKey: {
			Category:        &api.Category{Name: "On hold amount"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.ApplyEarmarkTransactionType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.ReleaseEarmarkScenarioKey: {
			Category:        &api.Category{Name: "On hold amount"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.ReleaseEarmarkTransactionType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.ManualDebitReconTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.BankAdjustment],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.ManualCreditReconTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.BankAdjustment],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.DisputeChargeBackTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.DisputeBankRefundTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.DisputeBankFraudRefundTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.ProvisionalCreditTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.ProvisionalCreditReversalTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.OperationalLossTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Adjustment Transaction"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.OPSTransactionSubType
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
		},
		constants.SpendCardPresentTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Payment"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardDebitTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardPresentTransactionType
				return txnDetails
			},
		},
		constants.SpendCardATMReversalTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Reversal"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getSpendCardReversalDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardATMReversalTransactionType
				return txnDetails
			},
		},
		constants.SpendCardNotPresentReversalTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Reversal"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getSpendCardReversalDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardNotPresentReversalTransactionType
				return txnDetails
			},
		},
		constants.SpendCardPresentReversalTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Reversal"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getSpendCardReversalDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardPresentReversalTransactionType
				return txnDetails
			},
		},
		constants.SpendCardNotPresentTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Payment"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardDebitTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardNotPresentTransactionType
				return txnDetails
			},
		},
		constants.SpendCardForceTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: "TODO FORCE TRANSACTION DEBIT"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardForceTransactionType
				return txnDetails
			},
		},
		constants.SpendCardForceTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "TODO FORCE TRANSACTION CREDIT"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardForceTransactionType
				return txnDetails
			},
		},
		constants.SpendCardAtmTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: "ATM Withdrawal"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardDebitTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardAtmTransactionType
				return txnDetails
			},
		},
		constants.ATMCashWithdrawalRefundTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "ATM Withdrawal Refund"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardCreditTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.ATMCashWithdrawalRefundTransactionType
				return txnDetails
			},
		},
		constants.SpendCardPresentTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Refund Payment"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardCreditTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendCardPresentTransactionType
				return txnDetails
			},
		},
		constants.MoneySendTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Debit Card Payment"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardCreditTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.MoneySendTransactionType
				return txnDetails
			},
		},
		constants.DomesticAtmFeeWaiverTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "ATM Withdrawal Fee"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardCreditTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.DomesticAtmFeeWaiverTransactionType
				return txnDetails
			},
		},
		constants.DomesticAtmFeeTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: "ATM Withdrawal Fee"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardDebitTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.DomesticAtmFeeTransactionType
				return txnDetails
			},
		},
		constants.NewCardIssuanceFeeTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				scenario := constants.CardBankInitiatedTransactionSubtype
				// issuance fee is auth and settlement, if its auth considered processing, release considered cancelled and not accepted is rejected
				if txnData.TmTransactionType == constants.OutboundAuthorisation || txnData.TmTransactionType == constants.Release ||
					txnData.BatchStatus != constants.BatchStatusAccepted {
					scenario = constants.CardBankInitiatedNotCompletedScenarioKey
				}

				txnDetails[txnScenarioKey] = scenario
				return txnDetails
			},
		},
		constants.NewCardIssuanceFeeWaiverTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				// waiver is using direct transfer, its unlikely to fail due to upstream issue.
				txnDetails[txnScenarioKey] = constants.CardBankInitiatedTransactionSubtype
				return txnDetails
			},
		},
		// revisit this when product started to pick up this txn type
		constants.CardReplacementFeeTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.CardBankInitiatedTransactionSubtype
				return txnDetails
			},
		},
		// revisit this when product started to pick up this txn type
		constants.CardReplacementFeeWaiverTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.CardBankInitiatedTransactionSubtype
				return txnDetails
			},
		},
		// revisit this when product started to pick up this txn type
		constants.CardAnnualFeeTransactionTypeDebitScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.CardBankInitiatedTransactionSubtype
				return txnDetails
			},
		},
		// revisit this when product started to pick up this txn type
		constants.CardAnnualFeeWaiverTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getCardMaintenanceFeeTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.CardBankInitiatedTransactionSubtype
				return txnDetails
			},
		},
		constants.SpendRefundTransactionTypeCreditScenarioKey: {
			Category:        &api.Category{Name: "Refund Payment"},
			Icon:            IconURLMap[constants.DebitCardDomain],
			DescriptionFunc: getDBMYCardCreditTransactionDescription,
			CardDetailsFunc: func(txnData *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendRefundTransactionType
				return txnDetails
			},
		},
		constants.LendingDrawdownScenarioKey: {
			Category:        &api.Category{Name: "Drawdown"},
			Icon:            IconURLMap[constants.DrawdownTransactionType],
			DescriptionFunc: getDBMYLoanTransactionDescription,
		},
		constants.LendingRepaymentScenarioKey: {
			Category:        &api.Category{Name: "Repayment"},
			Icon:            IconURLMap[constants.RepaymentTransactionType],
			DescriptionFunc: getDBMYLoanTransactionDescription,
		},
		constants.LendingWriteOffScenarioKey: {
			Category:        &api.Category{Name: "WriteOff"},
			Icon:            IconURLMap[constants.RepaymentTransactionType],
			DescriptionFunc: getDBMYLoanTransactionDescription,
		},
		constants.CASALendingDrawdownScenarioKey: {
			Category:        &api.Category{Name: "Drawdown"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getDBMYLoanTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.DrawdownTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.CASALendingRepaymentScenarioKey: {
			Category:        &api.Category{Name: "Repayment"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYLoanTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.RepaymentTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.CASALendingWriteOffScenarioKey: {
			Category:        &api.Category{Name: "WriteOff"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYLoanTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.WriteOffTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.BizLendingDrawdownScenarioKey: {
			Category:        &api.Category{Name: "Drawdown"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getBizLendingTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.DrawdownTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.BizLendingRepaymentScenarioKey: {
			Category:        &api.Category{Name: "Repayment"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getBizLendingTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.RepaymentTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.BizLendingWriteOffScenarioKey: {
			Category:        &api.Category{Name: "WriteOff"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getBizLendingTransactionDescription,
			LoanDetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentDetail.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.WriteOffTransactionType
				txnDetails[serviceTypeKey] = paymentMetadata.ServiceType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.InsurPremiumTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Insurance"},
			Icon:            IconURLMap[constants.InsuranceDomain],
			DescriptionFunc: getInsuranceTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.InsurPremiumTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.Remarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.InsurPremiumReversalTransactionTypeScenarioKey: {
			Category:        &api.Category{Name: "Insurance"},
			Icon:            IconURLMap[constants.InsuranceDomain],
			DescriptionFunc: getInsuranceTransactionDescription,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				paymentMetadata := paymentData.GetPaymentMetadata(ctx)
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.InsurPremiumReversalTxType
				txnDetails[recipientReferenceKey] = paymentMetadata.Remarks
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.MooMooCashOutScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getMooMooDesc,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.CashOutTxType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.MooMooSpendMoneyScenarioKey: {
			Category:        &api.Category{Name: "Transfer Out"},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getMooMooDesc,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendMoneyTxType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.MooMooSpendMoneyReversalScenarioKey: {
			Category:        &api.Category{Name: "Transfer In"},
			Icon:            IconURLMap[constants.TransferIn],
			DescriptionFunc: getMooMooDesc,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.SpendMoneyRevTxType
				return txnDetails
			},
			HasReceipt: false,
		},
		constants.UnclaimedMoniesSuspenseScenarioKey: {
			Category:        &api.Category{Name: ""},
			Icon:            IconURLMap[constants.TransferOut],
			DescriptionFunc: getDBMYOtherTransactionDetail,
			DetailsFunc: func(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) map[string]string {
				txnDetails := make(map[string]string)
				txnDetails[txnScenarioKey] = constants.UnclaimedMoniesSuspenseScenarioKey
				txnDetails[recipientReferenceKey] = txnData.BatchRemarks
				return txnDetails
			},
			HasReceipt: false,
		},
		"default": {
			Category:        &api.Category{},
			Icon:            IconURLMap["DefaultTransaction"],
			DescriptionFunc: getDefaultTransactionDetail,
		},
	}
	// We will enable this when there is significant difference between retail and biz deposit
	// TransactionResponseHelperFuncs[constants.BizFundInScenarioKey] = TransactionResponseHelperFuncs[constants.FundInScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizFundInReversalScenarioKey] = TransactionResponseHelperFuncs[constants.FundInReversalScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizSendMoneyScenarioKey] = TransactionResponseHelperFuncs[constants.SendMoneyDebitScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizSendMoneyReversalScenarioKey] = TransactionResponseHelperFuncs[constants.SendMoneyReversalCreditScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizReceiveMoneyScenarioKey] = TransactionResponseHelperFuncs[constants.ReceiveMoneyCreditScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizReceiveMoneyReversalScenarioKey] = TransactionResponseHelperFuncs[constants.ReceiveMoneyReversalDebitScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizTransferMoneyDebitScenarioKey] = TransactionResponseHelperFuncs[constants.TransferIntrabankDebitScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizTransferMoneyCreditScenarioKey] = TransactionResponseHelperFuncs[constants.TransferIntrabankCreditScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizTransferMoneyReversalDebitScenarioKey] = TransactionResponseHelperFuncs[constants.TransferMoneyReversalIntrabankDebitScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizTransferMoneyReversalCreditScenarioKey] = TransactionResponseHelperFuncs[constants.TransferMoneyReversalIntrabankCreditScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizInterestPayoutScenarioKey] = TransactionResponseHelperFuncs[constants.InterestPayoutScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizApplyEarmarkScenarioKey] = TransactionResponseHelperFuncs[constants.ApplyEarmarkScenarioKey]
	// TransactionResponseHelperFuncs[constants.BizReleaseEarmarkScenarioKey] = TransactionResponseHelperFuncs[constants.ReleaseEarmarkScenarioKey]
}

func getMooMooDesc(ctx context.Context, dbData interface{}, displayName string) (string, error) {
	var desc string
	paymentDetail, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error in casting paymentDetails data for MooMoo txn")
		return "", errors.New("error getting transaction description")
	}
	switch paymentDetail.TransactionType {
	case constants.SpendMoneyTxType:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.DBMYTransfer), getPaymentDirection(paymentDetail.Amount), displayName)
	case constants.SpendMoneyRevTxType:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.Refund), getPaymentDirection(paymentDetail.Amount), displayName)
	case constants.CashOutTxType:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.WithdrawalDesc), getPaymentDirection(paymentDetail.Amount), displayName)
	}
	slog.FromContext(ctx).Info(constants.GetTransactionDetailLogTag, "DBMY MooMoo transaction description is completed")
	return desc, nil
}

// nolint: dupl
// dbData: could be *storage.PaymentDetail or *storage.TransactionsData depends on which txn type
func getDBMYIntraBankTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for Intrabank transaction")
		return "", errors.New("error getting transaction description")
	}
	if paymentData.IsQrTxn(ctx) {
		transferTerm = localise.Translate(constants.QrTransfer)
	} else {
		transferTerm = localise.Translate(constants.DBMYTransfer)
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "DBMY Intrabank Transaction description is completed")
	return fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty), nil
}

// nolint: dupl
func getTransferMoneyRevTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for Transfer Money Reversal transaction")
		return "", errors.New("error getting transaction description")
	}
	if paymentData.IsQrTxn(ctx) {
		transferTerm = localise.Translate(constants.QrTransferReversal)
	} else {
		transferTerm = localise.Translate(constants.DBMYTransferRev)
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Transfer Reversal Transaction description is completed")
	return fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty), nil
}

func getDBMYPocketTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	txnData, ok := dbData.(*storage.TransactionsData)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for DBMY Pocket transaction")
		return "", errors.New("error getting transaction description")
	}
	switch {
	case txnData.TransactionSubtype == constants.PocketFundingTransactionSubType && txnData.DebitOrCredit == constants.DEBIT:
		desc = fmt.Sprintf("%s %s %s Pocket", localise.Translate(constants.DBMYTransfer), getTransactionDirection(txnData.DebitOrCredit), counterParty)
	case txnData.TransactionSubtype == constants.PocketFundingTransactionSubType && txnData.DebitOrCredit == constants.CREDIT:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.MoneyAdded), getTransactionDirection(txnData.DebitOrCredit), counterParty)
	case txnData.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && txnData.DebitOrCredit == constants.DEBIT:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.MoneyWithdrawn), getTransactionDirection(txnData.DebitOrCredit), counterParty)
	case txnData.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && txnData.DebitOrCredit == constants.CREDIT:
		desc = fmt.Sprintf("%s %s %s Pocket", localise.Translate(constants.WithdrawalDesc), getTransactionDirection(txnData.DebitOrCredit), counterParty)
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "DBMY Pocket Transaction description is completed")
	return desc, nil
}

// nolint: dupl
func getSendMoneyRevTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for Send Money Reversal transaction")
		return "", errors.New("error getting transaction description")
	}
	if paymentData.IsQrTxn(ctx) {
		transferTerm = localise.Translate(constants.QrTransferReversal)
	} else {
		transferTerm = localise.Translate(constants.RPPNormalTransferRev)
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Send Money Reversal Transaction description is completed")
	return fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty), nil
}

func getQrTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for QR payment transaction")
		return "", errors.New("error getting transaction description")
	}
	switch paymentData.TransactionType {
	case constants.QrPaymentReversalTxType:
		transferTerm = localise.Translate(constants.QrPaymentReversal)
	default:
		transferTerm = localise.Translate(constants.QrPayment)
	}
	desc := fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Debug(constants.GetTransactionDetailLogTag, "QR payment transaction description is completed")
	return desc, nil
}

// nolint: dupl
func getReceiveMoneyRevTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for Receive Money Reversal transaction")
		return "", errors.New("error getting transaction description")
	}
	if paymentData.IsQrTxn(ctx) {
		transferTerm = localise.Translate(constants.QrTransferReversal)
	} else {
		transferTerm = localise.Translate(constants.RPPNormalTransferRev)
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Receive Money Reversal Transaction description is completed")
	return fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty), nil
}

func getDBMYOtherTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	txnData, ok := dbData.(*storage.TransactionsData)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for DBMY Ops/Interest transaction")
		return "", errors.New("error getting transaction description")
	}
	switch txnData.TransactionType {
	case constants.Adjustment, constants.ManualDebitReconTransactionType, constants.ManualCreditReconTransactionType:
		desc = fmt.Sprintf("%s %s", counterParty, "applied")
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "DBMY Ops Transaction description is completed")
	case constants.DisputeBankRefundTransactionType, constants.DisputeBankFraudRefundTransactionType, constants.DisputeChargeBackTransactionType, constants.OperationalLossTransactionType:
		desc = localise.Translate(constants.BankRefund)
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "DBMY CardOps Transaction description is completed")
	case constants.ProvisionalCreditTransactionType:
		desc = localise.Translate(constants.ProvisionalCredit)
	case constants.ProvisionalCreditReversalTransactionType:
		desc = localise.Translate(constants.ProvisionalCreditReversal)
	case constants.ApplyEarmarkTransactionType, constants.ReleaseEarmarkTransactionType:
		desc = localise.Translate(constants.Earmark)
		slog.FromContext(ctx).Info(constants.GetTransactionDetailLogTag, "DBMY Earmark description is completed")
	case constants.UnclaimedMoniesTransactionType:
		desc = localise.Translate(constants.DBMYTransfer) + " " + localise.Translate(constants.To) + " " +
			localise.Translate(constants.UnclaimedMonies)
		slog.FromContext(ctx).Info(constants.GetTransactionDetailLogTag, "DBMY Unclaimed Monies description is completed")
	}
	return desc, nil
}

func getGrabTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error in casting paymentDetails data for Grab txn")
		return "", errors.New("error getting transaction description")
	}
	switch paymentData.TransactionType {
	case constants.SpendMoneyTxType:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.Payment), getPaymentDirection(paymentData.Amount), counterParty)
	case constants.SpendMoneyRevTxType:
		desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.Refund), getPaymentDirection(paymentData.Amount), counterParty)
	}
	slog.FromContext(ctx).Info(constants.GetTransactionDetailLogTag, "DBMY Grab transaction description is completed")
	return desc, nil
}

func getDBMYRewardsTransactionDescription(_ context.Context, _ interface{}, displayName string) (string, error) {
	return displayName, nil
}

func getInterestDescription(ctx context.Context, dbData interface{}, _ string) (string, error) {
	txnData := dbData.(*storage.TransactionsData)
	desc := txnData.GetInterestCampaignName(ctx)
	icon := emoji.PartyPopper
	switch desc {
	case "":
		desc = localise.Translate(constants.InterestEarned)
		switch config.GetTenant() {
		case tenants.TenantMY:
			desc = localise.Translate(constants.DBMYInterestEarned)
		}
	case "CNY interest earned":
		icon = emoji.RedEnvelope
	}
	return fmt.Sprintf("%s %v", desc, icon), nil
}

func getBonusInterestDescription(_ context.Context, _ any, _ string) (string, error) {
	icon := emoji.PartyPopper
	desc := ""

	switch config.GetTenant() {
	case tenants.TenantMY:
		desc = localise.Translate(constants.DBMYBonusInterestEarned)
	default:
		desc = localise.Translate(constants.BonusInterestEarned)
	}

	return fmt.Sprintf("%s %v", desc, icon), nil
}

// nolint: dupl
func getDBMYCardDebitTransactionDescription(_ context.Context, dbData interface{}, displayName string) (string, error) {
	var desc, txnType string
	switch val := dbData.(type) {
	case *storage.TransactionsData:
		txnType = val.TransactionType
	case *storage.CardTransactionDetail:
		txnType = val.TransactionType
	default:
		return "", errors.New("invalid data type")
	}
	switch txnType {
	case constants.SpendCardPresentTransactionType, constants.SpendCardNotPresentTransactionType:
		desc = fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardPaymentTo), displayName)
	case constants.SpendCardAtmTransactionType:
		desc = localise.Translate(constants.ATMWithdrawal) // TODO: add in the logic to handle overseas atm withdrawal
	case constants.DomesticAtmFeeTransactionType:
		desc = localise.Translate(constants.DebitCardAtmFeeCharged)
	}
	return desc, nil
}

func getCardMaintenanceFeeTransactionDescription(ctx context.Context, dbData interface{}, _ string) (string, error) {
	var desc, txnType string
	switch val := dbData.(type) {
	case *storage.TransactionsData:
		txnType = val.TransactionType
	case *storage.PaymentDetail:
		txnType = val.TransactionType
	default:
		return "", errors.New("invalid data type")
	}
	switch txnType {
	case constants.NewCardIssuanceFeeTransactionType:
		desc = localise.Translate(constants.NewCardIssuanceFee)
	case constants.NewCardIssuanceFeeWaiverTransactionType:
		desc = localise.Translate(constants.NewCardIssuanceFeeWaived)
	}

	return desc, nil
}

// nolint: dupl
func getDBMYCardCreditTransactionDescription(_ context.Context, dbData interface{}, displayName string) (string, error) {
	var desc, txnType string
	switch val := dbData.(type) {
	case *storage.TransactionsData:
		txnType = val.TransactionType
	case *storage.CardTransactionDetail:
		txnType = val.TransactionType
	default:
		return "", errors.New("invalid data type")
	}
	switch txnType {
	case constants.SpendRefundTransactionType:
		desc = fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardRefundPaymentFrom), displayName)
	case constants.MoneySendTransactionType:
		desc = fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardMoneySendReceive), displayName)
	case constants.ATMCashWithdrawalRefundTransactionType:
		desc = localise.Translate(constants.DebitCardAtmWithdrawalRefund)
	}
	return desc, nil
}

// nolint: dupl
func getDBMYLoanTransactionDescription(_ context.Context, dbData interface{}, _ string) (string, error) {
	var desc string
	txnData := dbData.(*storage.TransactionsData)
	switch txnData.TransactionType {
	case constants.DrawdownTransactionType:
		desc = constants.FlexiCreditDisplayName
	case constants.RepaymentTransactionType, constants.WriteOffTransactionType:
		desc = constants.FlexiCreditRepaymentDisplayName
	}
	return desc, nil
}

// nolint: dupl
func getBizLendingTransactionDescription(_ context.Context, dbData interface{}, _ string) (string, error) {
	var desc string
	txnData := dbData.(*storage.TransactionsData)
	switch txnData.TransactionType {
	case constants.DrawdownTransactionType:
		desc = constants.BizFlexiLoanDisplayName
	case constants.RepaymentTransactionType, constants.WriteOffTransactionType:
		desc = constants.BizFlexiLoanRepaymentDisplayName
	}
	return desc, nil
}

func getSpendCardReversalDescription(ctx context.Context, dbData interface{}, displayName string) (string, error) {
	return displayName, nil
}

// nolint: dupl
func getInsuranceTransactionDescription(_ context.Context, _ interface{}, displayName string) (string, error) {
	return displayName, nil
}

// nolint: dupl
func getFundInRevTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for Fund In Reversal transaction")
		return "", errors.New("error getting transaction description")
	}
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.RPPNormalTransferRev), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Fund In Reversal Transaction description is completed")
	return desc, nil
}

// getDefaultTransactionDetail is a dummy method to return empty txn detail for unknown txn type
// this is to avoid PANIC error occurred for any unknown txn type that newly added
func getDefaultTransactionDetail(ctx context.Context, _ interface{}, _ string) (string, error) {
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Unknown transaction description is completed.")
	return "", nil
}

func getRPPProxyType(counterPartyAccount *dto.AccountDetail) string {
	var proxyType string
	switch counterPartyAccount.Proxy.Type {
	case constants.MobileNumberRail:
		proxyType = "Mobile number"
	case constants.NRICRail:
		proxyType = "MyKad"
	case constants.ArmyIDRail:
		proxyType = "Army/Police ID"
	case constants.PassportNumberRail:
		proxyType = "Passport"
	case constants.BusinessRegistrationNumberRail:
		proxyType = "Business Registration"
	default:
		proxyType = "Account number"
	}
	return proxyType
}

func getPaymentDirection(amount int64) string {
	var txnDirection string
	if amount < 0 {
		txnDirection = localise.Translate(constants.OUT)
	} else if amount > 0 {
		txnDirection = localise.Translate(constants.IN)
	}
	return txnDirection
}

func getTransactionDirection(debitOrCredit string) string {
	if debitOrCredit == constants.CREDIT {
		return localise.Translate(constants.IN)
	}
	return localise.Translate(constants.OUT)
}
