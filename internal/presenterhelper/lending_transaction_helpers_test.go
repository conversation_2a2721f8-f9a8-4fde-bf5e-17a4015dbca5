package presenterhelper

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func TestFilterInvalidTransactions(t *testing.T) {
	scenarios := []struct {
		desc            string
		transactionData []*storage.TransactionsData
		expected        storage.TransactionsData
	}{
		{
			desc: "Return RepaymentTransactionType",
			transactionData: []*storage.TransactionsData{
				{
					TransactionType: constants.RepaymentTransactionType,
					AccountAddress:  constants.RepaymentMadeAccountAddress,
					DebitOrCredit:   constants.CREDIT,
					AccountPhase:    constants.PostingPhaseCommitted,
				},
				{
					TransactionType:   constants.RepaymentTransactionType,
					TmTransactionType: constants.PostingPhasePendingOutgoing,
				},
				{
					TransactionType:   constants.RepaymentTransactionType,
					TmTransactionType: constants.Release,
				},
				{
					TransactionType:   constants.RepaymentTransactionType,
					TmTransactionType: constants.OutboundAuthorisation,
				},
			},
			expected: storage.TransactionsData{
				TransactionType:   constants.RepaymentTransactionType,
				TmTransactionType: constants.RepaymentMadeAccountAddress,
				DebitOrCredit:     constants.CREDIT,
				AccountPhase:      constants.PostingPhaseCommitted,
			},
		},
		{
			desc: "Return DrawdownTransactionType",
			transactionData: []*storage.TransactionsData{
				{
					TransactionType:   constants.DrawdownTransactionType,
					TmTransactionType: constants.CREDIT,
				},
				{
					TransactionType:   constants.DrawdownTransactionType,
					TmTransactionType: constants.PostingPhasePendingOutgoing,
				},
				{
					TransactionType:   constants.DrawdownTransactionType,
					TmTransactionType: constants.Release,
				},
				{
					TransactionType:   constants.DrawdownTransactionType,
					TmTransactionType: constants.PostingPhaseCommitted,
				},
			},
			expected: storage.TransactionsData{
				TransactionType:   constants.DrawdownTransactionType,
				TmTransactionType: constants.Release,
			},
		},
	}

	for _, scn := range scenarios {
		scenario := scn
		t.Run(scenario.desc, func(t *testing.T) {
			transactionData := FilterInvalidTransactions(scenario.transactionData)
			assert.Equal(t, scenario.expected.TransactionType, transactionData.TransactionType)
		})
	}
}

func TestSortTransactions(t *testing.T) {
	scenarios := []struct {
		desc            string
		transactionData []*storage.TransactionsData
		expected        []*storage.TransactionsData
	}{
		{
			desc: "Sort data correctly",
			transactionData: []*storage.TransactionsData{
				{
					TmTransactionType: constants.Release,
				},
				{
					TmTransactionType: constants.Transfer,
				},
				{
					TmTransactionType: constants.OutboundAuthorisation,
				},
				{
					TmTransactionType: constants.Settlement,
				},
			},
			expected: []*storage.TransactionsData{
				{
					TmTransactionType: constants.Transfer,
				},
				{
					TmTransactionType: constants.OutboundAuthorisation,
				},
				{
					TmTransactionType: constants.Release,
				},
				{
					TmTransactionType: constants.Settlement,
				},
			},
		},
	}

	for _, scn := range scenarios {
		scenario := scn
		t.Run(scenario.desc, func(t *testing.T) {
			SortTransactions(scenario.transactionData)
			assert.Equal(t, scenario.expected[0].TransactionType, scenario.transactionData[0].TransactionType)
		})
	}
}

func TestGetPaymentTransactionID(t *testing.T) {
	scenarios := []struct {
		desc            string
		transactionData *storage.TransactionsData
		loanDetail      *storage.LoanDetail
	}{
		{
			desc: "Happy path - PaymentTransactionID not empty",
			transactionData: &storage.TransactionsData{
				ClientBatchID: "11111",
			},
			loanDetail: &storage.LoanDetail{
				PaymentTransactionID: "1111",
			},
		},
		{
			desc: "Sad path - PaymentTransactionID empty",
			transactionData: &storage.TransactionsData{
				ClientBatchID: "11111",
			},
			loanDetail: &storage.LoanDetail{
				PaymentTransactionID: "",
			},
		},
	}

	for _, scn := range scenarios {
		scenario := scn
		t.Run(scenario.desc, func(t *testing.T) {
			paymentTransactionID := GetPaymentTransactionID(scenario.transactionData, scenario.loanDetail)
			assert.NotEmpty(t, paymentTransactionID)
		})
	}
}

func TestGetLoanTransactionDisplayNameForOpsSearch(t *testing.T) {
	scenarios := []struct {
		desc                  string
		transaction           *storage.TransactionsData
		transactionLoanDetail *storage.LoanDetail
		expected              string
	}{
		{
			desc:        "returns-loan-name-when-present",
			transaction: &storage.TransactionsData{},
			transactionLoanDetail: &storage.LoanDetail{
				TransactionType: constants.RepaymentTransactionType,
				RepaymentDetail: []byte(`{"Currency": "MYR", "LoanRepaymentDetail": [{"LoanName": "Loan A"}]}`),
			},
			expected: "Loan A",
		},
		{
			desc: "returns-listing-displayname-when-loan-name-absent",
			transaction: &storage.TransactionsData{
				TransactionType: constants.DrawdownTransactionType,
			},
			transactionLoanDetail: &storage.LoanDetail{
				RepaymentDetail: []byte(`{"Currency": "MYR", "TotalInterestSaved": "0", "LoanRepaymentDetail": null, "IsTotalInterestSaved": false, "PaymentTransactionID": "", "PenalInterestCharged": "0", "TotalRepaymentAmount": "210.6", "IsTotalPenalInterestCharged": false}`),
			},
			expected: constants.FlexiCreditDisplayName,
		},
		{
			desc: "returns-listing-displayname-for-biz-lending-when-loan-name-absent",
			transaction: &storage.TransactionsData{
				TransactionType:   constants.DrawdownTransactionType,
				TransactionDomain: constants.BizLendingDomain,
			},
			transactionLoanDetail: &storage.LoanDetail{},
			expected:              constants.BizFlexiLoanDisplayName,
		},
	}

	for _, scn := range scenarios {
		scenario := scn
		t.Run(scenario.desc, func(t *testing.T) {
			result := GetLoanTransactionDisplayNameForOpsSearch(context.Background(), scenario.transaction, scenario.transactionLoanDetail)
			assert.Equal(t, scenario.expected, result)
		})
	}
}
