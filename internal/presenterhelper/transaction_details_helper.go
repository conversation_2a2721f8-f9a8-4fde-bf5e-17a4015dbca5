package presenterhelper

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetPaymentCasaTransactionStatus contains logic to return transaction status based on DB data
func GetPaymentCasaTransactionStatus(transaction *storage.TransactionsData, transactionPaymentDetail *storage.PaymentDetail) string {
	var status string
	// For Interest Transactions
	if transaction.IsInterestPayoutTxn() || transaction.IsTaxPayoutTxn() || transaction.IsOpsTxn() || transaction.IsRewardsTxn() {
		status = formatDepositsCoreInterest(transaction)
		return status
	} else if transaction.TransactionType == constants.ApplyEarmarkTransactionType {
		if transaction.BatchStatus == constants.BatchStatusRejected {
			return constants.FailedStatus
		}
		return constants.ProcessingStatus
	} else if transaction.TransactionType == constants.ReleaseEarmarkTransactionType {
		if transaction.BatchStatus == constants.BatchStatusRejected {
			return constants.FailedStatus
		}
		return constants.CanceledStatus
	} else if transaction.TransactionType == constants.NewCardIssuanceFeeTransactionType {
		return getCardIssuanceFeeStatus(transaction)
	}

	// For other Transactions(Currently Payments)
	if transactionPaymentDetail != nil {
		status = transactionPaymentDetail.Status
		// display refunded status for spend_money_reversal
		if transactionPaymentDetail.Status == constants.CompletedStatus && transactionPaymentDetail.TransactionType == constants.SpendMoneyRevTxType {
			status = constants.RefundedStatus
		}
		if transactionPaymentDetail.Status == constants.AuthorizedStatus {
			status = constants.ProcessingStatus
		}
	} else { // For now, this will happen for manual posting only
		status = formatDepositsCoreInterest(transaction)
	}
	return status
}

// GetPaymentCasaTransactionStatusForOpsSearch contains logic to return transaction status based on DB data for Ops Search endpoint
func GetPaymentCasaTransactionStatusForOpsSearch(transaction *storage.TransactionsData, transactionPaymentDetail *storage.PaymentDetail) string {
	var status string
	// For Interest Transactions
	if transaction.IsInterestPayoutTxn() || transaction.IsTaxPayoutTxn() || transaction.IsOpsTxn() {
		status = formatDepositsCoreInterest(transaction)
		return status
	} else if transaction.TransactionType == constants.ApplyEarmarkTransactionType {
		if transaction.BatchStatus == constants.BatchStatusRejected {
			return constants.FailedStatus
		}
		return constants.ProcessingStatus
	} else if transaction.TransactionType == constants.ReleaseEarmarkTransactionType {
		if transaction.BatchStatus == constants.BatchStatusRejected {
			return constants.FailedStatus
		}
		return constants.CanceledStatus
	} else if transaction.TransactionType == constants.NewCardIssuanceFeeTransactionType {
		return getCardIssuanceFeeStatus(transaction)
	}

	// For other Transactions(Currently Payments)
	if transactionPaymentDetail != nil {
		status = transactionPaymentDetail.Status
		if transactionPaymentDetail.Status == constants.AuthorizedStatus {
			status = constants.ProcessingStatus
		}
	} else { // For now, this will happen for manual posting only
		status = formatDepositsCoreInterest(transaction)
	}
	return status
}

func getCardIssuanceFeeStatus(transaction *storage.TransactionsData) string {
	// successful auth means the fee charge is processing, else it is failed
	if transaction.TmTransactionType == constants.OutboundAuthorisation {
		return lo.Ternary(transaction.BatchStatus == constants.BatchStatusAccepted, constants.ProcessingStatus, constants.FailedStatus)
	}
	// successful release means the fee charge has been cancelled, else it is failed
	if transaction.TmTransactionType == constants.Release {
		return lo.Ternary(transaction.BatchStatus == constants.BatchStatusAccepted, constants.CanceledStatus, constants.FailedStatus)
	}
	// this should settlement and follow the usual status mapping
	return formatDepositsCoreInterest(transaction)
}

// formatDepositsCoreInterest returns formatted status based on TM BatchStatus field.
func formatDepositsCoreInterest(transaction *storage.TransactionsData) string {
	var status string
	if transaction.BatchStatus == constants.BatchStatusRejected {
		status = constants.FailedStatus
	} else if transaction.BatchStatus == constants.BatchStatusAccepted {
		status = constants.CompletedStatus
	}
	return status
}

// GetPaymentCasaStatusDescription Get the transaction status description for payment or casa txn
func GetPaymentCasaStatusDescription(ctx context.Context, transaction *storage.TransactionsData, transactionPaymentDetail *storage.PaymentDetail) string {
	statusDesc := ""
	if transactionPaymentDetail != nil {
		statusDetails := transactionPaymentDetail.GetStatusDetails(ctx)
		if statusDetails != nil {
			if statusDetails.Description != "" {
				statusDesc = statusDetails.Description
			} else {
				statusDesc = statusDetails.Reason
			}
		}
	} else { // For now, this will happen for manual posting only
		statusDesc = transaction.BatchErrorMessage
	}
	return statusDesc
}

// GetCounterPartyDisplayNamePocketView ...
func GetCounterPartyDisplayNamePocketView(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail, isChildAccount bool, pocketIDToNameMap map[string]string) string {
	var counterPartyDisplayName string
	var batchDetails map[string]string

	err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
	}
	// For DBMY Pocket View
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, transaction.TransactionType) {
		counterPartyDisplayName = GetCounterPartyDisplayNameForInterestTxn(ctx, transaction)
	}

	switch config.GetTenant() {
	case tenants.TenantMY:
		if utils.SearchStringArray(constants.OpsInitiateTransactionType, transaction.TransactionType) {
			counterPartyDisplayName = getCounterPartyNameForOpsTransactions(isChildAccount)
		} else if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyDisplayName = getCounterPartyDisplayNameForPocketDBMY(transaction, batchDetails, isChildAccount, pocketIDToNameMap)
		} else if utils.SearchStringArray(constants.MooMooTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyDisplayName = GetCounterPartyDisplayNameForMooMooTxn(transaction.TransactionType)
		} else if utils.SearchStringArray(constants.SpendMoneyTransactionTypes, transaction.TransactionType) {
			counterPartyDisplayName = GetCounterPartyDisplayNameForGrabTxn(ctx, paymentDetail)
		} else if utils.SearchStringArray(constants.CardOpsTransactionTypes, transaction.TransactionType) {
			counterPartyDisplayName = getCounterPartyNameForCardOpsTransactions(transaction.TransactionType)
		} else if utils.SearchStringArray(constants.RewardsTransactions, transaction.TransactionType) {
			counterPartyDisplayName = GetCounterPartyDisplayNameForRewardsTxn(ctx, transaction)
		} else if utils.SearchStringArray(constants.LendingTransactionTypes, transaction.TransactionType) {
			return getLendingCounterPartyDisplayName(ctx, transaction)
		} else if transaction.TransactionType == constants.UnclaimedMoniesTransactionType {
			counterPartyDisplayName = localise.Translate(constants.UnclaimedMonies)
		} else if transaction.IsCardMaintenanceFeeTxn() {
			counterPartyDisplayName = getCounterPartyDisplayNameForCardMaintenanceFee(transaction.TransactionType)
		} else if transaction.IsInsuranceTxn() {
			counterPartyDisplayName = GetCounterPartyDisplayNameForInsuranceTxn(ctx, paymentDetail)
		}
	}
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}
	return getCounterPartyDisplayName(ctx, transaction, paymentDetail)
}

// GetCounterPartyDisplayNameForMooMooTxn ...
func GetCounterPartyDisplayNameForMooMooTxn(transactionType string) string {
	if utils.SearchStringArray(constants.MooMooTransactionTypes, transactionType) {
		return "Moomoo"
	}
	return ""
}

func getLendingCounterPartyDisplayName(ctx context.Context, transaction *storage.TransactionsData) string {
	if transaction.TransactionType == constants.DrawdownTransactionType ||
		(transaction.TransactionType == constants.RepaymentTransactionType && transaction.TmTransactionType == constants.Transfer &&
			transaction.TransactionSubtype == constants.IntraBank && transaction.BatchDetails != nil) {
		return GetCounterPartyDisplayNameForLendingTxn(ctx, transaction)
	}

	return ""
}

// GetCounterPartyDisplayNamePocketViewForTxnDetail handle get transaction details specific logic for DBMY
func GetCounterPartyDisplayNamePocketViewForTxnDetail(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail, isChildAccount bool, pocketIDToNameMap map[string]string) string {
	displayName := GetCounterPartyDisplayNamePocketView(ctx, transaction, paymentDetail, isChildAccount, pocketIDToNameMap)
	tenant := config.GetTenant()
	if tenants.TenantMY == tenant && utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
		if isChildAccount {
			displayName = localise.Translate(constants.DBMYBankPocket)
		}
	}
	return displayName
}

func getCounterPartyNameForOpsTransactions(isChildAccount bool) string {
	var counterPartyDisplayName string
	if isChildAccount {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	} else {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	}
	return counterPartyDisplayName
}

func getCounterPartyNameForCardOpsTransactions(txnType string) string {
	var counterPartyDisplayName string
	switch txnType {
	case constants.ManualDebitReconTransactionType, constants.ManualCreditReconTransactionType:
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	case constants.DisputeChargeBackTransactionType, constants.DisputeBankRefundTransactionType, constants.DisputeBankFraudRefundTransactionType, constants.OperationalLossTransactionType:
		counterPartyDisplayName = localise.Translate(constants.BankRefund)
	case constants.ProvisionalCreditTransactionType:
		counterPartyDisplayName = localise.Translate(constants.ProvisionalCredit)
	case constants.ProvisionalCreditReversalTransactionType:
		counterPartyDisplayName = localise.Translate(constants.ProvisionalCreditReversal)
	}
	return counterPartyDisplayName
}

func getCounterPartyDisplayNameForCardMaintenanceFee(transactionType string) string {
	var counterPartyDisplayName string
	switch transactionType {
	case constants.NewCardIssuanceFeeTransactionType:
		counterPartyDisplayName = localise.Translate(constants.NewCardIssuanceFee)
	case constants.NewCardIssuanceFeeWaiverTransactionType:
		counterPartyDisplayName = localise.Translate(constants.NewCardIssuanceFeeWaived)
	}
	return counterPartyDisplayName
}

func getCounterPartyDisplayNameForPocketDBMY(transaction *storage.TransactionsData, batchDetails map[string]string, isChildAccount bool, pocketIDToNameMap map[string]string) string {
	// Boost Pocket
	if batchDetails["sub_account_type"] == constants.BoostPocket {
		return getCounterPartyDisplayNameForBoostPocketDBMY(transaction, batchDetails, isChildAccount, pocketIDToNameMap)
	}

	// Savings Pocket
	return getCounterPartyDisplayNameForSavingsPocketDBMY(transaction, batchDetails, isChildAccount)
}

func getCounterPartyDisplayNameForSavingsPocketDBMY(transaction *storage.TransactionsData, batchDetails map[string]string, isChildAccount bool) string {
	var counterPartyDisplayName string
	if isChildAccount {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = localise.Translate(constants.MoneyAdded)
		} else {
			counterPartyDisplayName = localise.Translate(constants.MoneyWithdrawn)
		}
	} else {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		} else {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		}
	}
	return counterPartyDisplayName
}

func getCounterPartyDisplayNameForBoostPocketDBMY(transaction *storage.TransactionsData, batchDetails map[string]string, isChildAccount bool, pocketIDToNameMap map[string]string) string {
	if isChildAccount {
		if transaction.DebitOrCredit == constants.CREDIT {
			return localise.Translate(constants.MoneyAdded)
		} else {
			return localise.Translate(constants.MoneyWithdrawn)
		}
	}

	// from main account perspective
	pocketID, ok := batchDetails["pocket_id"]
	if !ok {
		return "Some Boost" // fallback if pocketName is not found
	}

	pocketName, ok := pocketIDToNameMap[pocketID]
	if !ok {
		return "Some Boost" // fallback if pocketName is not found
	}

	return pocketName
}

// GetCounterPartyDisplayNameForGrabTxn return respective display name for each grab activity type
func GetCounterPartyDisplayNameForGrabTxn(ctx context.Context, paymentDetail *storage.PaymentDetail) string {
	// grab refund can only be identified from txn type
	if paymentDetail.TransactionType == constants.SpendMoneyRevTxType {
		return "Grab"
	}

	grabActivityType := paymentDetail.GetGrabActivityTypeFromMetadata(ctx)
	displayName, exists := constants.GrabDisplayNameMap[grabActivityType]

	if !exists {
		displayName = "Grab"
	}

	return displayName
}

// GetCounterPartyDisplayNameForInsuranceTxn return respective display name for insurance txn type
func GetCounterPartyDisplayNameForInsuranceTxn(ctx context.Context, paymentDetail *storage.PaymentDetail) string {
	// partner activity type from upstream will be the counterparty display name
	counterPartyDisplayName := paymentDetail.GetCounterpartyDisplayNameFromMetadata(ctx)
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}
	// fallback value if there is no counterPartyDisplayName being passed from upstream
	if paymentDetail.TransactionType == constants.InsurPremiumTxType {
		return "Insurance payment"
	}
	return "Insurance payment reversal"
}

// GetCounterPartyDisplayNameForRewardsTxn ...
func GetCounterPartyDisplayNameForRewardsTxn(ctx context.Context, transaction *storage.TransactionsData) string {
	counterPartyDisplayName := transaction.GetRewardsCampaignName(ctx)
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}
	return "Reward"
}

// GetCounterPartyDisplayNameForLendingTxn ...
func GetCounterPartyDisplayNameForLendingTxn(ctx context.Context, transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string

	loanTransactionID := transaction.GetLoanTransactionID(ctx)

	if loanTransactionID != "" {
		loanDetail, err := logic.FetchLoanDetailForLoanTransactionID(ctx, transaction.AccountID, loanTransactionID, constants.GetLendingTransactionDetailLogTag)

		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error while fetching write off transaction data from loan detail, err: %s", err.Error()))
			return counterPartyDisplayName
		}

		if transaction.TransactionType == constants.DrawdownTransactionType {
			var drawdownDetail dto.DrawdownDetailsDTO

			err := json.Unmarshal(loanDetail.DisbursementDetail, &drawdownDetail)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing DisbursementDetail, err: %s", err.Error()))
				return counterPartyDisplayName
			}

			counterPartyDisplayName = drawdownDetail.LoanName
		} else if transaction.TransactionType == constants.RepaymentTransactionType {
			var repaymentDetail dto.RepaymentDetailsDTO

			err := json.Unmarshal(loanDetail.RepaymentDetail, &repaymentDetail)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing RepaymentDetail, err: %s", err.Error()))
				return counterPartyDisplayName
			}

			loanRepaymentDetails := repaymentDetail.LoanRepaymentDetail

			if len(loanRepaymentDetails) > 0 {
				counterPartyDisplayName = loanRepaymentDetails[0].LoanName
			}
		}
	}

	return counterPartyDisplayName
}

// GetCounterPartyDisplayNameForInterestTxn ...
func GetCounterPartyDisplayNameForInterestTxn(ctx context.Context, transaction *storage.TransactionsData) string {
	counterPartyDisplayName := transaction.GetInterestCampaignName(ctx)
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}

	switch config.GetTenant() {
	case tenants.TenantMY:
		switch transaction.TransactionType {
		case constants.BonusInterestPayoutTransactionType:
			return localise.Translate(constants.DBMYBonusInterestEarned)
		case constants.BonusInterestPayoutReversalTransactionType:
			return localise.Translate(constants.DBMYBonusInterestEarnedReversal)
		default:
			return localise.Translate(constants.DBMYInterestEarned)
		}

	case "":
		switch transaction.TransactionType {
		case constants.BonusInterestPayoutTransactionType:
			return localise.Translate(constants.BonusInterestEarned)
		case constants.BonusInterestPayoutReversalTransactionType:
			return localise.Translate(constants.BonusInterestEarnedReversal)
		default:
			return localise.Translate(constants.InterestEarned)
		}
	}
	return ""
}

// getCounterPartyDisplayName will return the counterparty displayName based on data from payments and TM.
func getCounterPartyDisplayName(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail) string {
	var counterPartyDisplayName string
	var batchDetails map[string]string
	// For Internal Transactions
	counterPartyDisplayName = transaction.CounterPartyName()
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	} else if transaction.ClientBatchID != "" {
		err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
		} else if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			return getCounterPartyNameForPocketTransactions(transaction, batchDetails)
		}

		// For now, this will happen for manual posting only.
		if paymentDetail == nil {
			return counterPartyNameForManualPosting(transaction, batchDetails)
		} // For other Transactions(Currently Payments)
		counterPartyDisplayName = getCounterPartyAccountNameFromPaymentDetail(ctx, paymentDetail)
	}
	return counterPartyDisplayName
}

// when tenant is configured, BP flow should not reach this function
// if tenant is not configured, just show empty string lor
func getCounterPartyNameForPocketTransactions(transaction *storage.TransactionsData, batchDetails map[string]string) string {
	var counterPartyDisplayName string
	if transaction.TransactionSubtype == constants.PocketFundingTransactionSubType {
		if transaction.DebitOrCredit == constants.DEBIT {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		} else if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		}
	} else if transaction.TransactionSubtype == constants.PocketWithdrawalTransactionSubType {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		} else if transaction.DebitOrCredit == constants.DEBIT {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		}
	}
	return counterPartyDisplayName
}

// counterPartyNameForManualPosting gets displayName for manual posting and intrabank payment-ops-trf
func counterPartyNameForManualPosting(transaction *storage.TransactionsData, batchDetails map[string]string) string {
	var counterPartyDisplayName string
	if transaction.TransactionType == constants.OPSTransactionType {
		return constants.OPSDisplayName
	}
	if transaction.DebitOrCredit == constants.DEBIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
		counterPartyDisplayName = batchDetails["source_display_name"]
	} else if transaction.DebitOrCredit == constants.CREDIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
		counterPartyDisplayName = batchDetails["destination_display_name"]
	} else if transaction.DebitOrCredit == constants.DEBIT {
		counterPartyDisplayName = batchDetails["destination_display_name"]
	} else {
		counterPartyDisplayName = batchDetails["source_display_name"]
	}
	return counterPartyDisplayName
}

func getCounterPartyAccountNameFromPaymentDetail(ctx context.Context, paymentDetail *storage.PaymentDetail) string {
	var counterPartyDisplayName string
	var counterPartyAccountDetails dto.AccountDetail
	var metadata map[string]string

	if paymentDetail.TransactionSubType == constants.Grab {
		if err := json.Unmarshal(paymentDetail.Metadata, &metadata); err != nil {
			slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing Metadata, err: %s", err.Error()))
			return counterPartyDisplayName
		}
		counterPartyDisplayName = metadata["grab_activity_type"]
		return counterPartyDisplayName
	}
	err := json.Unmarshal(paymentDetail.CounterPartyAccount, &counterPartyAccountDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing CounterPartyAccount, err: %s", err.Error()))
		return counterPartyDisplayName
	}
	if counterPartyAccountDetails.DisplayName != "" {
		counterPartyDisplayName = counterPartyAccountDetails.DisplayName
	} else {
		counterPartyDisplayName = counterPartyAccountDetails.FullName
	}
	return counterPartyDisplayName
}

// IsChildAccount ...
// Deprecated, use utils.IsChildAccount instead
func IsChildAccount(accountDetail *accountService.GetAccountResponse) bool {
	if accountDetail == nil || accountDetail.Account == nil {
		return false
	}
	return accountDetail.Account.ParentAccountID != ""
}

// IsCASAAccount ...
func IsCASAAccount(accountDetail *accountService.GetAccountResponse) bool {
	if accountDetail == nil || accountDetail.Account == nil {
		return false
	}
	return accountDetail.Account.ProductVariantID == constants.DepositsAccount
}

// GetExternalTransactionID ...
func GetExternalTransactionID(ctx context.Context, transaction *storage.TransactionsData) string {
	var txnDetails map[string]string
	err := json.Unmarshal(transaction.TransactionDetails, &txnDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error parsing transactionDetails, err: %s", err.Error()))
	}
	return txnDetails["external_id"]
}

// HasReceipt ...
func HasReceipt(key string, status string) bool {
	if status == constants.FailedStatus || status == constants.ProcessingStatus {
		return false
	}
	return TransactionResponseHelperFuncs[key].HasReceipt
}
