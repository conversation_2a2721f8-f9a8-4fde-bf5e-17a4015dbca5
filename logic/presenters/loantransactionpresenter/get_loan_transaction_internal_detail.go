package loantransactionpresenter

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// GetInternalLoanTransactionDetail fetches the loan transactions details from the DB for lending domain
func GetInternalLoanTransactionDetail(ctx context.Context, transaction *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail, account *accountService.GetAccountResponse) *api.GetInternalTransactionDetailResponse {
	var (
		displayName                    string
		status                         string
		counterPartyTransactionDetails map[string]string
		transactionDescription         string
	)

	isCASAAccount := presenterhelper.IsCASAAccount(account)
	key := transaction.GetTxnScenarioKey()
	key = lo.Ternary(isCASAAccount, "CASA."+key, key)
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	if loanDetail != nil {
		status = presenterhelper.GetLoanCasaTransactionStatus(transaction, loanDetail)
		displayName = presenterhelper.GetLoanTransactionDisplayNameForListing(transaction)
		counterPartyTransactionDetails = helperFuncs.LoanDetailsFunc(ctx, transaction, loanDetail, paymentDetail)
		transactionDescription, _ = helperFuncs.DescriptionFunc(ctx, transaction, displayName)
	}

	// this could be the auth amount if the transaction_data is still in POSTING_PHASE_PENDING_OUTGOING
	amount := transaction.FormattedAmount(ctx)
	transactionTimestamp := utils.TruncateTillSecond(transaction.BatchValueTimestamp)

	return &api.GetInternalTransactionDetailResponse{
		Amount:        amount,
		Currency:      transaction.TransactionCurrency,
		TransactionID: transaction.ClientTransactionID,
		CreditOrDebit: transaction.DebitOrCredit,
		Status:        status,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp:   &transactionTimestamp,
		TransactionType:        transaction.TransactionType,
		TransactionSubtype:     transaction.TransactionSubtype,
		TransactionDescription: transactionDescription,
		AccountID:              req.AccountID,
	}
}

// GetInternalCardDetail fetches the card transactions details from the DB and returns the formatted CardTransactionDetail for card domain
func GetInternalCardDetail(ctx context.Context, transaction *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) *api.CardTransactionDetail {
	if cardDetail == nil {
		return &api.CardTransactionDetail{}
	}

	merchantName := presenterhelper.GetMerchantName(ctx, cardDetail)
	cardID := cardDetail.CardID
	tailCardNumber := fmt.Sprintf("**%s", cardDetail.TailCardNumber)
	md := cardDetail.GetMetadata(ctx)
	bankFee := presenterhelper.GetBankFee(ctx, transaction, cardDetail)
	exchangeRate := presenterhelper.GetExchangeRate(ctx, transaction, cardDetail)

	return &api.CardTransactionDetail{
		CardID:                   cardID,
		TailCardNumber:           tailCardNumber,
		SettlementDate:           &cardDetail.ValueTimestamp,
		ExchangeRate:             exchangeRate,
		BankFee:                  bankFee,
		Mcc:                      md.Mcc,
		MaskedCardNumber:         md.MaskedCardNumber,
		TransactionCategory:      md.TransactionCategory,
		TransactionSubCategory:   md.TransactionSubCategory,
		NetworkID:                md.NetworkID,
		ThreeDsValidation:        md.ThreeDsValidation,
		PinValidation:            md.PinValidation,
		RetrievalReferenceNumber: md.RetrievalReferenceNumber,
		CardProxyNumber:          md.CardProxyNumber,
		MerchantName:             merchantName,
		AuthCode:                 md.AuthorizationID,
	}
}
