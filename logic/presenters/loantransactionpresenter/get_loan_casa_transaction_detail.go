package loantransactionpresenter

import (
	"context"

	"github.com/samber/lo"

	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
)

// GetLoanCasaTransactionDetail fetches the transactions from the DB and returns the formatted GetTransactionDetailResponse.
func GetLoanCasaTransactionDetail(ctx context.Context, transaction *storage.TransactionsData, loanDetail *storage.LoanDetail, paymentDetail *storage.PaymentDetail, account *accountService.GetAccountResponse) *api.GetTransactionDetailResponse {
	var (
		displayName                    string
		sourceOfFund                   string
		counterPartyTransactionDetails map[string]string
	)
	isCASAAccount := presenterhelper.IsCASAAccount(account)
	status := presenterhelper.GetLoanCasaTransactionStatus(transaction, loanDetail)
	displayName = presenterhelper.GetLoanTransactionDisplayNameForListing(transaction)
	amount := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)

	var key = transaction.GetTxnScenarioKey()
	key = lo.Ternary(isCASAAccount, "CASA."+key, key)

	iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
	category := presenterhelper.TransactionResponseHelperFuncs[key].Category
	transactionDescription, _ := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, transaction, displayName)
	counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[key].LoanDetailsFunc(ctx, transaction, loanDetail, paymentDetail)
	hasReceipt := presenterhelper.TransactionResponseHelperFuncs[key].HasReceipt

	return &api.GetTransactionDetailResponse{
		Amount:                 amount,
		Currency:               transaction.TransactionCurrency,
		TransactionDescription: transactionDescription,
		BatchID:                transaction.ClientBatchID,
		TransactionID:          transaction.ClientTransactionID,
		Status:                 status,
		SourceOfFund:           sourceOfFund,
		Category:               category,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			IconURL:            iconURL,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp: &batchValueTS,
		HasReceipt:           hasReceipt,
	}
}
