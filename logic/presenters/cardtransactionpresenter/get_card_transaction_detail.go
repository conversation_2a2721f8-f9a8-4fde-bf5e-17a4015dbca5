package cardtransactionpresenter

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
)

// GetCardTransactionDetail fetches the card transactions from the DB and returns the formatted GetTransactionDetailResponse for card domain
// nolint: funlen
func GetCardTransactionDetail(ctx context.Context, transaction *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) *api.GetTransactionDetailResponse {
	var (
		displayName                    string
		status                         string
		counterPartyTransactionDetails map[string]string
		cardID                         string
		tailCardNumber                 string
		formattedForeignAmount         string
		transactionDescription         string
		bankFee                        string
		exchangeRate                   string
		settlementValueTS              *time.Time
	)

	key := transaction.GetTxnScenarioKey()
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	if cardDetail != nil {
		status = cardDetail.GetFormattedTxnStatusForTxnDetails()
		displayName = presenterhelper.GetCardTransactionDisplayNameForListing(ctx, transaction, cardDetail)
		counterPartyTransactionDetails = helperFuncs.CardDetailsFunc(transaction, cardDetail)
		cardID = cardDetail.CardID
		tailCardNumber = fmt.Sprintf("**%s", cardDetail.TailCardNumber)
		formattedForeignAmount = presenterhelper.GetFormattedForeignAmount(cardDetail)
		transactionDescription, _ = helperFuncs.DescriptionFunc(ctx, cardDetail, displayName)
		bankFee = presenterhelper.GetBankFee(ctx, transaction, cardDetail)
		exchangeRate = presenterhelper.GetExchangeRate(ctx, transaction, cardDetail)
		settlementValueTS = presenterhelper.GetSettlementDate(cardDetail)
	}
	formattedLocalAmount := presenterhelper.GetFormattedLocalAmount(ctx, transaction)
	iconURL := helperFuncs.Icon
	category := helperFuncs.Category
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		transactionDescription = presenterhelper.GetDBMYCardAtmFeeTransactionDecription(transaction, displayName)
	} else if transaction.IsCardMaintenanceFeeTxn() {
		transactionDescription, _ = helperFuncs.DescriptionFunc(ctx, transaction, "")
	}
	// this could be the auth amount if the transaction_data is still in POSTING_PHASE_PENDING_OUTGOING
	amount := transaction.FormattedAmount(ctx)
	// use capture amount for completed charged transaction, digicard should send this amount into the card_transaction_detail table
	// capture amount currently is not available for completed refund transaction
	if status == constants.CompletedStatus && cardDetail != nil && cardDetail.TransferType != constants.CardTransferTypeRefund &&
		!transaction.IsAtmFeeTxn() && !transaction.IsCardMaintenanceFeeTxn() {
		amount = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmount)
		formattedLocalAmount = presenterhelper.GetFormattedSettlementAmount(cardDetail)

		if cardDetail.CaptureAmountTillDate > 0 {
			amount = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmountTillDate)
			formattedLocalAmount = presenterhelper.GetFormattedAmount(cardDetail.Currency, cardDetail.CaptureAmountTillDate)
		}
		if cardDetail.CaptureOriginalAmountTillDate > 0 {
			formattedForeignAmount = presenterhelper.GetFormattedAmount(cardDetail.OriginalCurrency, cardDetail.CaptureOriginalAmountTillDate)
		}
	}
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	return &api.GetTransactionDetailResponse{
		Amount:                 amount,
		FormattedForeignAmount: formattedForeignAmount,
		FormattedLocalAmount:   formattedLocalAmount,
		Currency:               transaction.TransactionCurrency,
		TransactionDescription: transactionDescription,
		BatchID:                transaction.ClientBatchID,
		TransactionID:          transaction.ClientTransactionID,
		Status:                 status,
		Category:               category,
		CardTransactionDetail: &api.CardTransactionDetail{
			CardID:         cardID,
			TailCardNumber: tailCardNumber,
			BankFee:        bankFee,
			SettlementDate: settlementValueTS,
			ExchangeRate:   exchangeRate, // TODO
		},
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			IconURL:            iconURL,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp: &batchValueTS,
	}
}
