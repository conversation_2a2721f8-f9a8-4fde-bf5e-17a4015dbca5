package cardtransactionpresenter

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.myteksi.net/dakota/common/currency"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// GetInternalCardTransactionDetail fetches the card transactions details from the DB and returns the formatted CardTransactionDetail for card domain
//
// nolint: funlen
func GetInternalCardTransactionDetail(ctx context.Context, transaction *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, cardDetail *storage.CardTransactionDetail) *api.GetInternalTransactionDetailResponse {
	var (
		displayName                    string
		status                         string
		counterPartyTransactionDetails map[string]string
		cardTransactionDetail          *api.CardTransactionDetail
		transactionDescription         string
		formattedForeignAmount         string
		transactionTimestamp           *time.Time
		isPartialSettlement            = false
		capturedAmountTillDate         int64
		captureOriginalAmountTillDate  int64
	)

	key := transaction.GetTxnScenarioKey()
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	if cardDetail != nil {
		status = cardDetail.GetFormattedTxnStatus()
		displayName = presenterhelper.GetCardTransactionDisplayName(ctx, transaction, cardDetail)
		counterPartyTransactionDetails = helperFuncs.CardDetailsFunc(transaction, cardDetail)
		cardTransactionDetail = GetInternalCardDetail(ctx, transaction, cardDetail)
		cardTransactionDetail.TransactionCountry = currency.GetByCode(cardDetail.OriginalCurrency).CountryName
		transactionDescription, _ = helperFuncs.DescriptionFunc(ctx, cardDetail, displayName)
		formattedForeignAmount = presenterhelper.GetFormattedForeignAmount(cardDetail)
		transactionTimestamp = &cardDetail.CreationTimestamp
		capturedAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmountTillDate)
		captureOriginalAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureOriginalAmountTillDate)
		if cardDetail.CaptureAmountTillDate > 0 {
			isPartialSettlement = true
		}
	}
	formattedLocalAmount := presenterhelper.GetFormattedLocalAmount(ctx, transaction)
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		transactionDescription = presenterhelper.GetDBMYCardAtmFeeTransactionDecription(transaction, displayName)
	}
	// this could be the auth amount if the transaction_data is still in POSTING_PHASE_PENDING_OUTGOING
	amount := transaction.FormattedAmount(ctx)
	// use capture amount for completed charged transaction, digicard should send this amount into the card_transaction_detail table
	// capture amount currently is not available for completed refund transaction
	if status == constants.CompletedStatus && !transaction.IsAtmFeeTxn() && cardDetail != nil && cardDetail.TransferType != constants.CardTransferTypeRefund {
		amount = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmount)
		formattedLocalAmount = presenterhelper.GetFormattedSettlementAmount(cardDetail)
	}
	if transaction.IsCardMaintenanceFeeTxn() {
		status = presenterhelper.GetPaymentCasaTransactionStatus(transaction, nil)
		var txnDetail dto.TransactionDetail
		cardTransactionDetail = &api.CardTransactionDetail{}
		if err := json.Unmarshal(transaction.TransactionDetails, &txnDetail); err == nil {
			cardTransactionDetail.CardID = txnDetail.CardID
			cardTransactionDetail.TailCardNumber = txnDetail.TailCardNumber
		}
		transactionDescription, _ = helperFuncs.DescriptionFunc(ctx, transaction, "")
		transactionTimestamp = &transaction.BatchValueTimestamp
	}
	return &api.GetInternalTransactionDetailResponse{
		Amount:        amount,
		Currency:      transaction.TransactionCurrency,
		TransactionID: transaction.ClientTransactionID,
		CreditOrDebit: transaction.DebitOrCredit,
		Status:        status,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp:           transactionTimestamp,
		TransactionType:                transaction.TransactionType,
		TransactionSubtype:             transaction.TransactionSubtype,
		TransactionDescription:         transactionDescription,
		FormattedLocalAmount:           formattedLocalAmount,
		FormattedForeignAmount:         formattedForeignAmount,
		AccountID:                      req.AccountID,
		CardTransactionDetail:          cardTransactionDetail,
		IsPartialSettlement:            isPartialSettlement,
		CapturedAmountTillDate:         capturedAmountTillDate,
		CapturedOriginalAmountTillDate: captureOriginalAmountTillDate,
	}
}

// GetInternalCardDetail fetches the card transactions details from the DB and returns the formatted CardTransactionDetail for card domain
func GetInternalCardDetail(ctx context.Context, transaction *storage.TransactionsData, cardDetail *storage.CardTransactionDetail) *api.CardTransactionDetail {
	if cardDetail == nil {
		return &api.CardTransactionDetail{}
	}

	merchantName := presenterhelper.GetMerchantName(ctx, cardDetail)
	cardID := cardDetail.CardID
	tailCardNumber := fmt.Sprintf("**%s", cardDetail.TailCardNumber)
	md := cardDetail.GetMetadata(ctx)
	bankFee := presenterhelper.GetBankFee(ctx, transaction, cardDetail)
	exchangeRate := presenterhelper.GetExchangeRate(ctx, transaction, cardDetail)

	return &api.CardTransactionDetail{
		CardID:                   cardID,
		TailCardNumber:           tailCardNumber,
		SettlementDate:           &cardDetail.ValueTimestamp,
		ExchangeRate:             exchangeRate,
		BankFee:                  bankFee,
		Mcc:                      md.Mcc,
		MaskedCardNumber:         md.MaskedCardNumber,
		TransactionCategory:      md.TransactionCategory,
		TransactionSubCategory:   md.TransactionSubCategory,
		NetworkID:                md.NetworkID,
		ThreeDsValidation:        md.ThreeDsValidation,
		PinValidation:            md.PinValidation,
		RetrievalReferenceNumber: md.RetrievalReferenceNumber,
		CardProxyNumber:          md.CardProxyNumber,
		MerchantName:             merchantName,
		AuthCode:                 md.AuthorizationID,
		AcquirerReferenceData:    md.AcquirerReferenceData,
	}
}
