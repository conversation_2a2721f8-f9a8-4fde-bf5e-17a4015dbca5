// Package cardtransactionpresenter ...
package cardtransactionpresenter

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
)

var (
	cardTxnRespFuncTag = "generateCardTxnResponse"
)

// GenerateCardTxnResponse ...
func GenerateCardTxnResponse(ctx context.Context, transaction *storage.TransactionsData, cardTransactionsDetail map[string]*storage.CardTransactionDetail) *api.TransactionHistoryResponse {
	var iconURL string
	var category *api.Category
	var status string
	logger := slog.FromContext(ctx)
	cardTxnDetail, ok := cardTransactionsDetail[transaction.ClientBatchID]
	if ok {
		status = cardTxnDetail.GetFormattedTxnStatus()
	} else {
		logger.Warn(cardTxnRespFuncTag, fmt.Sprintf("failed to find card txn detail: clientBatchID %s", transaction.ClientBatchID))
		return nil
	}
	counterPartyDisplayName := presenterhelper.GetCardTransactionDisplayNameForListing(ctx, transaction, cardTxnDetail)
	amountInCents := transaction.FormattedAmount(ctx)
	// for card txn that doesn't go through the card network, we don't need to rely on the card detail capture amount
	if status == constants.CompletedStatus && !transaction.IsAtmFeeTxn() && !transaction.IsCardMaintenanceFeeTxn() &&
		cardTxnDetail != nil && cardTxnDetail.TransferType != constants.CardTransferTypeRefund {
		if cardTxnDetail.CaptureAmountTillDate > 0 {
			amountInCents = logic.FormattedAmountInCents(transaction, cardTxnDetail.CaptureAmountTillDate)
		} else {
			amountInCents = logic.FormattedAmountInCents(transaction, cardTxnDetail.CaptureAmount)
		}
	}
	txnScenarioKey := transaction.GetTxnScenarioKey()
	iconURL = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Icon
	category = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Category

	return &api.TransactionHistoryResponse{
		TransactionID:     transaction.ClientTransactionID,
		BatchID:           transaction.ClientBatchID,
		DisplayName:       counterPartyDisplayName,
		IconURL:           iconURL,
		Amount:            amountInCents,
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: utils.TruncateTillSecond(transaction.BatchValueTimestamp),
		Category:          category,
	}
}
