package transactionpresenter

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetPaymentAndCasaTransactionDetail fetches the transactions from the DB and returns the formatted GetTransactionDetailResponse.
// nolint: dupl
func GetPaymentAndCasaTransactionDetail(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail, account *accountService.GetAccountResponse, pocketDetails []accountService.SavingsPocketDetail) *api.GetTransactionDetailResponse {
	var (
		displayName                    string
		sourceOfFund                   string
		iconURL                        string
		category                       *api.Category
		transactionDescription         string
		counterPartyTransactionDetails map[string]string
		hasReceipt                     bool
	)
	isChildAccount := utils.IsChildAccount(account)
	pocketIDToNameMap := BuildPocketIDToNameMap(ctx, pocketDetails, constants.GetAllTransactionsLogicLogTag)
	status := presenterhelper.GetPaymentCasaTransactionStatus(transaction, paymentDetail)
	statusDescription := presenterhelper.GetPaymentCasaStatusDescription(ctx, transaction, paymentDetail)
	displayName = presenterhelper.GetCounterPartyDisplayNamePocketViewForTxnDetail(ctx, transaction, paymentDetail, isChildAccount, pocketIDToNameMap)
	sourceOfFund = getSourceOfFund(ctx, transaction, paymentDetail, account)

	// fetching if pocketID related transfer.
	tenant := config.GetTenant()
	switch tenant {
	case tenants.TenantMY:
		var key = transaction.GetTxnScenarioKey()
		iconURL = presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category = presenterhelper.TransactionResponseHelperFuncs[key].Category
		if paymentDetail != nil {
			transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, paymentDetail, displayName)
		} else {
			transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, transaction, displayName)
		}
		counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(ctx, transaction, paymentDetail)
		hasReceipt = presenterhelper.HasReceipt(key, status)
	default:
		category, iconURL = transaction.GetTransactionCategoryAndIconURL()
		transactionDescription, _ = generateFormattedDescription(ctx, transaction, paymentDetail, displayName)
		counterPartyTransactionDetails = nil
		hasReceipt = false
	}
	amount := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	return &api.GetTransactionDetailResponse{
		Amount:                 amount,
		Currency:               transaction.TransactionCurrency,
		TransactionDescription: transactionDescription,
		TransactionRemarks:     getTransactionRemarks(ctx, transaction, paymentDetail),
		BatchID:                transaction.ClientBatchID,
		TransactionID:          transaction.ClientTransactionID,
		Status:                 status,
		StatusDescription:      statusDescription,
		SourceOfFund:           sourceOfFund,
		Category:               category,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			IconURL:            iconURL,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp:  &batchValueTS,
		HasReceipt:            hasReceipt,
		CardTransactionDetail: lo.Ternary(transaction.IsCardMaintenanceFeeTxn(), buildCardDetailForMaintenanceFeePayment(ctx, transaction), nil),
	}
}

// BuildPocketIDToNameMap ...
func BuildPocketIDToNameMap(ctx context.Context, pocketDetails []accountService.SavingsPocketDetail, logTag string) map[string]string {
	if len(pocketDetails) == 0 {
		slog.FromContext(ctx).Info(logTag, "Pocket details are empty, returning empty map.")
		return nil
	}

	return lo.SliceToMap(pocketDetails, func(pocketDetail accountService.SavingsPocketDetail) (string, string) {
		return pocketDetail.ID, pocketDetail.Name
	})
}

func buildCardDetailForMaintenanceFeePayment(ctx context.Context, txnData *storage.TransactionsData) *api.CardTransactionDetail {
	if txnData == nil {
		return nil
	}
	var txnDetail dto.TransactionDetail
	err := json.Unmarshal(txnData.TransactionDetails, &txnDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing transactionDetails, err: %s", err.Error()))
		return nil
	}
	if txnDetail.CardID == "" || txnDetail.TailCardNumber == "" {
		return nil
	}
	return &api.CardTransactionDetail{
		CardID:         txnDetail.CardID,
		TailCardNumber: "**" + txnDetail.TailCardNumber,
	}
}

func getTransactionRemarks(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) string {
	remarks := ""
	if paymentData != nil {
		remarks = paymentData.GetRemarksFromMetadata(ctx)
	} else {
		remarks = txnData.BatchRemarks
	}
	return remarks
}

// getSourceOfFund will return the source of fund based on data from payments and TM.
func getSourceOfFund(ctx context.Context, transaction *storage.TransactionsData, paymentDetail *storage.PaymentDetail, account *accountService.GetAccountResponse) string {
	var sourceOfFund string
	if transaction.DebitOrCredit != constants.DEBIT {
		return sourceOfFund
	}
	if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
		return sourceOfFundSubAccount(ctx, transaction, account)
	}

	if paymentDetail != nil {
		return sourceOfFundFromAccountName(account)
	}

	return sourceOfFund
}

func sourceOfFundFromAccountName(account *accountService.GetAccountResponse) string {
	if account == nil || account.Account == nil {
		return ""
	}

	if account.Account.ParentAccountID == "" {
		return constants.MainAccountSource
	}
	return account.Account.Name
}

func sourceOfFundSubAccount(ctx context.Context, transaction *storage.TransactionsData, account *accountService.GetAccountResponse) string {
	if constants.PocketFundingTransactionSubType == transaction.TransactionSubtype {
		return constants.MainAccountSource
	}

	var batchDetails map[string]string
	err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
		return ""
	}

	// BP product requirement is to get live pocket name
	if batchDetails["sub_account_type"] == constants.BoostPocket {
		return sourceOfFundFromAccountName(account)
	}

	// SP product requirement is to show fact pocket name
	return batchDetails["source_display_name"]
}

func generateFormattedDescription(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail, counterparty string) (string, error) {
	if paymentData == nil {
		description, err := getDepositsTransactionDetail(ctx, txnData, counterparty)
		if err != nil {
			return "", err
		}
		return description, nil
	}
	if utils.SearchStringArray(constants.SendMoneyFeeTransactionTypes, txnData.TransactionType) {
		return localise.Translate(constants.TransactionFee), nil
	}
	description, err := getPaymentsTransactionDetail(ctx, paymentData, counterparty)
	if err != nil {
		return "", err
	}
	return description, nil
}

func getPaymentsTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	paymentData := dbData.(*storage.PaymentDetail)
	paymentTypeMap := make(map[string]presenterhelper.PaymentsTransactionFuncTemplate)
	paymentTypeMap[constants.IntraBank] = presenterhelper.GetIntraBankTransactionDetail
	paymentTypeMap[constants.RtolAlto] = presenterhelper.GetRtolTransactionDetail
	paymentTypeMap[constants.RtolAJ] = presenterhelper.GetRtolTransactionDetail
	paymentTypeMap[constants.RPP] = presenterhelper.GetRPPTransactionDetail
	if function, ok := paymentTypeMap[paymentData.TransactionSubType]; ok {
		response, err := function(ctx, paymentData, counterParty)
		if err != nil {
			return "", err
		}
		return response, nil
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Invalid paymentTransaction subType: %s", paymentData.TransactionSubType))
	return "", fmt.Errorf("Invalid paymentTransaction subType: %s", paymentData.TransactionSubType) // nolint:stylecheck
}

func getDepositsTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var response string
	var interestOrTaxDisplayName string
	txnData := dbData.(*storage.TransactionsData)
	transactionTypeMap := make(map[string]presenterhelper.InternalTransactionFuncTemplate)
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, txnData.TransactionType) {
		switch txnData.TransactionType {
		case constants.BonusInterestPayoutTransactionType:
			interestOrTaxDisplayName = localise.Translate(constants.BonusInterestEarned)
		case constants.BonusInterestPayoutReversalTransactionType:
			interestOrTaxDisplayName = localise.Translate(constants.BonusInterestEarnedReversal)
		default:
			interestOrTaxDisplayName = localise.Translate(constants.InterestEarned)
		}
	}
	if utils.SearchStringArray(constants.TaxPayoutTransactionTypes, txnData.TransactionType) {
		interestOrTaxDisplayName = localise.Translate(constants.TaxOnInterestDesc)
	}
	transactionTypeMap[constants.InterestPayoutTransactionSubType] = presenterhelper.GetInterestOrTaxPayoutTransactionDetail
	transactionTypeMap[constants.PocketFundingTransactionSubType] = presenterhelper.GetPocketFundingTransactionDetail
	transactionTypeMap[constants.PocketWithdrawalTransactionSubType] = presenterhelper.GetPocketWithdrawalTransactionDetail
	transactionTypeMap[constants.OPSTransactionSubType] = presenterhelper.GetOPSTransactionDetail

	if function, ok := transactionTypeMap[txnData.TransactionSubtype]; ok {
		response = function(counterParty, getTransactionDirection(txnData.DebitOrCredit), interestOrTaxDisplayName)
		return response, nil
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Invalid Transaction type: %s", txnData.TransactionType))
	return "", fmt.Errorf("Invalid Transaction type: %s", txnData.TransactionType) // nolint:stylecheck
}

func getTransactionDirection(debitOrCredit string) string {
	if debitOrCredit == constants.CREDIT {
		return localise.Translate(constants.IN)
	}
	return localise.Translate(constants.OUT)
}
