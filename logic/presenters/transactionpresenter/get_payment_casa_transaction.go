// Package transactionpresenter ...
package transactionpresenter

import (
	"context"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/tenants"
)

// GeneratePaymentCasaTxnResponse current payment transaction and casa transactions are
// nolint: dupl
func GeneratePaymentCasaTxnResponse(ctx context.Context, transaction *storage.TransactionsData, isChildAccount bool, transactionsPaymentDetail map[string]*storage.PaymentDetail, pocketIDToNameMap map[string]string) *api.TransactionHistoryResponse {
	var iconURL string
	var category *api.Category
	var txnPaymentDetail *storage.PaymentDetail

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		if ok {
			txnPaymentDetail = detail
		}
	}
	counterPartyDisplayName := presenterhelper.GetCounterPartyDisplayNamePocketView(ctx, transaction, txnPaymentDetail, isChildAccount, pocketIDToNameMap)
	status := presenterhelper.GetPaymentCasaTransactionStatus(transaction, txnPaymentDetail)
	amountInCents := transaction.FormattedAmount(ctx)

	switch config.GetTenant() {
	case tenants.TenantMY:
		var txnScenarioKey = transaction.GetTxnScenarioKey()
		iconURL = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Icon
		category = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Category
	default:
		category, iconURL = transaction.GetTransactionCategoryAndIconURL()
	}

	return &api.TransactionHistoryResponse{
		TransactionID:     transaction.ClientTransactionID,
		BatchID:           transaction.ClientBatchID,
		DisplayName:       counterPartyDisplayName,
		IconURL:           iconURL,
		Amount:            amountInCents,
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: utils.TruncateTillSecond(transaction.BatchValueTimestamp),
		Category:          category,
	}
}

// GeneratePaymentCasaTxnResponseForOpsSearch return response of current payment transaction and casa transactions for ops search endpoint
// nolint:funlen
func GeneratePaymentCasaTxnResponseForOpsSearch(ctx context.Context, transaction *storage.TransactionsData, isChildAccount bool, transactionsPaymentDetail map[storage.PaymentDataKey]*storage.PaymentDetail, pocketIDToNameMap map[string]string) api.OpsSearchResponse {
	var (
		txnPaymentDetail               *storage.PaymentDetail
		externalID                     string
		transactionDescription         string
		displayName                    string
		swiftCode                      string
		counterPartyAccountID          string
		counterPartyTransactionDetails map[string]string
	)
	if transaction.ClientBatchID != "" {
		detail, ok := transactionsPaymentDetail[storage.BuildPaymentDataKey(transaction.ClientBatchID, transaction.AccountID)]
		if ok {
			txnPaymentDetail = detail
		}
	}
	status := presenterhelper.GetPaymentCasaTransactionStatusForOpsSearch(transaction, txnPaymentDetail)
	amountInCents := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	externalID = presenterhelper.GetExternalTransactionID(ctx, transaction)
	displayName = presenterhelper.GetCounterPartyDisplayNamePocketViewForTxnDetail(ctx, transaction, txnPaymentDetail, isChildAccount, pocketIDToNameMap)
	var key = transaction.GetTxnScenarioKey()
	if txnPaymentDetail != nil {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, txnPaymentDetail, displayName)
		swiftCode = txnPaymentDetail.GetCounterPartyAccount(ctx).SwiftCode
		counterPartyAccountID = txnPaymentDetail.CounterPartyAccountID
		// counterparty account for Grab is internal account
		if transaction.TransactionSubtype == constants.Grab {
			counterPartyAccountID = ""
		}
		if transaction.TransactionSubtype == constants.MooMoo {
			swiftCode = ""
			counterPartyAccountID = ""
		}
	} else {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, transaction, displayName)
		if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyAccountID = transaction.GetPocketTxnCounterPartyAccountID(ctx)
		}
	}
	counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(ctx, transaction, txnPaymentDetail)
	helper.MapNonResidentDetail(ctx, counterPartyTransactionDetails, txnPaymentDetail)
	counterParty := api.CounterParty{
		SwiftCode:          swiftCode,
		AccountNumber:      counterPartyAccountID,
		DisplayName:        displayName,
		TransactionDetails: counterPartyTransactionDetails,
	}
	return api.OpsSearchResponse{
		Amount:                 amountInCents,
		Currency:               transaction.TransactionCurrency,
		BatchID:                transaction.ClientBatchID,
		TransactionID:          transaction.ClientTransactionID,
		CreditOrDebit:          transaction.DebitOrCredit,
		CounterParty:           &counterParty,
		CounterParties:         []api.CounterParty{counterParty},
		Status:                 status,
		TransactionTimestamp:   batchValueTS,
		TransactionType:        transaction.TransactionType,
		TransactionSubtype:     transaction.TransactionSubtype,
		ExternalTransactionID:  externalID,
		TransactionDescription: transactionDescription,
		TransactionRemarks:     getTransactionRemarks(ctx, transaction, txnPaymentDetail),
		AccountID:              transaction.AccountID,
	}
}
