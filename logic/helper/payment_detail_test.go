package helper

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func TestPaymentDetails(t *testing.T) {
	type args struct {
		req                  *api.GetLendingTransactionDetailRequest
		paymentTransactionID string
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.PaymentDetail
		wantErr     bool
	}{
		{
			name: "should return transactions when database has data",
			args: args{
				req: &api.GetLendingTransactionDetailRequest{
					AccountID:     "123456",
					TransactionID: "123123",
				},
				paymentTransactionID: "123123",
			},
			data: []*storage.PaymentDetail{{
				ID:                 101,
				TransactionID:      "1001",
				TransactionDomain:  "LENDING",
				TransactionType:    "DRAWDOWN",
				TransactionSubType: "FASTNETWORK",
			}},
		},
		{
			name: "should return error when database returns error",
			args: args{
				req: &api.GetLendingTransactionDetailRequest{
					AccountID:     "123456",
					TransactionID: "123123",
				},
				paymentTransactionID: "123123",
			},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockPayment := &storage.MockIPaymentDetailDAO{}
			storage.PaymentDetailD = mockPayment
			mockPayment.On("Find", mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)
			payment, err := FetchPaymentDetailForTransaction(context.Background(), test.args.req, test.args.paymentTransactionID)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.data[0], payment)
			}
		})
	}
}
