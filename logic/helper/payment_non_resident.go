package helper

import (
	"context"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

// MapNonResidentDetail maps payment details for non-resident beneficiary
func MapNonResidentDetail(ctx context.Context, transactionDetails map[string]string, paymentDetail *storage.PaymentDetail) {
	if paymentDetail == nil {
		return
	}
	paymentDetailMetaData := paymentDetail.GetPaymentMetadata(ctx)
	if paymentDetailMetaData == nil {
		return
	}
	transactionDetails["purposeCode"] = paymentDetailMetaData.PurposeCode
	transactionDetails["residentStatus"] = paymentDetailMetaData.ResidentStatus
	transactionDetails["beneficiaryCountry"] = paymentDetailMetaData.BeneficiaryCountry
	transactionDetails["relationshipCode"] = paymentDetailMetaData.RelationshipCode
}

// MergePaymentPropertyPaymentInfo extracts fields from payment properties that include non-resident info
func MergePaymentPropertyPaymentInfo(properties *payment_engine_tx.Properties, paymentDetailMetaData map[string]interface{}) {
	if properties == nil || properties.BalanceOfPaymentInfo == nil {
		return
	}

	paymentDetailMetaData["purposeCode"] = properties.BalanceOfPaymentInfo.PurposeCode
	paymentDetailMetaData["residentStatus"] = properties.BalanceOfPaymentInfo.BeneficiaryResidentStatus
	paymentDetailMetaData["beneficiaryCountry"] = properties.BalanceOfPaymentInfo.BeneficiaryCountry
	paymentDetailMetaData["relationshipCode"] = properties.BalanceOfPaymentInfo.RelationshipCode
}
