package helper

import (
	"encoding/json"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func TestFilterHistoricRecordsAndUpdateClientBatchID(t *testing.T) {
	details, _ := json.<PERSON>(map[string]string{"loanTransactionID": "12345"})
	type args struct {
		transactions []*storage.TransactionsData
	}
	tests := []struct {
		name                             string
		args                             args
		expectedBatchID                  string
		expectedCount                    int64
		prevPageLastTxnLoanTransactionID string
	}{
		{
			name: "should update clientBatchID for a completed drawdown",
			args: args{transactions: []*storage.TransactionsData{
				{
					ID:                 1,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 2,
					ClientBatchID:      "9001125da3734864ac740f106a5317ca",
					TmTransactionType:  "TRANSFER",
					BatchDetails:       nil,
					TransactionDomain:  "LENDING",
					TransactionType:    "DRAWDOWN",
					TransactionSubtype: "INTRABANK",
					TransactionDetails: details,
				},
				{
					ID:                 3,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					BatchDetails:       details,
					TransactionDomain:  "SETTLEMENT",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 4,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "SETTLEMENT",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedBatchID:                  "9001125da3734864ac740f106a5317ca",
			expectedCount:                    4,
			prevPageLastTxnLoanTransactionID: uuid.NewString(),
		},
		{
			name: "should update clientBatchID and filter historic for a completed DRAWDOWN",
			args: args{transactions: []*storage.TransactionsData{
				{
					ID:                 1,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 2,
					ClientBatchID:      "9001125da3734864ac740f106a5317ca",
					TmTransactionType:  "TRANSFER",
					BatchDetails:       nil,
					TransactionDomain:  "LENDING",
					TransactionType:    "DRAWDOWN",
					TransactionSubtype: "INTRABANK",
					TransactionDetails: details,
				},
				{
					ID:                 3,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "SETTLEMENT",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 4,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "SETTLEMENT",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedBatchID:                  "9001125da3734864ac740f106a5317ca",
			expectedCount:                    1,
			prevPageLastTxnLoanTransactionID: uuid.NewString(),
		},
		{
			name: "should not update clientBatchID for a drawdown failed at execute intent",
			args: args{transactions: []*storage.TransactionsData{
				{
					ID:                 1,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 2,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "RELEASE",
					BatchDetails:       details,
					TransactionDomain:  "SETTLEMENT",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedBatchID:                  "ebd60e5d8aeee40920e174197760d962",
			expectedCount:                    2,
			prevPageLastTxnLoanTransactionID: uuid.NewString(),
		},
		{
			name: "should update clientBatchID for a drawdown failed at settlement",
			args: args{transactions: []*storage.TransactionsData{
				{
					ID:                 1,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 2,
					ClientBatchID:      "9001125da3734864ac740f106a5317ca",
					TmTransactionType:  "TRANSFER",
					BatchDetails:       nil,
					TransactionDomain:  "LENDING",
					TransactionType:    "DRAWDOWN",
					TransactionSubtype: "INTRABANK",
					TransactionDetails: details,
				},
				{
					ID:                 3,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "RELEASE",
					BatchDetails:       details,
					TransactionDomain:  "SETTLEMENT",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedBatchID:                  "9001125da3734864ac740f106a5317ca",
			expectedCount:                    3,
			prevPageLastTxnLoanTransactionID: uuid.NewString(),
		},
		{
			name: "should not update clientBatchID for RELEASE",
			args: args{transactions: []*storage.TransactionsData{
				{
					ID:                 1,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					ID:                 4,
					ClientBatchID:      "ebd60e5d8aeee40920e174197760d962",
					TmTransactionType:  "RELEASE",
					BatchDetails:       details,
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedBatchID:                  "ebd60e5d8aeee40920e174197760d962",
			expectedCount:                    2,
			prevPageLastTxnLoanTransactionID: uuid.NewString(),
		}}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			actualTransactions := FilterHistoricTransactionsAndUpdateClientBatchID(tt.args.transactions, tt.prevPageLastTxnLoanTransactionID)
			for _, transaction := range actualTransactions {
				assert.Equal(t, tt.expectedBatchID, transaction.ClientBatchID)
			}
		})
	}
}

func TestFilterInvalidTransactions(t *testing.T) {
	type args struct {
		transactions []*storage.TransactionsData
	}
	tests := []struct {
		name     string
		args     args
		expected []*storage.TransactionsData
	}{
		{
			name: "should return only one REPAYMENT transaction for credit",
			args: args{transactions: []*storage.TransactionsData{{
				ID:                 1,
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "INTRABANK",
				AccountAddress:     "DEFAULT",
				DebitOrCredit:      "credit",
			}, {
				ID:                 2,
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "INTRABANK",
				AccountAddress:     "REPAYMENT_MADE",
				DebitOrCredit:      "debit",
			}, {
				ID:                 3,
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "INTRABANK",
				AccountAddress:     "REPAYMENT_MADE",
				DebitOrCredit:      "credit",
			}}},
			expected: []*storage.TransactionsData{{
				ID:                 3,
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "INTRABANK",
				AccountAddress:     "REPAYMENT_MADE",
				DebitOrCredit:      "credit",
			}},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.expected, FilterDefaultOrDebitTransactions(tt.args.transactions), "FilterRejectedTransactions(%v)", tt.args.transactions)
		})
	}
}
