package helper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGetDateMonthYear(t *testing.T) {
	assert.Equal(t, "31 Jan 2023", GetDateMonthYear("2023-01-31"), "GetDateMonthYear")
}

func TestGetDayReturnsCorrectDayForGivenDate(t *testing.T) {
	location, _ := time.LoadLocation("UTC")
	eventTime := time.Date(2023, 2, 15, 0, 0, 0, 0, location)
	expected := "2023-02-15"

	result := GetDay(eventTime, location)

	assert.Equal(t, expected, result)
}

func TestGetDayReturnsCorrectDayForDifferentTimeZones(t *testing.T) {
	location, _ := time.LoadLocation("Asia/Kolkata")
	eventTime := time.Date(2023, 2, 15, 0, 0, 0, 0, location)
	expected := "2023-02-15"

	result := GetDay(eventTime, location)

	assert.Equal(t, expected, result)
}

func TestGetDayReturnsCorrectDayForLeapYear(t *testing.T) {
	location, _ := time.LoadLocation("UTC")
	eventTime := time.Date(2024, 2, 29, 0, 0, 0, 0, location)
	expected := "2024-02-29"

	result := GetDay(eventTime, location)

	assert.Equal(t, expected, result)
}

func TestGetMonthReturnsCorrectMonthForGivenDate(t *testing.T) {
	location, _ := time.LoadLocation("UTC")
	eventTime := time.Date(2023, 2, 15, 0, 0, 0, 0, location)
	expected := "2023-02"

	result := GetMonth(eventTime, location)

	assert.Equal(t, expected, result)
}

func TestGetMonthReturnsCorrectMonthForDifferentTimeZones(t *testing.T) {
	location, _ := time.LoadLocation("Asia/Kolkata")
	eventTime := time.Date(2023, 2, 15, 0, 0, 0, 0, location)
	expected := "2023-02"

	result := GetMonth(eventTime, location)

	assert.Equal(t, expected, result)
}

func TestGetMonthReturnsCorrectMonthForLeapYear(t *testing.T) {
	location, _ := time.LoadLocation("UTC")
	eventTime := time.Date(2024, 2, 29, 0, 0, 0, 0, location)
	expected := "2024-02"

	result := GetMonth(eventTime, location)

	assert.Equal(t, expected, result)
}
