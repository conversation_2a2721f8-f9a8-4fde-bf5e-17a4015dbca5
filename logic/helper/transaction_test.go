package helper

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func TestFetchCursorTransactions(t *testing.T) {
	type args struct {
		req    *dto.TransactionHistorySearchRequest
		cursor *dto.PaginationCursor
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.TransactionsData
		wantErr     bool
	}{
		{
			name: "should return transactions when cursor has starting date and database has data",
			args: args{req: &dto.TransactionHistorySearchRequest{
				AccountID:      "123456",
				PageSize:       2,
				StartingBefore: "2022-10-01T00:00:00Z",
			},
				cursor: &dto.PaginationCursor{
					Date:               "2022-15-01T00:00:00Z",
					ID:                 101,
					FirstTransactionID: 1001,
					TransactionID:      "1001",
				},
			},
			data: []*storage.TransactionsData{{
				ID:                  102,
				ClientTransactionID: "12321",
			},
				{
					ID:                  103,
					ClientTransactionID: "12321",
				}},
		},
		{
			name: "should return transactions when cursor has ending date and database has data",
			args: args{req: &dto.TransactionHistorySearchRequest{
				AccountID:   "123456",
				PageSize:    2,
				EndingAfter: "2022-10-01T00:00:00Z",
			},
				cursor: &dto.PaginationCursor{
					Date:               "2022-15-01T00:00:00Z",
					ID:                 101,
					FirstTransactionID: 1001,
					TransactionID:      "1001",
				},
			},
			data: []*storage.TransactionsData{{
				ID:                  98,
				ClientTransactionID: "12321",
			}, {
				ID:                  99,
				ClientTransactionID: "12321",
			}},
		},
		{
			name: "should return transactions when database has data",
			args: args{
				req: &dto.TransactionHistorySearchRequest{
					AccountID: "123456",
					PageSize:  1,
				},
				cursor: &dto.PaginationCursor{
					Date:               "2022-10-01T00:00:00Z",
					ID:                 101,
					FirstTransactionID: 1001,
					TransactionID:      "1001",
				},
			},
			data: []*storage.TransactionsData{{
				ID:                  100,
				ClientTransactionID: "12321",
			},
				{
					ID:                  101,
					ClientTransactionID: "12321",
				}},
		},
		{
			name: "should return error when database returns error",
			args: args{req: &dto.TransactionHistorySearchRequest{
				AccountID: "123456",
				PageSize:  2,
			}, cursor: &dto.PaginationCursor{
				Date:               "2023-01-31",
				ID:                 101,
				FirstTransactionID: 1001,
				TransactionID:      "1001",
			}},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockTransaction := &storage.MockITransactionsDataDAO{}
			storage.TransactionsDataD = mockTransaction
			mockTransaction.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)

			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					TransactionsSearchFilterDurationInMonths: 6,
				},
				FeatureFlags: config.FeatureFlags{
					EnableTransactionsSearchDurationFilter: config.FeatureFlag{
						Enabled: true,
					},
				},
			}
			transactions, err := FetchTransactionsForCursor(context.Background(), test.args.req, test.args.cursor, mockAppConfig.TransactionHistoryServiceConfig, mockAppConfig.FeatureFlags)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.data, transactions)
			}
		})
	}
}

func TestTransactionListDBFiltersForLending(t *testing.T) {
	t.Run("happyPath", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		var cursorData dto.PaginationCursor

		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				TransactionsSearchFilterDurationInMonths: 6,
			},
			FeatureFlags: config.FeatureFlags{
				EnableTransactionsSearchDurationFilter: config.FeatureFlag{
					Enabled: true,
				},
			},
		}
		filters := lendingTransactionSearchDBFilters(request, &cursorData, mockAppConfig.TransactionHistoryServiceConfig, mockAppConfig.FeatureFlags)
		assert.Equal(t, 10, len(filters))
	})
}

func TestFetchTransactions(t *testing.T) {
	type args struct {
		req *api.GetLendingTransactionDetailRequest
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.TransactionsData
		wantErr     bool
	}{
		{
			name: "should return transactions when database has data",
			args: args{req: &api.GetLendingTransactionDetailRequest{
				AccountID:     "123456",
				TransactionID: "123123",
			}},
			data: []*storage.TransactionsData{{
				ID:                  101,
				ClientTransactionID: "12321",
			}},
		},
		{
			name: "should return error when database returns error",
			args: args{req: &api.GetLendingTransactionDetailRequest{
				AccountID:     "123456",
				TransactionID: "123123",
			}},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					TransactionsSearchFilterDurationInMonths: 6,
				},
				FeatureFlags: config.FeatureFlags{
					EnableTransactionsSearchDurationFilter: config.FeatureFlag{
						Enabled: true,
					},
				},
			}
			mockTransaction := &storage.MockITransactionsDataDAO{}
			storage.TransactionsDataD = mockTransaction
			mockTransaction.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)

			transactions, err := FetchTransaction(context.Background(), test.args.req, mockAppConfig.TransactionHistoryServiceConfig, mockAppConfig.FeatureFlags)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.data, transactions)
			}
		})
	}
}
