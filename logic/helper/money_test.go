package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
)

func TestConvertToMoneyObj(t *testing.T) {
	expected := &api.Money{
		CurrencyCode: "MYR",
		Val:          2043,
	}
	assert.Equal(t, expected, ConvertToMoneyObj(decimal.NewFromFloat(20.43)))
}

func TestConvertValueToFloat(t *testing.T) {
	assert.Equal(t, float32(20.43), ConvertValueToFloat(decimal.NewFromFloat(20.43)))
}
