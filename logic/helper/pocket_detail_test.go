package helper

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	dbmyAccountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	dbmyAccountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestListPocketDetails(t *testing.T) {
	type args struct {
		ctx                      context.Context
		pocketIDs                []string
		prepAccountServiceClient func(*testing.T) dbmyAccountServiceAPI.AccountService
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "happy path",
			args: args{
				ctx:       context.Background(),
				pocketIDs: []string{"8888000081234000001"},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					mockAS := dbmyAccountServiceMock.NewAccountService(t)
					mockAS.On("V2ListSavingsPocketDetail", mock.Anything, mock.Anything).
						Return(&dbmyAccountServiceAPI.ListSavingsPocketDetailResponse{
							SavingsPocketDetail: []dbmyAccountServiceAPI.SavingsPocketDetail{
								{
									ID:   "8888000081234000001",
									Name: "dummy pocket name",
								},
							},
						}, nil)
					return mockAS
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "mockAS returns dummy error",
			args: args{
				ctx:       context.Background(),
				pocketIDs: []string{"8888000081234000001"},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					mockAS := dbmyAccountServiceMock.NewAccountService(t)
					mockAS.On("V2ListSavingsPocketDetail", mock.Anything, mock.Anything).
						Return(nil, errors.New("dummy error"))
					return mockAS
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "mockAS returns pocketDetails with len(pockets) 0",
			args: args{
				ctx:       context.Background(),
				pocketIDs: []string{"8888000081234000001"},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					mockAS := dbmyAccountServiceMock.NewAccountService(t)
					mockAS.On("V2ListSavingsPocketDetail", mock.Anything, mock.Anything).
						Return(&dbmyAccountServiceAPI.ListSavingsPocketDetailResponse{}, nil)
					return mockAS
				},
			},
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ListPocketDetails(tt.args.ctx, tt.args.pocketIDs, tt.args.prepAccountServiceClient(t), "logTag")
			if !tt.wantErr(t, err, "ListPocketDetails()") {
				return
			}
		})
	}
}

func TestGetSinglePocketDetailIfRequired(t *testing.T) {
	type args struct {
		prepCtx                  func(*testing.T) context.Context
		prepTxn                  func() *storage.TransactionsData
		prepAccountServiceClient func(*testing.T) dbmyAccountServiceAPI.AccountService
	}
	tests := []struct {
		name    string
		args    args
		want    []dbmyAccountServiceAPI.SavingsPocketDetail
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "isPocketDetailsRequired true",
			args: args{
				prepCtx: func(t *testing.T) context.Context {
					mockRepo := featureflag.NewMockRepo(t)
					mockRepo.On("IsBoostPocketNameQueryEnabled").Return(true)
					return featureflag.NewContextWithFeatureFlags(context.Background(), mockRepo)
				},
				prepTxn: func() *storage.TransactionsData {
					batchDetails := map[string]string{
						"sub_account_type": "BOOST_POCKET",
						"pocket_id":        "888800008123400001",
					}
					batchDetailsBytes, _ := json.Marshal(batchDetails)
					return &storage.TransactionsData{
						BatchDetails:       batchDetailsBytes,
						TransactionDomain:  constants.DepositsDomain,
						TransactionType:    constants.TransferMoneyTxType,
						TransactionSubtype: constants.PocketFundingTransactionSubType,
					}
				},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					mockAS := dbmyAccountServiceMock.NewAccountService(t)
					mockAS.On("V2ListSavingsPocketDetail", mock.Anything, mock.Anything).
						Return(&dbmyAccountServiceAPI.ListSavingsPocketDetailResponse{
							SavingsPocketDetail: []dbmyAccountServiceAPI.SavingsPocketDetail{
								{
									ID:   "888800008123400001",
									Name: "dummy pocket name",
								},
							},
						}, nil)
					return mockAS
				},
			},
			want: []dbmyAccountServiceAPI.SavingsPocketDetail{
				{
					ID:   "888800008123400001",
					Name: "dummy pocket name",
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "isPocketDetailsRequired false because feature flag is not enabled",
			args: args{
				prepCtx: func(t *testing.T) context.Context {
					mockRepo := featureflag.NewMockRepo(t)
					mockRepo.On("IsBoostPocketNameQueryEnabled").Return(false)
					return featureflag.NewContextWithFeatureFlags(context.Background(), mockRepo)
				},
				prepTxn: func() *storage.TransactionsData {
					batchDetails := map[string]string{
						"sub_account_type": "BOOST_POCKET",
						"pocket_id":        "8888000081234000001",
					}
					batchDetailsBytes, _ := json.Marshal(batchDetails)
					return &storage.TransactionsData{
						BatchDetails:       batchDetailsBytes,
						TransactionDomain:  constants.DepositsDomain,
						TransactionType:    constants.TransferMoneyTxType,
						TransactionSubtype: constants.PocketFundingTransactionSubType,
					}
				},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					return dbmyAccountServiceMock.NewAccountService(t)
				},
			},
			want:    nil,
			wantErr: assert.NoError,
		},
		{
			name: "isPocketDetailsRequired false because transaction subtype is not FROM_MAIN_TO_POCKET / FROM_POCKET_TO_MAIN",
			args: args{
				prepCtx: func(t *testing.T) context.Context {
					mockRepo := featureflag.NewMockRepo(t)
					mockRepo.On("IsBoostPocketNameQueryEnabled").Return(true)
					return featureflag.NewContextWithFeatureFlags(context.Background(), mockRepo)
				},
				prepTxn: func() *storage.TransactionsData {
					batchDetails := map[string]string{
						"sub_account_type": "BOOST_POCKET",
						"pocket_id":        "8888000081234000001",
					}
					batchDetailsBytes, _ := json.Marshal(batchDetails)
					return &storage.TransactionsData{
						BatchDetails:       batchDetailsBytes,
						TransactionDomain:  constants.DepositsDomain,
						TransactionType:    constants.BonusInterestPayoutTransactionType,
						TransactionSubtype: constants.InterestPayoutTransactionSubType,
					}
				},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					return dbmyAccountServiceMock.NewAccountService(t)
				},
			},
			want:    nil,
			wantErr: assert.NoError,
		},
		{
			name: "isPocketDetailsRequired false because sub_account_type is not Boost Pocket",
			args: args{
				prepCtx: func(t *testing.T) context.Context {
					mockRepo := featureflag.NewMockRepo(t)
					mockRepo.On("IsBoostPocketNameQueryEnabled").Return(true)
					return featureflag.NewContextWithFeatureFlags(context.Background(), mockRepo)
				},
				prepTxn: func() *storage.TransactionsData {
					batchDetails := map[string]string{
						"sub_account_type": "SAVINGS_POCKET", // not BOOST_POCKET
						"pocket_id":        "*****************",
					}
					batchDetailsBytes, _ := json.Marshal(batchDetails)
					return &storage.TransactionsData{
						BatchDetails:       batchDetailsBytes,
						TransactionDomain:  constants.DepositsDomain,
						TransactionType:    constants.TransferMoneyTxType,
						TransactionSubtype: constants.PocketFundingTransactionSubType,
					}
				},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					return dbmyAccountServiceMock.NewAccountService(t)
				},
			},
			want:    nil,
			wantErr: assert.NoError,
		},
		{
			name: "isPocketDetailsRequired false because pocket_id does not exist in batch details",
			args: args{
				prepCtx: func(t *testing.T) context.Context {
					mockRepo := featureflag.NewMockRepo(t)
					mockRepo.On("IsBoostPocketNameQueryEnabled").Return(true)
					return featureflag.NewContextWithFeatureFlags(context.Background(), mockRepo)
				},
				prepTxn: func() *storage.TransactionsData {
					batchDetails := map[string]string{
						"sub_account_type": "BOOST_POCKET",
						//"pocket_id":        "8888000081234000001",
					}
					batchDetailsBytes, _ := json.Marshal(batchDetails)
					return &storage.TransactionsData{
						BatchDetails:       batchDetailsBytes,
						TransactionDomain:  constants.DepositsDomain,
						TransactionType:    constants.TransferMoneyTxType,
						TransactionSubtype: constants.PocketFundingTransactionSubType,
					}
				},
				prepAccountServiceClient: func(t *testing.T) dbmyAccountServiceAPI.AccountService {
					return dbmyAccountServiceMock.NewAccountService(t)
				},
			},
			want:    nil,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetSinglePocketDetailIfRequired(tt.args.prepCtx(t), tt.args.prepTxn(), tt.args.prepAccountServiceClient(t), "logTag")
			if !tt.wantErr(t, err, "GetSinglePocketDetailIfRequired()") {
				return
			}
			assert.Equalf(t, tt.want, got, "GetSinglePocketDetailIfRequired")
		})
	}
}
