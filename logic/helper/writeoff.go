// Package helper ...
package helper

import (
	"encoding/json"
	"sort"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// MergeTransactions will merge normal and recovery transactions by avoiding duplicates and sort by timestamp
func MergeTransactions(transactions []*storage.TransactionsData, recoveryTransactions []*storage.TransactionsData) []*storage.TransactionsData {
	var mergedTransactions []*storage.TransactionsData
	clientBatchIDs := make(map[string]bool)
	for _, transaction := range transactions {
		clientBatchIDs[transaction.ClientBatchID] = true
		mergedTransactions = append(mergedTransactions, transaction)
	}
	for _, transaction := range recoveryTransactions {
		_, ok := clientBatchIDs[transaction.ClientBatchID]
		if !ok {
			clientBatchIDs[transaction.ClientBatchID] = true
			mergedTransactions = append(mergedTransactions, transaction)
		}
	}
	sort.SliceStable(mergedTransactions, func(i, j int) bool {
		return mergedTransactions[i].BatchValueTimestamp.After(mergedTransactions[j].BatchValueTimestamp)
	})
	return mergedTransactions
}

// CreateTransactionFromLoanDetailForWriteOff will return a transaction for write off from loan detail
func CreateTransactionFromLoanDetailForWriteOff(loanDetail *storage.LoanDetail, accountID string, repaymentDetails dto.RepaymentDetailsDTO) (*storage.TransactionsData, error) {
	transactionDetails := map[string]string{
		"loanTransactionID": loanDetail.LoanTransactionID,
	}
	txnDetails, err := json.Marshal(transactionDetails)
	if err != nil {
		return nil, err
	}

	transaction := &storage.TransactionsData{
		ClientBatchID:       loanDetail.PaymentTransactionID,
		TransactionDomain:   loanDetail.TransactionDomain,
		TransactionType:     loanDetail.TransactionType,
		TransactionSubtype:  loanDetail.TransactionSubType,
		BatchValueTimestamp: loanDetail.CreatedAt,
		AccountID:           accountID,
		DebitOrCredit:       constants.CREDIT,
		AccountAddress:      constants.RepaymentMadeAccountAddress,
		AccountPhase:        constants.PostingPhaseCommitted,
		TransactionAmount:   repaymentDetails.TotalRepaymentAmount.String(),
		TransactionDetails:  txnDetails,
		TransactionCurrency: loanDetail.Currency,
		CreatedAt:           loanDetail.CreatedAt,
		UpdatedAt:           loanDetail.UpdatedAt,
	}
	if len(repaymentDetails.LoanRepaymentDetail) > 0 {
		transaction.BatchValueTimestamp = repaymentDetails.LoanRepaymentDetail[0].ValueTimestamp
	}
	return transaction, nil
}
