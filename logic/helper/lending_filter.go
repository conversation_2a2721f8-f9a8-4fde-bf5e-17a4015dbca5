package helper

import (
	"encoding/json"
	"sort"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// FilterHistoricTransactionsAndUpdateClientBatchID filters out historic line of credit transactions without loanTransactionID
// and updates the clientBatchID of related transactions
// nolint:gocognit
func FilterHistoricTransactionsAndUpdateClientBatchID(transactions []*storage.TransactionsData, prevPageLastTxnLoanTransactionID string) []*storage.TransactionsData {
	var filteredTransactions, lineOfCreditTransactions []*storage.TransactionsData
	var mapLoanTxnIDToClientBatchID = make(map[string]string)
	for _, transaction := range transactions {
		if transaction.TransactionType == constants.DrawdownTransactionType {
			var details map[string]string
			if transaction.TransactionDetails != nil {
				_ = json.Unmarshal(transaction.TransactionDetails, &details)
			}
			loanTxnID := details["loanTransactionID"]
			if loanTxnID == prevPageLastTxnLoanTransactionID { // if entry matches implies duplicates
				continue
			}
			mapLoanTxnIDToClientBatchID[loanTxnID] = transaction.ClientBatchID
			filteredTransactions = append(filteredTransactions, transaction)
		} else if transaction.TransactionType == constants.LineOfCreditTransactionType {
			lineOfCreditTransactions = append(lineOfCreditTransactions, transaction)
		} else if transaction.TransactionType == constants.RepaymentTransactionType {
			filteredTransactions = append(filteredTransactions, transaction)
		}
	}

	for _, transaction := range lineOfCreditTransactions {
		var details map[string]string
		if transaction.BatchDetails != nil {
			_ = json.Unmarshal(transaction.BatchDetails, &details)
		}
		loanTxnID := details["loanTransactionID"]
		if loanTxnID == "" || loanTxnID == prevPageLastTxnLoanTransactionID { // if entry matches implies duplicates
			continue
		}
		filteredTransactions = append(filteredTransactions, transaction)
		if mapLoanTxnIDToClientBatchID[loanTxnID] != "" {
			transaction.ClientBatchID = mapLoanTxnIDToClientBatchID[loanTxnID]
		}
	}

	sort.Slice(filteredTransactions, func(i, j int) bool {
		return filteredTransactions[i].BatchValueTimestamp.After(filteredTransactions[j].BatchValueTimestamp)
	})
	return filteredTransactions
}

// FilterDefaultOrDebitTransactions filters out invalid transactions based on transaction type
func FilterDefaultOrDebitTransactions(transactions []*storage.TransactionsData) []*storage.TransactionsData {
	var finalTxnData []*storage.TransactionsData
	for _, txn := range transactions {
		if txn.TransactionType == constants.RepaymentTransactionType && (txn.AccountAddress != constants.RepaymentMadeAccountAddress || txn.DebitOrCredit != constants.CREDIT) {
			continue
		}
		finalTxnData = append(finalTxnData, txn)
	}
	return finalTxnData
}

// FilterInvalidTransactions Filter out the transactions based on the account phase and type
func FilterInvalidTransactions(transactions []*storage.TransactionsData) *storage.TransactionsData {
	var validDrawDownTx, validRepaymentTx *storage.TransactionsData
	SortTransactions(transactions)
	for _, transaction := range transactions {
		isRepayment := IsRepayment(transaction)
		isAkpkRepayment := IsAkpkRepayment(transaction)
		if isRepayment || isAkpkRepayment {
			validRepaymentTx = validRepaymentTransaction(transaction, isAkpkRepayment)
			if validRepaymentTx != nil {
				return validRepaymentTx
			}
		} else if IsDrawDown(transaction) {
			validDrawDownTx = validDrawDownTransaction(transaction)
			if validDrawDownTx != nil {
				return validDrawDownTx
			}
		}
	}
	return nil
}

// validRepaymentTransaction filters out repayment transactions to show in txn history
func validRepaymentTransaction(txn *storage.TransactionsData, isAkpkRepayment bool) *storage.TransactionsData {
	validAccountAddress := constants.RepaymentMadeAccountAddress
	if isAkpkRepayment {
		validAccountAddress = constants.DefaultAccountAddress
	}
	if txn.AccountAddress == validAccountAddress && txn.DebitOrCredit == constants.CREDIT && txn.AccountPhase == constants.PostingPhaseCommitted {
		return txn
	}
	return nil
}

// validDrawDownTransaction filters out drawdown transactions to show in txn history
func validDrawDownTransaction(txn *storage.TransactionsData) *storage.TransactionsData {
	if txn.TmTransactionType == constants.Release || txn.TmTransactionType == constants.Transfer || txn.TmTransactionType == constants.OutboundAuthorisation || txn.TmTransactionType == constants.Custom || txn.TmTransactionType == constants.CustomInstruction {
		return txn
	}
	return nil
}

// ExtractPrevPageLastTxnLoanTransactionID : method to extract loanTxID from the transactions data object
func ExtractPrevPageLastTxnLoanTransactionID(prevPageLastTxn *storage.TransactionsData) string {
	var prevPageLastTxnLoanTransactionID string
	if prevPageLastTxn != nil {
		if prevPageLastTxn.TransactionType == constants.DrawdownTransactionType {
			var details map[string]string
			if prevPageLastTxn.TransactionDetails != nil {
				_ = json.Unmarshal(prevPageLastTxn.TransactionDetails, &details)
			}
			prevPageLastTxnLoanTransactionID = details["loanTransactionID"]
		} else if prevPageLastTxn.TransactionType == constants.LineOfCreditTransactionType {
			var details map[string]string
			if prevPageLastTxn.BatchDetails != nil {
				_ = json.Unmarshal(prevPageLastTxn.BatchDetails, &details)
			}
			prevPageLastTxnLoanTransactionID = details["loanTransactionID"]
		}
	}
	return prevPageLastTxnLoanTransactionID
}
