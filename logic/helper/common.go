package helper

import (
	"encoding/json"
	"strings"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// ComputeDateRange will compute the time range based on input params and cursor.
func ComputeDateRange(startingBefore string, endingAfter string, startDate string, endDate string, cursorData dto.PaginationCursor) (string, string) {
	var startingDate, endingDate string
	switch {
	case endingAfter == "" && startingBefore == "":
		startingDate = startDate
		endingDate = endDate
	case endingAfter != "":
		startingDate = cursorData.Date
		endingDate = endDate
	case startingBefore != "":
		startingDate = startDate
		endingDate = cursorData.Date
	}
	return startingDate, endingDate
}

// GetLoanTxnIDFromTransaction returns the loan transaction id for a particular transaction
func GetLoanTxnIDFromTransaction(transaction *storage.TransactionsData) (string, error) {
	var details = make(map[string]string)
	if transaction.TransactionDetails != nil {
		err := json.Unmarshal(transaction.TransactionDetails, &details)
		if err != nil {
			return "", err
		}
	}
	loanTxnID := details["loanTransactionID"]
	if loanTxnID == "" {
		if transaction.BatchDetails != nil {
			err := json.Unmarshal(transaction.BatchDetails, &details)
			if err != nil {
				return "", err
			}
		}
	}
	loanTxnID = details["loanTransactionID"]
	return loanTxnID, nil
}

// IsRepayment ...
func IsRepayment(transaction *storage.TransactionsData) bool {
	return transaction.TransactionType == constants.RepaymentTransactionType
}

// IsAkpkRepayment ...
func IsAkpkRepayment(transaction *storage.TransactionsData) bool {
	return transaction.TransactionType == constants.WriteOffTransactionType &&
		(transaction.TransactionSubtype == constants.BadDebtRecoveryAkpkTransactionSubType ||
			transaction.TransactionSubtype == constants.OpsLossRecoveryAkpkTransactionSubType)
}

// IsDrawDown ...
func IsDrawDown(transaction *storage.TransactionsData) bool {
	if transaction.TransactionType == constants.DrawdownTransactionType || transaction.TmTransactionType == constants.Release || transaction.TmTransactionType == constants.OutboundAuthorisation {
		return true
	}
	return false
}

// IsDepositsAccountID checks whether the provided accountID is deposit account id.
func IsDepositsAccountID(accountID string) bool {
	return strings.HasPrefix(accountID, constants.DepositAccountIDPrefix)
}

// IsBizDepositsAccountID checks whether the provided accountID is BIZ deposit account id.
func IsBizDepositsAccountID(accountID string) bool {
	return strings.HasPrefix(accountID, constants.BizDepositsAccountIDPrefix)
}
