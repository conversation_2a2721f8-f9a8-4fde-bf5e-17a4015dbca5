package helper

import (
	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"testing"
	"time"
)

func TestMergeTransactions(t *testing.T) {
	type args struct {
		transactions         []*storage.TransactionsData
		recoveryTransactions []*storage.TransactionsData
	}
	tests := []struct {
		name string
		args args
		want []*storage.TransactionsData
	}{
		{
			name: "should return two elements when transaction and recovery transaction has different clientBatchID",
			args: args{
				transactions:         []*storage.TransactionsData{{ID: 1, ClientBatchID: "123"}},
				recoveryTransactions: []*storage.TransactionsData{{ID: 1, ClientBatchID: "123"}},
			},
			want: []*storage.TransactionsData{{ID: 1, ClientBatchID: "123"}},
		},
		{
			name: "should return two elements sorted when transaction and recovery transaction has different clientBatchID",
			args: args{
				transactions:         []*storage.TransactionsData{{ID: 1, ClientBatchID: "123", BatchValueTimestamp: time.Unix(1629421200, 0).UTC()}},
				recoveryTransactions: []*storage.TransactionsData{{ID: 2, ClientBatchID: "456", BatchValueTimestamp: time.Unix(1629425400, 0).UTC()}},
			},
			want: []*storage.TransactionsData{
				{ID: 2, ClientBatchID: "456", BatchValueTimestamp: time.Unix(1629425400, 0).UTC()},
				{ID: 1, ClientBatchID: "123", BatchValueTimestamp: time.Unix(1629421200, 0).UTC()}},
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			mergedTransactions := MergeTransactions(tt.args.transactions, tt.args.recoveryTransactions)
			assert.Equal(t, tt.want, mergedTransactions)
		})
	}
}
