package helper

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	dbmyAccountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetSinglePocketDetailIfRequired checks if pocket details are required for the given pocketID in the transaction before calling ListPocketDetails.
//   - should only be used by APIs that return single txnDetail and not by APIs that return multiple txnDetails
func GetSinglePocketDetailIfRequired(
	ctx context.Context,
	txn *storage.TransactionsData,
	accountServiceClient dbmyAccountServiceAPI.AccountService,
	logTag string,
) ([]dbmyAccountServiceAPI.SavingsPocketDetail, error) {
	boostPocketID, isPocketDetailsRequired := getBoostPocketIDFromBatchDetails(ctx, txn, logTag)
	if !isPocketDetailsRequired {
		return nil, nil
	}

	return ListPocketDetails(ctx, []string{boostPocketID}, accountServiceClient, logTag)
}

// getBoostPocketIDFromBatchDetails returns (pocketID, true) if pocket details are required for the given transaction. Returns ("", false) otherwise
func getBoostPocketIDFromBatchDetails(ctx context.Context,
	txn *storage.TransactionsData,
	logTag string,
) (string, bool) {
	// feature flag must be set to true
	if ff := featureflag.FeatureFlagsFromContext(ctx); ff == nil || !ff.IsBoostPocketNameQueryEnabled() {
		return "", false
	}

	// only for transactions between CASA and Boost Pocket
	isTxnCodeForTxnsBetweenCASAAndBP := txn.TransactionDomain == constants.DepositsDomain && txn.TransactionType == constants.TransferMoneyTxType &&
		(txn.TransactionSubtype == constants.PocketFundingTransactionSubType || txn.TransactionSubtype == constants.PocketWithdrawalTransactionSubType)
	if !isTxnCodeForTxnsBetweenCASAAndBP {
		return "", false
	}

	// sub_account_type must be BOOST_POCKET
	batchDetails, err := txn.GetBatchDetails(ctx, logTag)
	if err != nil {
		// error logged internally
		return "", false
	}
	if batchDetails["sub_account_type"] != constants.BoostPocket {
		slog.FromContext(ctx).Debug(logTag, "Sub account type is not Boost Pocket, pocket details not required")
		return "", false
	}

	// extract pocket_id from batchDetails
	pocketID, ok := batchDetails["pocket_id"]
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "pocket_id does not exist in batchDetails")
		return "", false
	}

	return pocketID, true
}

// ListPocketDetails fetches the details of pockets associated with a given parent account ID.
func ListPocketDetails(
	ctx context.Context,
	pocketIDs []string,
	accountServiceClient dbmyAccountServiceAPI.AccountService,
	logTag string,
) ([]dbmyAccountServiceAPI.SavingsPocketDetail, error) {
	// funny naming, this API works for both Savings Pocket and Boost Pocket
	// we are only interested in Boost Pocket here
	resp, err := accountServiceClient.V2ListSavingsPocketDetail(ctx, &dbmyAccountServiceAPI.ListSavingsPocketDetailRequest{
		PocketIDs: pocketIDs,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Error fetching pocket details", slog.Error(err))
		return nil, err
	}

	if resp == nil || len(resp.SavingsPocketDetail) == 0 {
		slog.FromContext(ctx).Info(logTag, "no pocket details found")
		return nil, errors.New("no pocket details found")
	}

	slog.FromContext(ctx).Info(logTag, "Fetched pocket details", slog.CustomTag("count", len(resp.SavingsPocketDetail)))
	return resp.SavingsPocketDetail, nil
}
