package helper

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func TestIsRepayment(t *testing.T) {
	type args struct {
		transaction *storage.TransactionsData
	}
	tests := []struct {
		name     string
		args     args
		expected bool
	}{
		{
			name: "should return true for repayment transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "FAST_NETWORK",
			}},
			expected: true,
		},
		{
			name: "should return false for drawdown transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "DRAWDOWN",
				TransactionSubtype: "FAST_NETWORK",
			}},
			expected: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.expected, IsRepayment(tt.args.transaction), "IsRepayment(%v)", tt.args.transaction)
		})
	}
}

func TestIsAkpkRepayment(t *testing.T) {
	type args struct {
		transaction *storage.TransactionsData
	}
	tests := []struct {
		name     string
		args     args
		expected bool
	}{
		{
			name: "should return true for akpk repayment transaction - bad debt recovery",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "WRITE_OFF",
				TransactionSubtype: "BAD_DEBT_RECOVERY_AKPK",
			}},
			expected: true,
		},
		{
			name: "should return true for akpk repayment transaction - ops loss recovery",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "WRITE_OFF",
				TransactionSubtype: "OPS_LOSS_RECOVERY_AKPK",
			}},
			expected: true,
		},
		{
			name: "should return false for drawdown transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "DRAWDOWN",
				TransactionSubtype: "FAST_NETWORK",
			}},
			expected: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.expected, IsAkpkRepayment(tt.args.transaction), "IsAkpkRepayment(%v)", tt.args.transaction)
		})
	}
}

func TestIsDrawDown(t *testing.T) {
	type args struct {
		transaction *storage.TransactionsData
	}
	tests := []struct {
		name     string
		args     args
		expected bool
	}{
		{
			name: "should return false for repayment transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "REPAYMENT",
				TransactionSubtype: "FAST_NETWORK",
			}},
			expected: false,
		},
		{
			name: "should return true for drawdown transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "TRANSFER",
				TransactionDomain:  "LENDING",
				TransactionType:    "DRAWDOWN",
				TransactionSubtype: "FAST_NETWORK",
			}},
			expected: true,
		},
		{
			name: "should return true for release tm transaction",
			args: args{transaction: &storage.TransactionsData{
				ID:                 0,
				TmTransactionType:  "RELEASE",
				TransactionDomain:  "LENDING",
				TransactionType:    "LINE_OF_CREDIT",
				TransactionSubtype: "UTILISE_LIMIT",
			}},
			expected: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.expected, IsDrawDown(tt.args.transaction), "IsRepayment(%v)", tt.args.transaction)
		})
	}
}

func TestGetLoanTxnIDForTransaction(t *testing.T) {
	details, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	type args struct {
		transaction *storage.TransactionsData
	}
	tests := []struct {
		name        string
		args        args
		expected    string
		expectedErr bool
	}{
		{
			name: "should return transactionID for drawdown transactions",
			args: args{transaction: &storage.TransactionsData{
				ID:                 101,
				TransactionDetails: details,
				TransactionType:    "DRAWDOWN",
				TmTransactionType:  "TRANSFER",
			}},
			expected: "12345",
		},
		{
			name: "should return error for invalid transactions",
			args: args{transaction: &storage.TransactionsData{
				ID:                 101,
				TransactionDetails: json.RawMessage("details"),
				TransactionType:    "DRAWDOWN",
				TmTransactionType:  "TRANSFER",
			}},
			expected:    "",
			expectedErr: true,
		},
		{
			name: "should return transactionID for release transactions",
			args: args{transaction: &storage.TransactionsData{
				ID:                101,
				BatchDetails:      details,
				TransactionType:   "LINE_OF_CREDIT",
				TmTransactionType: "RELEASE",
			}},
			expected: "12345",
		},
		{
			name: "should return error for invalid transactions",
			args: args{transaction: &storage.TransactionsData{
				ID:                101,
				BatchDetails:      json.RawMessage("details"),
				TransactionType:   "LINE_OF_CREDIT",
				TmTransactionType: "RELEASE",
			}},
			expected:    "",
			expectedErr: true,
		},
		{
			name: "should return empty for release transactions",
			args: args{transaction: &storage.TransactionsData{
				ID:                101,
				TransactionType:   "LINE_OF_CREDIT",
				TmTransactionType: "SETTLEMENT",
			}},
			expected: "",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			transactionID, err := GetLoanTxnIDFromTransaction(tt.args.transaction)
			if tt.expectedErr {
				assert.Error(t, err)
			} else {
				assert.Equalf(t, tt.expected, transactionID, "GetLoanTxnIDFromTransaction(%v)", tt.args.transaction)
				assert.NoError(t, err)
			}
		})
	}
}

func TestIsDepositsAccountID(t *testing.T) {
	tests := []struct {
		name      string
		accountID string
		expected  bool
	}{
		{
			name:      "should return false for empty accountID",
			accountID: "",
			expected:  false,
		},
		{
			name:      "should return true for valid deposit accountID",
			accountID: "********",
			expected:  true,
		},
		{
			name:      "should return false for invalid deposit accountID",
			accountID: "********",
			expected:  false,
		},
		{
			name:      "should return false for valid Biz account ID but not Deposit",
			accountID: "********",
			expected:  false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.accountID, func(t *testing.T) {
			result := IsDepositsAccountID(tt.accountID)
			if result != tt.expected {
				t.Errorf("IsDepositsAccountID(%q) = %v; want %v", tt.accountID, result, tt.expected)
			}
		})
	}
}

func TestIsBizDepositsAccountID(t *testing.T) {
	tests := []struct {
		name      string
		accountID string
		expected  bool
	}{
		{
			name:      "should return false for empty accountID",
			accountID: "",
			expected:  false,
		},
		{
			name:      "should return true for valid BIZ deposit accountID",
			accountID: "********",
			expected:  true,
		},
		{
			name:      "should return false for invalid deposit accountID",
			accountID: "********",
			expected:  false,
		},
		{
			name:      "should return false for valid Deposit account ID but not Biz",
			accountID: "********",
			expected:  false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.accountID, func(t *testing.T) {
			result := IsBizDepositsAccountID(tt.accountID)
			if result != tt.expected {
				t.Errorf("IsBizDepositsAccountID(%q) = %v; want %v", tt.accountID, result, tt.expected)
			}
		})
	}
}
