package helper

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

func TestMapPaymentEngineMetaData(t *testing.T) {
	scenarios := []struct {
		name                  string
		paymentDetailMetaData map[string]interface{}
		paymentProperties     *payment_engine_tx.Properties
		expectedResult        map[string]interface{}
	}{
		{
			name:                  "retain the existing fields from metadata",
			paymentDetailMetaData: map[string]interface{}{"serviceType": "", "remarks": "TRANSFER"},
			expectedResult:        map[string]interface{}{"serviceType": "", "remarks": "TRANSFER"},
		},
		{
			name:                  "all non-resident related fields will be mapped to metadata",
			paymentDetailMetaData: map[string]interface{}{"serviceType": "", "remarks": "TRANSFER"},
			paymentProperties: &payment_engine_tx.Properties{
				BalanceOfPaymentInfo: &payment_engine_tx.BalanceOfPaymentInfo{
					PurposeCode:               "16830",
					BeneficiaryResidentStatus: "2",
					BeneficiaryCountry:        "MY",
					RelationshipCode:          "NR",
				},
			},
			expectedResult: map[string]interface{}{"serviceType": "", "remarks": "TRANSFER", "purposeCode": "16830", "residentStatus": "2", "beneficiaryCountry": "MY", "relationshipCode": "NR"},
		},
	}

	for i := range scenarios {
		scenario := scenarios[i]
		t.Run(scenario.name, func(t *testing.T) {
			MergePaymentPropertyPaymentInfo(scenario.paymentProperties, scenario.paymentDetailMetaData)
			assert.Equal(t, scenario.expectedResult, scenario.paymentDetailMetaData)
		})
	}
}
