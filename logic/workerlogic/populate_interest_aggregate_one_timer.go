package workerlogic

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/flow"

	yslog "gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// PopulateInterestAggregateOneTimerLogic ...
// nolint:funlen
func PopulateInterestAggregateOneTimerLogic(ctx context.Context, appCfg *config.AppConfig) {
	startDate, _ := time.Parse(constants.ISODateLayout, appCfg.PopulateInterestAggregateOneTimerConf.StartDate)
	endDate, _ := time.Parse(constants.ISODateLayout, appCfg.PopulateInterestAggregateOneTimerConf.EndDate)
	if !runOneTimer(endDate, appCfg) {
		return
	}

	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Started one timer from %s to %s", startDate, endDate))

	batchSizeInDays := appCfg.PopulateInterestAggregateOneTimerConf.BatchSizeInDays
	batchCount := computeBatchCount(endDate, startDate, float64(batchSizeInDays))
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Batch Count: %d", batchCount))

	var tempStartDate, tempEndDate time.Time
	tempStartDate = startDate
	// the child executors will append interest earned to this map and the parent executor will update the interest earned for the accounts in the map
	accountInterestMap := make(map[string]dto.Money)
	parentBatchExecutor := (&aggregateInterestExecutor{
		conf:                 appCfg,
		accountInterestMap:   accountInterestMap,
		batchCOPInSecond:     appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.BatchCOPInSecond,
		updateStrategy:       UpsertStrategy,
		updateBatchSizeInRow: appCfg.PopulateInterestAggregateOneTimerConf.UpdateBatchSizeInRow,
		refreshAggregation:   appCfg.PopulateInterestAggregateOneTimerConf.RefreshAggregation,
	}).BackfillDefault()

	for iter := 0; iter <= batchCount; iter++ {
		batchCtx := slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobBatchIDTagKey), utils.NewUUID()))
		tempEndDate = tempStartDate.AddDate(0, 0, batchSizeInDays)
		if tempEndDate.After(endDate) || tempEndDate.Equal(endDate) {
			tempEndDate = endDate
		}
		slog.FromContext(batchCtx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Iter: %d, StartDate: %s, EndDate: %s", iter+1, tempStartDate, tempEndDate))

		childBatchExecutor := (&aggregateInterestExecutor{
			startTime:                         tempStartDate,
			endTime:                           tempEndDate,
			conf:                              appCfg,
			accountInterestMap:                accountInterestMap,
			batchSizeInRow:                    appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.BatchSizeInRow,
			batchCOPInSecond:                  appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.BatchCOPInSecond,
			parentBatchSubPaginationInSeconds: appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.ParentBatchSubPaginationInSeconds,
			refreshAggregation:                appCfg.PopulateInterestAggregateOneTimerConf.RefreshAggregation,
			updateBatchSizeInRow:              appCfg.PopulateInterestAggregateOneTimerConf.UpdateBatchSizeInRow,
		}).BackfillDefault()
		steps := flow.Seq(
			childBatchExecutor.BuildBatchFilters(),
			childBatchExecutor.GetBatchBoundaryIDs(),
			childBatchExecutor.AggregateInterestByBatch(),
		)
		childErr := flow.Exec(ctx, steps)
		if childErr != nil && !errors.Is(childErr, ErrShortCircuit) {
			slog.FromContext(batchCtx).Warn(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Failed calculate Iter: %d, StartDate: %s, EndDate: %s, err: %s", iter+1, tempStartDate, tempEndDate, childErr.Error()))
		} else {
			slog.FromContext(batchCtx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Done calculate Iter: %d, StartDate: %s, EndDate: %s", iter+1, tempStartDate, tempEndDate))
		}
		tempStartDate = tempEndDate
	}
	err := flow.Exec(ctx, parentBatchExecutor.UpdateInterestByStrategy())
	if err != nil {
		slog.FromContext(ctx).Error(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Failed to update the interest earned for accounts: %s", err.Error()))
		return
	}
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("One timer completed, from %s to %s", startDate, endDate))
}

// LegacyPopulateInterestAggregateOneTimerLogic ...
func LegacyPopulateInterestAggregateOneTimerLogic(ctx context.Context, appCfg *config.AppConfig) {
	startDate, _ := time.Parse(constants.ISODateLayout, appCfg.PopulateInterestAggregateOneTimerConf.StartDate)
	endDate, _ := time.Parse(constants.ISODateLayout, appCfg.PopulateInterestAggregateOneTimerConf.EndDate)
	if !runOneTimer(endDate, appCfg) {
		return
	}

	accountIDToInterestEarnedMap := make(map[string]dto.Money)
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Started one timer from %s to %s", startDate, endDate))

	batchSizeInDays := appCfg.PopulateInterestAggregateOneTimerConf.BatchSizeInDays
	batchCount := computeBatchCount(endDate, startDate, float64(batchSizeInDays))
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Batch Count: %d", batchCount))

	var tempStartDate, tempEndDate time.Time
	tempStartDate = startDate
	workerID := yslog.TagFromContext(ctx, string(constants.WorkerIDTagKey)).Value()

	for iter := 0; iter <= batchCount; iter++ {
		now := time.Now()
		batchCtx := slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobBatchIDTagKey), utils.NewUUID()))
		tempEndDate = tempStartDate.AddDate(0, 0, batchSizeInDays)
		if tempEndDate.After(endDate) || tempEndDate.Equal(endDate) {
			tempEndDate = endDate
		}
		slog.FromContext(batchCtx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Iter: %d, StartDate: %s, EndDate: %s", iter+1, tempStartDate, tempEndDate))

		accountIDToInterestEarnedMap = CreateInterestPayoutMapForAccountIDAndAddress(batchCtx, tempStartDate, tempEndDate, accountIDToInterestEarnedMap)
		accountIDToInterestEarnedMap = CreateInterestPayoutRevMapForAccountIDAndAddress(batchCtx, tempStartDate, tempEndDate, accountIDToInterestEarnedMap)
		slog.FromContext(batchCtx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Done calculate Iter: %d, StartDate: %s, EndDate: %s, TotalAccountAddress: %d", iter+1, tempStartDate, tempEndDate, len(accountIDToInterestEarnedMap)))
		tempStartDate = tempEndDate
		statsD.Duration(
			metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, now, metrics.CalculateAggregatedInterestOpID,
			fmt.Sprintf("%s%v", metrics.WorkerIDTag, workerID),
		)
	}
	i := 0
	totalAccountAddressCount := len(accountIDToInterestEarnedMap)
	for compositeKey, moneyDTO := range accountIDToInterestEarnedMap {
		now := time.Now()
		i++
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Upsert account aggregated interest [%d/%d]", i, totalAccountAddressCount))
		arr := strings.Split(compositeKey, ",")
		err := createOrOverrideExistingData(ctx, arr[0], arr[1], moneyDTO)
		workerIDTag := fmt.Sprintf("%s%v", metrics.WorkerIDTag, workerID)
		if err != nil {
			statsD.Count1(
				metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, metrics.UpsertAggregatedInterestOpID,
				workerIDTag, metrics.FailedTag, fmt.Sprintf("%s%s", metrics.StatusReasonTag, err.Error()),
			)
		}
		statsD.Duration(
			metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, now, metrics.UpsertAggregatedInterestOpID,
			workerIDTag,
		)
	}
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("One timer completed, from %s to %s", startDate, endDate))
}

func computeBatchCount(endDate, startDate time.Time, batchSizeInDays float64) int {
	batchCount := (endDate.Sub(startDate).Hours() / 24) / batchSizeInDays
	return int(batchCount)
}

func runOneTimer(_ time.Time, appCfg *config.AppConfig) bool {
	// Currently on flag will change to date logic in the next iteration
	return appCfg.PopulateInterestAggregateOneTimerConf.RunOneTimer
	//currentTime := time.Now()
	//return !currentTime.After(endDate)
}

func createOrOverrideExistingData(ctx context.Context, accountID, accountAddress string, money dto.Money) error {
	var interestAggregateRecord *storage.InterestAggregate

	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("AccountAddress", accountAddress),
	}
	result, err := storage.InterestAggregateD.Find(ctx, filters...)

	if err != nil || len(result) == 0 {
		interestAggregateRecord = &storage.InterestAggregate{
			AccountID:           accountID,
			AccountAddress:      accountAddress,
			TotalInterestEarned: money.Amount,
			Currency:            money.Currency,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}
	} else {
		interestAggregateRecord = result[0]
		interestAggregateRecord.TotalInterestEarned = money.Amount // direct override
		interestAggregateRecord.UpdatedAt = time.Now()
	}
	upsertErr := storage.InterestAggregateD.Upsert(ctx, interestAggregateRecord)
	if upsertErr != nil {
		slog.FromContext(ctx).Warn(constants.PopulateInterestAggregateOneTimerLogTag,
			fmt.Sprintf("failed to update the interest earned for accountID:%s, accountAddress: %s, err: %s", accountID, accountAddress, upsertErr.Error()))
		return upsertErr
	}
	slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag,
		fmt.Sprintf("Updated the interest earned for accountID:%s, accountAddress: %s", accountID, accountAddress))
	return nil
}
