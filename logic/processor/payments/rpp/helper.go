package rpp

import (
	"context"
	"errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/payments/common"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// HandlePaymentsInterbankTx ...
func HandlePaymentsInterbankTx(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	var err error
	// validate the input parameters
	validationErrors := common.ValidationPaymentsIntrabankTx(data)
	if len(validationErrors) != 0 {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Validation failed, err: %v", validationErrors))
		return errors.New("validation failed")
	}
	err = createTransferInterbankEntry(ctx, data, txCode, client)
	return err
}

func createTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	switch txCode.Type {
	// Outward RPP Transfer
	case constants.SendMoneyTxType, constants.ReceiveMoneyRevTxType, constants.FundInRevTxType, constants.QrPaymentTxType:
		return sendTransferInterbankEntry(ctx, data, txCode, client)
	// Inward RPP Transfer
	case constants.ReceiveMoneyTxType, constants.SendMoneyRevTxType, constants.FundInTxType, constants.QrPaymentReversalTxType:
		return receiveTransferInterbankEntry(ctx, data, txCode, client)
	default:
		return fmt.Errorf("TransactionType Unknown: %s", txCode.Type)
	}
}

//nolint:dupl
func sendTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	senderTx, err := common.CreateSenderTxEntryDBMY(ctx, data, txCode, client)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to create Sender Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{senderTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}

//nolint:dupl
func receiveTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	receiverTx, err := common.CreateReceiverTxEntryDBMY(ctx, data, txCode, client)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to create Receiver Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{receiverTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}
