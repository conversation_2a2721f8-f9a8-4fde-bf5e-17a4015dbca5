package rpp

import (
	"context"
	"errors"

	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"

	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
)

// Test case for HandlePaymentsInterbankTx method
func TestHandlePaymentsInterbankTx(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.NoError(t, err)
	})

	t.Run("error-path-validation-failed", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPSendTransferErrorInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.NotNil(t, err)
	})

	t.Run("db-error-path-for-senderTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.EqualError(t, err, errorMessage)
	})

	t.Run("db-error-path-for-receiverTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for TestSendTransferRPPEntry
func TestSendTransferRPPEntry(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := sendTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SendMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := sendTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SendMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for ReceiveTransferRPPEntry
func TestReceiveTransferRPPEntry(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for TestFundInTransferRPPEntry
func TestFundInTransferRPPEntry(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPFundInTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.FundInTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRPPFundInTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.FundInTxType)
		assert.EqualError(t, err, errorMessage)
	})
}
