package moomoo

import (
	"context"
	"errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/payments/common"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// HandleMooMooTx ...
func HandleMooMooTx(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	var err error
	// validate the input parameters
	validationErrors := common.ValidationPaymentsIntrabankTx(data)
	if len(validationErrors) != 0 {
		slog.FromContext(ctx).Warn(constants.PaymentMooMooTxTag, fmt.Sprintf("Validation failed, err: %v", validationErrors))
		return errors.New("validation failed")
	}

	if txCode.Type == constants.SpendMoneyTxType {
		err = sendMooMooEntry(ctx, data, txCode)
	} else if txCode.Type == constants.SpendMoneyRevTxType || txCode.Type == constants.CashOutTxType {
		err = receiveMooMooEntry(ctx, data, txCode)
	} else {
		err = fmt.Errorf("TransactionType Unknown: %s", txCode.Type)
	}
	return err
}

//nolint:dupl
func sendMooMooEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	senderTx, err := common.CreateSenderTxEntry(ctx, data, txCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentMooMooTxTag, fmt.Sprintf("Failed to create Sender Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{senderTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentMooMooTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}

//nolint:dupl
func receiveMooMooEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	receiverTx, err := common.CreateReceiverTxEntry(ctx, data, txCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentMooMooTxTag, fmt.Sprintf("Failed to create Receiver Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{receiverTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentMooMooTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}
