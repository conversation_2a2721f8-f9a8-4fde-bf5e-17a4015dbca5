package common // nolint:dupl

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateSenderTxEntry create transaction entry for sender(firstParty)
// nolint:dupl
func CreateSenderTxEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode) (*storage.PaymentDetail, error) {
	accountDetail, counterPartyDetail, err := senderEntryAccountsInfo(ctx, data)
	if err != nil {
		return nil, err
	}

	statusDetail, err := PrepareStatusDetail(data)
	if err != nil {
		return nil, err
	}

	metaData, err := senderEntryMetaDataInfo(ctx, data)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to create SenderTx, err: %s", err.Error()))
		return nil, err
	}
	res := PrepareSenderTxEntry(data, transactionCode)
	res.Account = accountDetail
	res.CounterPartyAccount = counterPartyDetail
	res.StatusDetails = statusDetail
	res.Metadata = metaData
	return res, nil
}

// CreateSenderTxEntryDBMY create transaction entry for sender(firstParty) - DBMY
// nolint:dupl
func CreateSenderTxEntryDBMY(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) (*storage.PaymentDetail, error) {
	accountDetail, counterPartyDetail, err := senderEntryAccountsInfo(ctx, data)
	if err != nil {
		return nil, err
	}

	statusDetail, err := PrepareStatusDetail(data)
	if err != nil {
		return nil, err
	}

	metaData, err := senderEntryMetaDataInfoDBMY(ctx, data, client)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to create SenderTx, err: %s", err.Error()))
		return nil, err
	}
	res := PrepareSenderTxEntry(data, transactionCode)
	res.Account = accountDetail
	res.CounterPartyAccount = counterPartyDetail
	res.StatusDetails = statusDetail
	res.Metadata = metaData
	return res, nil
}

func senderEntryAccountsInfo(ctx context.Context, data *payment_engine_tx.PaymentEngineTx) ([]byte, []byte, error) {
	var accountDetailBytes, counterPartyDetailBytes []byte
	var counterPartyDetail, accountDetail dto.AccountDetail
	proxyObject := ParseProxyObject(data)

	if data.SourceAccount != nil {
		accountDetail = dto.AccountDetail{
			Number:      data.SourceAccount.Number,
			DisplayName: data.SourceAccount.DisplayName,
			PairingID:   data.SourceAccount.PairingID,
			SwiftCode:   data.SourceAccount.SwiftCode,
		}
	}

	if data.DestinationAccount != nil {
		counterPartyDetail = dto.AccountDetail{
			Number:      data.DestinationAccount.Number,
			DisplayName: data.DestinationAccount.DisplayName,
			PairingID:   data.DestinationAccount.PairingID,
			SwiftCode:   data.DestinationAccount.SwiftCode,
			Proxy:       proxyObject,
		}
	}

	accountDetailBytes, err := json.Marshal(accountDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to marshal accountDetail, err: %s", err.Error()))
		return accountDetailBytes, counterPartyDetailBytes, err
	}
	counterPartyDetailBytes, err = json.Marshal(counterPartyDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to marshal counterPartyAccountDetail, err: %s", err.Error()))
		return accountDetailBytes, counterPartyDetailBytes, err
	}
	return accountDetailBytes, counterPartyDetailBytes, nil
}

//nolint:dupl
func senderEntryMetaDataInfo(ctx context.Context, data *payment_engine_tx.PaymentEngineTx) ([]byte, error) {
	var paymentMetadata map[string]string
	metadata := make(map[string]interface{}) // values can be of different types
	// Storing Payments TransferType in metadata
	metadata["transferType"] = data.Type
	metadata["is_ops_transaction"] = data.IsOpsTransaction

	// Assignment of fields to metadata map
	if data.Properties != nil && data.Properties.FAST != nil {
		metadata["purposeCode"] = data.Properties.FAST.PurposeCode
	}
	metadata["remarks"] = data.Remarks
	metadata["external_id"] = data.ExternalID
	if data.Properties != nil {
		if data.Properties.PartnerPayload != nil {
			if data.TransactionCode.SubType == constants.Grab {
				metadata["grab_activity_id"] = data.Properties.PartnerPayload.ActivityID
				metadata["grab_activity_type"] = data.Properties.PartnerPayload.ActivityType
			} else {
				metadata["activity_id"] = data.Properties.PartnerPayload.ActivityID
				metadata["activity_type"] = data.Properties.PartnerPayload.ActivityType
			}
		}
		if data.Properties.RefundProperties != nil {
			metadata["original_verdict_id"] = data.Properties.RefundProperties.OriginalVerdictID
			metadata["original_transaction_id"] = data.Properties.RefundProperties.OriginalTransactionID
		}
	}
	if HasReversalProperties(data) {
		metadata["original_transaction_id"] = data.Properties.ReversalProperties.OriginalTransactionID
	}
	err := json.Unmarshal(data.Metadata, &paymentMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to unmarshal paymentEngine metadata, err %s", err.Error()))
	}
	metadata["serviceType"] = paymentMetadata["serviceType"]
	metadata["counterPartyDisplayName"] = paymentMetadata["counterPartyDisplayName"]
	metaDataBytes, err := json.Marshal(metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to marshal metadata, err: %s", err.Error()))
		return metaDataBytes, err
	}
	return metaDataBytes, nil
}

//nolint:dupl
func senderEntryMetaDataInfoDBMY(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, client paymentExperience.PaymentExperience) ([]byte, error) {
	var (
		bankNameErr     error
		paymentMetadata map[string]interface{}
	)

	metadata := make(map[string]interface{}) // values can be of different types
	// Storing Payments TransferType in metadata
	metadata["transferType"] = data.Type
	metadata["is_ops_transaction"] = data.IsOpsTransaction

	// Assignment of fields to metadata map
	if data.Properties != nil && data.Properties.FAST != nil {
		metadata["purposeCode"] = data.Properties.FAST.PurposeCode
	}
	if HasRPPProperties(data) {
		metadata["recipient_reference"] = data.Properties.RPP.RecipientReference
		metadata["payment_description"] = data.Properties.RPP.PaymentDescription
		metadata["cash_account_code"] = data.Properties.RPP.CashAccountCode
	}
	if HasCounterPartyBank(data) {
		metadata["bank_name"], bankNameErr = GetBankName(ctx, data.DestinationAccount.SwiftCode, client)
		if bankNameErr != nil {
			slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Unable to get bank name, err: %s", bankNameErr.Error()))
		}
	}
	if HasReversalProperties(data) {
		metadata["original_transaction_id"] = data.Properties.ReversalProperties.OriginalTransactionID
	}
	metadata["remarks"] = data.Remarks
	metadata["external_id"] = data.ExternalID
	if data.TransactionCode.SubType == constants.Grab && data.Properties != nil {
		if data.Properties.PartnerPayload != nil {
			metadata["grab_activity_id"] = data.Properties.PartnerPayload.ActivityID
			metadata["grab_activity_type"] = data.Properties.PartnerPayload.ActivityType
		}
		if data.Properties.RefundProperties != nil {
			metadata["original_verdict_id"] = data.Properties.RefundProperties.OriginalVerdictID
			metadata["original_transaction_id"] = data.Properties.RefundProperties.OriginalTransactionID
		}
	}
	err := json.Unmarshal(data.Metadata, &paymentMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to unmarshal paymentEngine metadata, err %s", err.Error()))
	}

	metadata["serviceType"] = ""
	if serviceType, ok := paymentMetadata["serviceType"]; ok {
		metadata["serviceType"] = serviceType
	}
	helper.MergePaymentPropertyPaymentInfo(data.Properties, metadata)

	metaDataBytes, err := json.Marshal(metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SenderTxEntryLogTag, fmt.Sprintf("Error to marshal metadata, err: %s", err.Error()))
		return metaDataBytes, err
	}
	return metaDataBytes, nil
}
