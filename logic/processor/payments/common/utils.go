package common

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

// ValidateTransactionTypeFields validate Transaction Code details from Payment Engine Kafka Message
func ValidateTransactionTypeFields(data *payment_engine_tx.PaymentEngineTx) error {
	transactionCode := *data.TransactionCode
	if transactionCode.Domain == "" || transactionCode.Type == "" || transactionCode.SubType == "" {
		return errors.New("transaction code details not in proper format")
	}
	return nil
}

// ParseProxyObject pare ProxyObject details from Payment Engine Kafka Message
func ParseProxyObject(data *payment_engine_tx.PaymentEngineTx) dto.ProxyObject {
	var proxyDetail dto.ProxyObject
	if data.Properties != nil && data.Properties.Proxy != nil {
		proxyDetail.Type = data.Properties.Proxy.Type
		proxyDetail.Channel = data.Properties.Proxy.Channel
	}
	return proxyDetail
}

// PrepareStatusDetail combine status fields from Payment Engine Kafka Message
func PrepareStatusDetail(data *payment_engine_tx.PaymentEngineTx) ([]byte, error) {
	statusDetail := map[string]string{
		"reason":      data.StatusReason,
		"description": data.StatusReasonDescription,
	}
	statusDetailBytes, err := json.Marshal(statusDetail)
	return statusDetailBytes, err
}

// PrepareSenderTxEntry method that will create sender entry independent of transfer type from payments
func PrepareSenderTxEntry(data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode) *storage.PaymentDetail {
	res := &storage.PaymentDetail{
		TransactionID:         data.TransactionID,
		TransactionDomain:     transactionCode.Domain,
		TransactionType:       transactionCode.Type,
		TransactionSubType:    transactionCode.SubType,
		Amount:                -data.Amount,
		Currency:              data.Currency,
		AccountID:             data.SourceAccount.Number,
		CounterPartyAccountID: data.DestinationAccount.Number,
		Status:                data.Status,
		CreationTimestamp:     data.CreationTimestamp,
		ValueTimestamp:        *data.ValueTimestamp,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}
	return res
}

// PrepareReceiverTxEntry ...
func PrepareReceiverTxEntry(data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode) *storage.PaymentDetail {
	res := &storage.PaymentDetail{
		TransactionID:         data.TransactionID,
		TransactionDomain:     transactionCode.Domain,
		TransactionType:       transactionCode.Type,
		TransactionSubType:    transactionCode.SubType,
		Amount:                data.Amount,
		Currency:              data.Currency,
		AccountID:             data.DestinationAccount.Number,
		CounterPartyAccountID: data.SourceAccount.Number,
		Status:                data.Status,
		CreationTimestamp:     data.CreationTimestamp,
		ValueTimestamp:        *data.ValueTimestamp,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}
	return res
}

// HasRPPProperties check if PaymentEngineTx has RPP Properties
func HasRPPProperties(data *payment_engine_tx.PaymentEngineTx) bool {
	if data.Properties != nil && data.Properties.RPP != nil {
		return true
	}
	return false
}

// HasCounterPartyBank check if the TransactionType has BankName
func HasCounterPartyBank(data *payment_engine_tx.PaymentEngineTx) bool {
	switch data.TransactionCode.Type {
	case constants.FundIn, constants.FundInRevTxType:
		return true
	case constants.SendMoneyTxType, constants.SendMoneyRevTxType:
		if HasRPPProperties(data) {
			return false
		}
		return true
	default:
		return false
	}
}

// HasReversalProperties check if PaymentEngineTx has Reversal Properties
func HasReversalProperties(data *payment_engine_tx.PaymentEngineTx) bool {
	if utils.SearchStringArray(constants.ReverseTransactionType, data.TransactionCode.Type) && data.Properties.ReversalProperties != nil {
		return true
	}
	return false
}

// GetBankList get list of bank
func GetBankList(ctx context.Context, peClient paymentExperience.PaymentExperience) ([]paymentExperience.BankInfo, error) {
	resp, err := peClient.GetExperience(ctx)
	if err != nil {
		return nil, err
	}
	return resp.Data.Banks, nil
}

// GetBankName get bank name
func GetBankName(ctx context.Context, swiftCode string, client paymentExperience.PaymentExperience) (string, error) {
	var bankName string
	bankList, err := GetBankList(ctx, client)
	if err != nil {
		return "", err
	}
	for _, bank := range bankList {
		if bank.SwiftCode == swiftCode {
			bankName = bank.Name
			break
		}
	}
	return bankName, nil
}
