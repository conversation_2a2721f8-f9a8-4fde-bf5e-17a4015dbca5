package common

import (
	"errors"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

// ValidationPaymentsIntrabankTx ...
func ValidationPaymentsIntrabankTx(data *payment_engine_tx.PaymentEngineTx) []error {
	var err []error
	if data.SourceAccount.Number == "" {
		err = append(err, errors.New("SourceAccountID cannot be empty"))
	}
	if data.DestinationAccount.Number == "" {
		err = append(err, errors.New("DestinationAccountID cannot be empty"))
	}
	// TODO: More Validations
	return err
}
