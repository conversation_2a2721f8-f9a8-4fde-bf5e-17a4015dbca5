package common

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
)

func TestReceiverEntryAccountsInfoHappyPath(t *testing.T) {
	t.Run("fast-transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineFASTSendTransferSampleInput()
		accountDetail, counterPartyDetail, err := receiverEntryAccountsInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testReceiver\",\"fullName\":\"\",\"proxy_object\":{\"channel\":\"PAYNOW\",\"type\":\"NRIC\"}}", string(accountDetail))
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testSender\",\"fullName\":\"\",\"proxy_object\":{}}", string(counterPartyDetail))
	})
	t.Run("grab-transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		accountDetail, counterPartyDetail, err := receiverEntryAccountsInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"pairingID\":\"\",\"number\":\"********\",\"swiftCode\":\"\",\"displayName\":\"testSender\",\"fullName\":\"\",\"proxy_object\":{}}", string(counterPartyDetail))
		assert.Equal(t, "{\"pairingID\":\"\",\"number\":\"********\",\"swiftCode\":\"\",\"displayName\":\"testReceiver\",\"fullName\":\"\",\"proxy_object\":{}}", string(accountDetail))
	})
}

func TestReceiverEntryMetaDataInfoHappyPath(t *testing.T) {
	t.Run("FAST Transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineIntrabankTxHappyInput()
		metaDataBytes, err := receiverEntryMetaDataInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"counterPartyDisplayName\":\"\",\"external_id\":\"\",\"is_ops_transaction\":false,\"purposeCode\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"INTRABANK\"}", string(metaDataBytes))
	})
	t.Run("GRAB Transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		metaDataBytes, err := receiverEntryMetaDataInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"counterPartyDisplayName\":\"\",\"external_id\":\"\",\"grab_activity_id\":\"\",\"grab_activity_type\":\"Grab Ride\",\"is_ops_transaction\":false,\"purposeCode\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"PAYMENT\"}", string(metaDataBytes))
	})
	t.Run("RPP Transfer", func(t *testing.T) {
		paymentExp := &paymentExperienceMock.PaymentExperience{}
		paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil).Once()
		peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
		peMessage.TransactionCode = &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "FUND_IN", SubType: "RPP_NETWORK"}
		metaDataBytes, err := receiverEntryMetaDataInfoDBMY(context.Background(), peMessage, paymentExp)
		assert.NoError(t, err)
		assert.Equal(t, "{\"bank_name\":\"United Overseas Bank Berhad (UOB)\",\"external_id\":\"\",\"is_ops_transaction\":false,\"recipient_reference\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"COLLECTION\"}", string(metaDataBytes))
	})

	t.Run("RPP Transfer - Grab", func(t *testing.T) {
		paymentExp := &paymentExperienceMock.PaymentExperience{}
		paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil).Once()
		peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
		peMessage.TransactionCode = &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "FUND_IN", SubType: constants.Grab}
		metaDataBytes, err := receiverEntryMetaDataInfoDBMY(context.Background(), peMessage, paymentExp)
		assert.NoError(t, err)
		assert.Equal(t, "{\"bank_name\":\"United Overseas Bank Berhad (UOB)\",\"external_id\":\"\",\"is_ops_transaction\":false,\"recipient_reference\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"COLLECTION\"}", string(metaDataBytes))
	})
}

func TestCreateReceiverTxEntry(t *testing.T) {
	peMessage := resources.PaymentEngineIntrabankTxHappyInput()
	transactionCode := peMessage.TransactionCode
	_, err := CreateReceiverTxEntry(context.Background(), peMessage, transactionCode)
	assert.NoError(t, err)
}

func TestCreateReceiverTxEntryDBMY(t *testing.T) {
	peMessage := resources.PaymentEngineIntrabankTxHappyInput()
	transactionCode := peMessage.TransactionCode
	clientMock := &paymentExperienceMock.PaymentExperience{}
	_, err := CreateReceiverTxEntryDBMY(context.Background(), peMessage, transactionCode, clientMock)
	assert.NoError(t, err)
}
