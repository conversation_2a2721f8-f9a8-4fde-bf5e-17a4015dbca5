package common

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

func TestValidationPaymentsInternalTxHappyPath(t *testing.T) {
	data := &payment_engine_tx.PaymentEngineTx{SourceAccount: &payment_engine_tx.AccountDetail{Number: "12345"},
		DestinationAccount: &payment_engine_tx.AccountDetail{Number: "54321"}}
	err := ValidationPaymentsIntrabankTx(data)
	assert.Equal(t, 0, len(err))
}

func TestValidationPaymentsInternalTxSourceAccountMissing(t *testing.T) {
	data := &payment_engine_tx.PaymentEngineTx{DestinationAccount: &payment_engine_tx.AccountDetail{Number: "54321"},
		SourceAccount: &payment_engine_tx.AccountDetail{}}
	err := ValidationPaymentsIntrabankTx(data)
	assert.EqualError(t, err[0], "SourceAccountID cannot be empty")
}

func TestValidationPaymentsInternalTxDestinationAccountMissing(t *testing.T) {
	data := &payment_engine_tx.PaymentEngineTx{SourceAccount: &payment_engine_tx.AccountDetail{Number: "12345"},
		DestinationAccount: &payment_engine_tx.AccountDetail{}}
	err := ValidationPaymentsIntrabankTx(data)
	assert.EqualError(t, err[0], "DestinationAccountID cannot be empty")
}
