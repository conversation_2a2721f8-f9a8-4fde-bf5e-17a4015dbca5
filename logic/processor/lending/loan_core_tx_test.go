package lending

import (
	"context"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/metrics"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

// TestCreateBatchLevelEntry test cases for createBatchLevelEntry method
func TestCreateBatchLevelEntry(t *testing.T) {
	data := resources.LoanCoreTxSampleKafkaMessage()
	t.Run("happy-path", func(t *testing.T) {
		expectedResponse, _ := responses.CreateBatchLevelEntryHappyPathResponse()
		response, err := createBatchLevelEntry(data)
		assert.Nil(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

// TestCreateTransactionLevelEntry test cases for createTransactionLevelEntry method
func TestCreateTransactionLevelEntry(t *testing.T) {
	data := resources.LoanCoreTxSampleKafkaMessage()

	t.Run("happy-path", func(t *testing.T) {
		batchLevelData, _ := createBatchLevelEntry(data)
		expectedResponse, _ := responses.CreateTransactionLevelEntryHappyPathResponseLoanCoreTx()
		response, err := createTransactionLevelEntry(context.Background(), batchLevelData, data.TransactionEnquiries[0])
		assert.Nil(t, err)
		assert.Equal(t, expectedResponse.ClientTransactionID, response.ClientTransactionID)
		assert.Equal(t, expectedResponse.TmTransactionID, response.TmTransactionID)
		assert.Equal(t, expectedResponse.TransactionDomain, response.TransactionDomain)
		assert.Equal(t, expectedResponse.BatchRemarks, response.BatchRemarks)
	})
}

// TestHandleLoanCoreTxStream test cases for HandleLoanCoreTxStream method
func TestHandleLoanCoreTxStream(t *testing.T) {
	var stats statsd.Client

	// mocking Publish Metrics client
	mockPublishMetricStruct := &metrics.MockPublishMetricsImpl{}
	mockPublishMetricStruct.On("PublishLoanCoreTxMetrics", mock.Anything, mock.Anything).Return()
	publishMetricStruct = mockPublishMetricStruct

	// MockingAccountCalendarActivity
	mockAccountCalendarActivityStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
	mockAccountCalendarActivityStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
		[]*storage.AccountCalendarActivity{}, nil)
	mockAccountCalendarActivityStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
	storage.AccountCalendarActivityD = mockAccountCalendarActivityStorageDAO

	// Mocking TransactionData
	mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
	mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
	mockTransactionDataStorageDAO.On("SaveBatch", mock.Anything, mock.Anything).Return(nil)
	storage.TransactionsDataD = mockTransactionDataStorageDAO

	// MockingAccountCalendarActivity
	mockInterestAggregateDAO := &storage.MockIInterestAggregateDAO{}
	mockInterestAggregateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
		[]*storage.InterestAggregate{}, nil)
	mockInterestAggregateDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
	storage.InterestAggregateD = mockInterestAggregateDAO

	mockFeatureFlag := &featureflag.MockRepo{}
	mockFeatureFlag.On("IsUpdatePostingInstructionBatch").Return(true)

	t.Run("happy-path", func(t *testing.T) {
		data := resources.LoanCoreTxSampleKafkaMessage()
		err := HandleLoanCoreTxStream(context.Background(), data, stats)
		assert.Nil(t, err)
	})
	t.Run("happy-path-interest-aggregate", func(t *testing.T) {
		data := resources.LoanCoreInterestKafkaMessage()
		err := HandleLoanCoreTxStream(context.Background(), data, stats)
		assert.Nil(t, err)
	})
}

func TestPerformPostingUpdate(t *testing.T) {
	t.Run("No records to insert or update", func(t *testing.T) {
		postingLevelList, dbLevelList := resources.LoanCoreEmptySliceOfTransactionList()
		resources.MockTransactionDataSaveBatchAndUpdate()
		err := performPostingUpdate(context.Background(), postingLevelList, dbLevelList)
		assert.Nil(t, err)
	})
	t.Run("All records in postingLevelTransactionList already exist in dbTransactionsDataList", func(t *testing.T) {
		postingLevelList, dbLevelList := resources.LoanCoreEqualRecordsInBothList()
		resources.MockTransactionDataSaveBatchAndUpdate()
		err := performPostingUpdate(context.Background(), postingLevelList, dbLevelList)
		assert.Nil(t, err)
	})
	t.Run("Some records in postingLevelTransactionList need to be inserted, no records to update", func(t *testing.T) {
		postingLevelList, dbLevelList := resources.LoanCoreDifferentRecordsInBothAndUpdateSecondList()
		resources.MockTransactionDataSaveBatchAndUpdate()
		err := performPostingUpdate(context.Background(), postingLevelList, dbLevelList)
		assert.Nil(t, err)
	})
	t.Run("Some records in postingLevelTransactionList need to be updated, no records to insert", func(t *testing.T) {
		postingLevelList, dbLevelList := resources.LoanCoreDifferentRecordsInBothAndUpdateFirstList()
		resources.MockTransactionDataSaveBatchAndUpdate()
		err := performPostingUpdate(context.Background(), postingLevelList, dbLevelList)
		assert.Nil(t, err)
	})
}
