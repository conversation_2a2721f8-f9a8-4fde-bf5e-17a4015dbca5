package deposits

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.myteksi.net/gophers/go/commons/util/time/grabtime"

	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/streaminfo"
	sdata "gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/servus/v2"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/metrics"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var testDB *sql.DB

func init() {
	if isTestingDB() {
		dbStore := storage.DBStore{
			SchedulerLockDAO: nil,
			Statsd:           statsd.NewNoop(),
		}
		dbConfig := &data.MysqlConfig{
			Dsn:             "root:@tcp(localhost:3306)/transaction_history_test?parseTime=true&loc=UTC",
			MaxIdle:         2,
			MaxOpen:         10,
			ConnMaxLifetime: grabtime.Duration{Duration: 1 * time.Minute},
			PendingCalls:    0,
			ConnectOnce:     sync.Once{},
		}

		db, err := dbStore.GetDatabaseHandle(context.Background(), dbConfig)
		testDB = db
		if err != nil {
			panic(fmt.Errorf("failed to connect to test db: %v", err))
		}
	}
}

func isTestingDB() bool {
	return os.Getenv("TEST_DB") == "true"
}

// TestCreateBatchLevelEntry test cases for createBatchLevelEntry method
func TestCreateBatchLevelEntry(t *testing.T) {
	data := resources.DepositsCoreSampleKafkaMessage()
	t.Run("happy-path", func(t *testing.T) {
		expectedResponse, _ := responses.CreateBatchLevelEntryHappyPathResponse()
		response, err := createBatchLevelEntry(data)
		assert.Nil(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

// TestCreateTransactionLevelEntry test cases for createTransactionLevelEntry method
func TestCreateTransactionLevelEntry(t *testing.T) {
	data := resources.DepositsCoreSampleKafkaMessage()

	t.Run("happy-path", func(t *testing.T) {
		batchLevelData, _ := createBatchLevelEntry(data)
		expectedResponse, _ := responses.CreateTransactionLevelEntryHappyPathResponse()
		response, err := createTransactionLevelEntry(context.Background(), batchLevelData, data.TransactionEnquiries[0])
		assert.Nil(t, err)
		assert.Equal(t, expectedResponse.ClientTransactionID, response.ClientTransactionID)
		assert.Equal(t, expectedResponse.TmTransactionID, response.TmTransactionID)
		assert.Equal(t, expectedResponse.TransactionDomain, response.TransactionDomain)
		assert.Equal(t, expectedResponse.BatchRemarks, response.BatchRemarks)
	})
}

// TestHandleDepositsCoreStream test cases for HandleDepositsCoreStream method
func TestHandleDepositsCoreStream(t *testing.T) {
	var stats statsd.Client
	oriFunc := storage.WithTransaction
	defer func() { storage.WithTransaction = oriFunc }()

	scenarios := []struct {
		desc                   string
		data                   *deposits_core_tx.DepositsCoreTx
		enableBatchInsertion   bool
		findTransactionDataErr error
		saveTransactionDataErr error
		expectedErr            error
	}{
		{
			desc: "happy-path",
			data: resources.DepositsCoreSampleKafkaMessage(),
		},
		{
			desc:                   "batch-insertion-happy-path",
			data:                   resources.DepositsCoreSampleKafkaMessage(),
			findTransactionDataErr: data.ErrNoData,
			enableBatchInsertion:   true,
		},
		{
			desc:                   "sad-path",
			data:                   resources.DepositsCoreSampleKafkaMessage(),
			findTransactionDataErr: errors.New("unknown find error"),
			expectedErr:            errors.New("unknown find error"),
		},
		{
			desc:                   "batch-insertion-sad-path",
			data:                   resources.DepositsCoreSampleKafkaMessage(),
			enableBatchInsertion:   true,
			findTransactionDataErr: errors.New("unknown find error"),
			expectedErr:            errors.New("failed to load transaction data from db, err: unknown find error"),
		},
		{
			desc:                   "sad-path-save-error",
			data:                   resources.DepositsCoreSampleKafkaMessage(),
			saveTransactionDataErr: errors.New("unknown save error"),
			expectedErr:            errors.New("unknown save error"),
		},
		{
			desc:                   "batch-insert-sad-path-save-error",
			data:                   resources.DepositsCoreSampleKafkaMessage(),
			enableBatchInsertion:   true,
			saveTransactionDataErr: errors.New("unknown save error"),
			expectedErr:            errors.New("failed to batch save 2 transaction datas to db, err: unknown save error"),
		},
		{
			desc: "happy-path-interest-aggregate",
			data: resources.DepositsCoreInterestKafkaMessage(),
		},
		{
			desc: "happy-path-interest-aggregate-with-interest-reversal",
			data: resources.DepositsCoreInterestReversalKafkaMessage(),
		},
		{
			desc: "happy-path-interest-aggregate-rejected-transactions",
			data: resources.DepositsCoreRejectedTxnKafkaMessage(),
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			storage.WithTransaction = func(ctx context.Context, db *sql.DB, fn storage.TxFn) (err error) {
				if scenario.findTransactionDataErr != nil {
					return scenario.findTransactionDataErr
				}
				if scenario.saveTransactionDataErr != nil {
					return scenario.saveTransactionDataErr
				}
				return nil
			}
			// mocking Snowflake client
			mockPublishMetricStruct := &metrics.MockPublishMetricsImpl{}
			mockPublishMetricStruct.On("PublishDepositsCoreMetrics", mock.Anything, mock.Anything).Return()
			publishMetricStruct = mockPublishMetricStruct

			// MockingAccountCalendarActivity
			mockAccountCalendarActivityStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
			mockAccountCalendarActivityStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
				[]*storage.AccountCalendarActivity{}, nil)
			mockAccountCalendarActivityStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
			storage.AccountCalendarActivityD = mockAccountCalendarActivityStorageDAO

			// Mocking TransactionData
			mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
			mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, scenario.findTransactionDataErr)
			mockTransactionDataStorageDAO.On("Save", mock.Anything, mock.Anything).Return(scenario.saveTransactionDataErr)
			storage.TransactionsDataD = mockTransactionDataStorageDAO

			// MockingAccountCalendarActivity
			mockInterestAggregateDAO := &storage.MockIInterestAggregateDAO{}
			mockInterestAggregateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
				[]*storage.InterestAggregate{}, nil)
			mockInterestAggregateDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
			storage.InterestAggregateD = mockInterestAggregateDAO
			ctx := context.Background()

			// Batch insertion enabled
			if scenario.enableBatchInsertion {
				mockFeatureFlag := &featureflag.MockRepo{}
				mockFeatureFlag.On("IsInsertTransactionsByBatchEnabled").Return(true)
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything).Return(nil, scenario.findTransactionDataErr)
				mockTransactionDataStorageDAO.On("SaveBatch", mock.Anything, mock.Anything).Return(scenario.saveTransactionDataErr)
				ctx = featureflag.NewContextWithFeatureFlags(ctx, mockFeatureFlag)
			}
			err := HandleDepositsCoreStream(ctx, scenario.data, stats, nil)
			assert.Equal(t, scenario.expectedErr, err)
		})
	}
}

// TestHandleDepositsCoreStream test cases for HandleDepositsCoreStream method
func TestHandleDepositsCoreStream_UpdateRecord(t *testing.T) {
	var stats statsd.Client
	oriFunc := storage.WithTransaction
	defer func() { storage.WithTransaction = oriFunc }()
	// mocking Snowflake client
	mockPublishMetricStruct := &metrics.MockPublishMetricsImpl{}
	mockPublishMetricStruct.On("PublishDepositsCoreMetrics", mock.Anything, mock.Anything).Return()
	publishMetricStruct = mockPublishMetricStruct

	// MockingAccountCalendarActivity
	mockAccountCalendarActivityStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
	mockAccountCalendarActivityStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
		[]*storage.AccountCalendarActivity{}, nil)
	mockAccountCalendarActivityStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
	storage.AccountCalendarActivityD = mockAccountCalendarActivityStorageDAO

	// Mocking TransactionData
	mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
	mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
		[]*storage.TransactionsData{
			{
				ID: 1,
			},
		}, nil)
	mockTransactionDataStorageDAO.On("Update", mock.Anything, mock.Anything).Return(nil)
	storage.TransactionsDataD = mockTransactionDataStorageDAO

	t.Run("happy-path-update-record", func(t *testing.T) {
		storage.WithTransaction = func(ctx context.Context, db *sql.DB, fn storage.TxFn) (err error) {
			return nil
		}
		data := resources.DepositsCoreSampleKafkaMessage()
		err := HandleDepositsCoreStream(context.Background(), data, stats, testDB)
		assert.Nil(t, err)
	})
}

func Test_batchInsertTransfer(t *testing.T) {
	if !isTestingDB() {
		t.Log("skipping test; TEST_DB env var not set")
		return
	}
	t.Run("when transfer is not present it should insert new transfer", func(t *testing.T) {
		ctx := context.Background()
		_, err := testDB.Exec("truncate table transactions_data")
		dbConfig := &data.MysqlConfig{
			Dsn:             "root:@tcp(localhost:3306)/transaction_history_test?parseTime=true&loc=UTC",
			MaxIdle:         2,
			MaxOpen:         10,
			ConnMaxLifetime: grabtime.Duration{Duration: 1 * time.Minute},
			PendingCalls:    0,
			ConnectOnce:     sync.Once{},
		}
		oriTxnDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriTxnDAO
		}()
		storage.TransactionsDataD = storage.NewTransactionsDataDAO(&servus.DataConfig{
			MySQL: &sdata.MysqlConfig{
				MysqlMasterSlaveConfig: sdata.MysqlMasterSlaveConfig{
					Master: dbConfig,
				},
			},
		}, statsd.NewNoop())
		if err != nil {
			t.Fatalf("failed to truncate transactions_data: %v", err)
		}
		const clientBatchID = "a3f444c6-a409-4280-b3dd-01d2296c6076"
		const tmPostingInstructionBatchID = "275e193a-df72-4df6-adb5-930d72419d86"

		const clientTransactionID1 = "8131ee12-a7ef-41f6-930d-dcdc339f2bfb"
		const clientTransactionID2 = "8131ee12-41f6-41f6-930d-dcdc339f2bfb"
		const tmTransactionID1 = "fbea2058-849e-482a-84e7-df4409d845c2"
		const tmTransactionID2 = "fbea2058-849e-84e7-84e7-df4409d845c2"

		txnData1 := &storage.TransactionsData{
			ClientTransactionID:         clientTransactionID1,
			ClientBatchID:               clientBatchID,
			TmPostingInstructionBatchID: tmPostingInstructionBatchID,
			TmTransactionID:             tmTransactionID1,
			TmTransactionType:           "Transfer",
			BatchRemarks:                "",
			BatchStatus:                 "ACCEPTED",
			BatchErrorType:              "",
			BatchDetails:                json.RawMessage(`{}`),
			BatchErrorMessage:           "",
			BatchInsertionTimestamp:     time.Now(),
			BatchValueTimestamp:         time.Now(),
			TransactionDomain:           "DEPOSIT",
			TransactionType:             constants.TransferMoneyTxType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			TransactionDetails:          json.RawMessage(`{}`),
			TransactionViolations:       json.RawMessage(`{}`),
			AccountID:                   "*************",
			AccountAddress:              "DEFAULT",
			AccountAsset:                "BANK_ASSET",
			AccountPhase:                "COMMITTED",
			DebitOrCredit:               "CREDIT",
			TransactionAmount:           "100000",
			TransactionCurrency:         "MYR",
			BalanceAfterTransaction:     "300000",
			Metadata:                    json.RawMessage(`{"interest_rate":"0.0003"}`),
		}
		txnData2 := &storage.TransactionsData{
			ClientTransactionID:         clientTransactionID2,
			ClientBatchID:               clientBatchID,
			TmPostingInstructionBatchID: tmPostingInstructionBatchID,
			TmTransactionID:             tmTransactionID2,
			TmTransactionType:           "Transfer",
			BatchRemarks:                "",
			BatchStatus:                 "ACCEPTED",
			BatchErrorType:              "",
			BatchDetails:                json.RawMessage(`{}`),
			BatchErrorMessage:           "",
			BatchInsertionTimestamp:     time.Now(),
			BatchValueTimestamp:         time.Now(),
			TransactionDomain:           "DEPOSIT",
			TransactionType:             constants.TransferMoneyTxType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			TransactionDetails:          json.RawMessage(`{}`),
			TransactionViolations:       json.RawMessage(`{}`),
			AccountID:                   "*************",
			AccountAddress:              "DEFAULT",
			AccountAsset:                "BANK_ASSET",
			AccountPhase:                "COMMITTED",
			DebitOrCredit:               "DEBIT",
			TransactionAmount:           "100000",
			TransactionCurrency:         "MYR",
			BalanceAfterTransaction:     "300000",
			Metadata:                    json.RawMessage(`{"interest_rate":"0.0003"}`),
		}
		_, err = batchInsertTransfer(ctx, []*storage.TransactionsData{txnData1, txnData2})
		if err != nil {
			t.Fatalf("failed to insert transfer: %v", err)
		}
		res, err := storage.TransactionsDataD.Find(ctx, data.EqualTo("TmPostingInstructionBatchID", tmPostingInstructionBatchID))
		if err != nil {
			t.Fatalf("failed to find transfer: %v", err)
		}
		assert.Equal(t, 2, len(res))
	})
}

func Test_updateOrInsertTransfer(t *testing.T) {
	if !isTestingDB() {
		t.Log("skipping test; TEST_DB env var not set")
		return
	}

	t.Run("when transfer is not present it should insert new transfer", func(t *testing.T) {
		ctx := context.Background()
		_, err := testDB.Exec("truncate table transactions_data")
		if err != nil {
			t.Fatalf("failed to truncate transactions_data: %v", err)
		}
		clientTransactionID := "8131ee12-a7ef-41f6-930d-dcdc339f2bfb"
		clientBatchID := "a3f444c6-a409-4280-b3dd-01d2296c6076"
		tmPostingInstructionBatchID := "275e193a-df72-4df6-adb5-930d72419d86"
		tmTransactionID := "fbea2058-849e-482a-84e7-df4409d845c2"

		txnData := &storage.TransactionsData{
			ClientTransactionID:         clientTransactionID,
			ClientBatchID:               clientBatchID,
			TmPostingInstructionBatchID: tmPostingInstructionBatchID,
			TmTransactionID:             tmTransactionID,
			TmTransactionType:           "Transfer",
			BatchRemarks:                "",
			BatchStatus:                 "ACCEPTED",
			BatchErrorType:              "",
			BatchDetails:                nil,
			BatchErrorMessage:           "",
			BatchInsertionTimestamp:     time.Now(),
			BatchValueTimestamp:         time.Now(),
			TransactionDomain:           "DEPOSIT",
			TransactionType:             constants.InterestPayoutTransactionType,
			TransactionSubtype:          constants.InterestPayoutTransactionSubType,
			TransactionDetails:          nil,
			TransactionViolations:       nil,
			AccountID:                   "*************",
			AccountAddress:              "DEFAULT",
			AccountAsset:                "BANK_ASSET",
			AccountPhase:                "COMMITTED",
			DebitOrCredit:               "CREDIT",
			TransactionAmount:           "100000",
			TransactionCurrency:         "MYR",
			BalanceAfterTransaction:     "300000",
			Metadata:                    json.RawMessage(`{"interest_rate":"0.0003"}`),
		}
		err = insertTransactionData(ctx, testDB, txnData)
		if err != nil {
			t.Fatalf("failed to insert transfer: %v", err)
		}
		assert.NotEqual(t, 0, txnData.ID)
	})

	t.Run("when transfer is present it should update transfer related data only", func(t *testing.T) {
		ctx := context.Background()
		_, err := testDB.Exec("truncate table transactions_data")
		if err != nil {
			t.Fatalf("failed to truncate transactions_data: %v", err)
		}
		clientTransactionID := "8131ee12-a7ef-41f6-930d-dcdc339f2bfb"
		clientBatchID := "a3f444c6-a409-4280-b3dd-01d2296c6076"
		tmPostingInstructionBatchID := "275e193a-df72-4df6-adb5-930d72419d86"
		tmTransactionID := "fbea2058-849e-482a-84e7-df4409d845c2"

		txnData := &storage.TransactionsData{
			ClientTransactionID:         clientTransactionID,
			ClientBatchID:               clientBatchID,
			TmPostingInstructionBatchID: tmPostingInstructionBatchID,
			TmTransactionID:             tmTransactionID,
			TmTransactionType:           "Transfer",
			BatchRemarks:                "",
			BatchStatus:                 "ACCEPTED",
			BatchErrorType:              "",
			BatchDetails:                nil,
			BatchErrorMessage:           "",
			BatchInsertionTimestamp:     time.Now().UTC(),
			BatchValueTimestamp:         time.Now().UTC(),
			TransactionDomain:           "DEPOSIT",
			TransactionType:             constants.InterestPayoutTransactionType,
			TransactionSubtype:          constants.InterestPayoutTransactionSubType,
			TransactionDetails:          nil,
			TransactionViolations:       nil,
			AccountID:                   "*************",
			AccountAddress:              "DEFAULT",
			AccountAsset:                "BANK_ASSET",
			AccountPhase:                "COMMITTED",
			DebitOrCredit:               "CREDIT",
			TransactionAmount:           "100000",
			TransactionCurrency:         "MYR",
			BalanceAfterTransaction:     "300000",
			Metadata:                    json.RawMessage(`{"interest_rate": "0.0003"}`),
		}
		err = insertTransactionData(ctx, testDB, txnData)
		if err != nil {
			t.Fatalf("failed to insert transfer: %v", err)
		}
		txnData.BalanceAfterTransaction = ""
		txnData.Metadata = json.RawMessage(`{}`)
		txnData.BatchErrorMessage = "some error"

		_, err = updateOrInsertTransfer(ctx, txnData, testDB)
		assert.NoError(t, err)
		if err != nil {
			return
		}

		updatedTxnData, err := findTxnDataByKeys(ctx, testDB, txnDataKeys{
			AccountID:                   txnData.AccountID,
			AccountAddress:              txnData.AccountAddress,
			AccountPhase:                txnData.AccountPhase,
			TmPostingInstructionBatchID: txnData.TmPostingInstructionBatchID,
			TmTransactionID:             txnData.TmTransactionID,
		})
		emptyJSON := json.RawMessage(`{}`)
		emptyJSONArray := json.RawMessage(`[]`)
		assert.NoError(t, err)

		assert.Equal(t, "300000", updatedTxnData.BalanceAfterTransaction, "balance after transaction should not be updated")
		assert.Equal(t, json.RawMessage(`{"interest_rate": "0.0003"}`), updatedTxnData.Metadata, "metadata should not be updated")

		assert.Equal(t, txnData.ClientTransactionID, updatedTxnData.ClientTransactionID)
		assert.Equal(t, txnData.ClientBatchID, updatedTxnData.ClientBatchID)
		assert.Equal(t, txnData.TmPostingInstructionBatchID, updatedTxnData.TmPostingInstructionBatchID)
		assert.Equal(t, txnData.TmTransactionID, updatedTxnData.TmTransactionID)
		assert.Equal(t, txnData.TmTransactionType, updatedTxnData.TmTransactionType)
		assert.Equal(t, txnData.BatchRemarks, updatedTxnData.BatchRemarks)
		assert.Equal(t, txnData.BatchStatus, updatedTxnData.BatchStatus)
		assert.Equal(t, txnData.BatchErrorType, updatedTxnData.BatchErrorType)
		assert.Equal(t, emptyJSON, updatedTxnData.BatchDetails)
		assert.Equal(t, "some error", updatedTxnData.BatchErrorMessage)
		assert.Equal(t, txnData.BatchInsertionTimestamp, updatedTxnData.BatchInsertionTimestamp)
		assert.Equal(t, txnData.BatchValueTimestamp, updatedTxnData.BatchValueTimestamp)
		assert.Equal(t, txnData.TransactionDomain, updatedTxnData.TransactionDomain)
		assert.Equal(t, txnData.TransactionType, updatedTxnData.TransactionType)
		assert.Equal(t, txnData.TransactionSubtype, updatedTxnData.TransactionSubtype)
		assert.Equal(t, emptyJSON, updatedTxnData.TransactionDetails)
		assert.Equal(t, emptyJSONArray, updatedTxnData.TransactionViolations)
		assert.Equal(t, txnData.AccountID, updatedTxnData.AccountID)
		assert.Equal(t, txnData.AccountAddress, updatedTxnData.AccountAddress)
		assert.Equal(t, txnData.AccountAsset, updatedTxnData.AccountAsset)
		assert.Equal(t, txnData.AccountPhase, updatedTxnData.AccountPhase)
		assert.Equal(t, txnData.DebitOrCredit, updatedTxnData.DebitOrCredit)
		assert.Equal(t, txnData.TransactionAmount, updatedTxnData.TransactionAmount)
		assert.Equal(t, txnData.TransactionCurrency, updatedTxnData.TransactionCurrency)
	})
}

func Test_insertTransactionData(t *testing.T) {
	txnData := &storage.TransactionsData{
		ClientTransactionID:         "8131ee12-a7ef-41f6-930d-dcdc339f2bfb",
		ClientBatchID:               "a3f444c6-a409-4280-b3dd-01d2296c6076",
		TmPostingInstructionBatchID: "275e193a-df72-4df6-adb5-930d72419d86",
		TmTransactionID:             "fbea2058-849e-482a-84e7-df4409d845c2",
		TmTransactionType:           "Transfer",
		BatchRemarks:                "",
		BatchStatus:                 "ACCEPTED",
		BatchErrorType:              "",
		BatchDetails:                nil,
		BatchErrorMessage:           "",
		BatchInsertionTimestamp:     time.Now(),
		BatchValueTimestamp:         time.Now(),
		TransactionDomain:           "DEPOSIT",
		TransactionType:             constants.InterestPayoutTransactionType,
		TransactionSubtype:          constants.InterestPayoutTransactionSubType,
		TransactionDetails:          nil,
		TransactionViolations:       nil,
		AccountID:                   "*************",
		AccountAddress:              "DEFAULT",
		AccountAsset:                "BANK_ASSET",
		AccountPhase:                "COMMITTED",
		DebitOrCredit:               "CREDIT",
		TransactionAmount:           "100000",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "300000",
		Metadata:                    json.RawMessage(`{"interest_rate":"0.0003"}`),
	}
	scenarios := []struct {
		name string
		err  error
	}{
		{
			name: "it should not return error",
		},
		{
			name: "when there is transaction error it should return error",
			err:  errors.New("transaction error"),
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			mockResult := &storage.MockResult{}
			mockDB := &storage.MockTransaction{}
			mockResult.On("RowsAffected").Return(int64(1), nil)
			mockResult.On("LastInsertId").Return(int64(1), nil)
			var args []interface{}
			for i := 0; i < 30; i++ {
				args = append(args, mock.Anything)
			}
			mockDB.On("Exec", args...).Run(func(args mock.Arguments) {
				assert.Equal(t, args.Get(1).(string), txnData.ClientTransactionID)
				assert.Equal(t, args.Get(2).(string), txnData.ClientBatchID)
				assert.Equal(t, args.Get(3).(string), txnData.TmPostingInstructionBatchID)
				assert.Equal(t, args.Get(4).(string), txnData.TmTransactionID)
				assert.Equal(t, args.Get(5).(string), txnData.TmTransactionType)
				assert.Equal(t, args.Get(6).(string), txnData.BatchRemarks)
				assert.Equal(t, args.Get(7).(string), txnData.BatchStatus)
				assert.Equal(t, args.Get(8).(string), txnData.BatchErrorType)
				assert.Equal(t, args.Get(9).(json.RawMessage), txnData.BatchDetails)
				assert.Equal(t, args.Get(10).(string), txnData.BatchErrorMessage)
				assert.Equal(t, args.Get(11).(time.Time), txnData.BatchInsertionTimestamp)
				assert.Equal(t, args.Get(12).(time.Time), txnData.BatchValueTimestamp)
				assert.Equal(t, args.Get(13).(string), txnData.TransactionDomain)
				assert.Equal(t, args.Get(14).(string), txnData.TransactionType)
				assert.Equal(t, args.Get(15).(string), txnData.TransactionSubtype)
				assert.Equal(t, args.Get(16).(json.RawMessage), txnData.TransactionDetails)
				assert.Equal(t, args.Get(17).(json.RawMessage), txnData.TransactionViolations)
				assert.Equal(t, args.Get(18).(string), txnData.AccountID)
				assert.Equal(t, args.Get(19).(string), txnData.AccountAddress)
				assert.Equal(t, args.Get(20).(string), txnData.AccountAsset)
				assert.Equal(t, args.Get(21).(string), txnData.AccountPhase)
				assert.Equal(t, args.Get(22).(string), txnData.DebitOrCredit)
				assert.Equal(t, args.Get(23).(string), txnData.TransactionAmount)
				assert.Equal(t, args.Get(24).(string), txnData.TransactionCurrency)
				assert.Equal(t, args.Get(25).(string), txnData.BalanceAfterTransaction)
				assert.Equal(t, args.Get(26).(json.RawMessage), txnData.Metadata)
			}).Return(mockResult, scenario.err)
			err := insertTransactionData(context.Background(), mockDB, txnData)
			if scenario.err != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, uint64(1), txnData.ID)
			}
		})
	}
}

func Test_buildTransactionDatas(t *testing.T) {
	t.Run("should build 1 transaction datas for 1 posting", func(t *testing.T) {
		ctx := context.Background()
		event := &deposits_core_tx.DepositsCoreTx{
			StreamInfo:            streaminfo.StreamInfo{},
			ID:                    utils.NewUUID(),
			BatchID:               utils.NewUUID(),
			RequestIdempotencyKey: utils.NewUUID(),
			TransactionEnquiries: []deposits_core_tx.TransactionEnquiry{
				{
					ID:                 utils.NewUUID(),
					TransactionID:      utils.NewUUID(),
					TransactionType:    constants.TransferMoneyTxType,
					TransactionDetails: make(map[string]string),
					Violations:         nil,
					TransactionCode: &deposits_core_tx.TransactionCode{
						Type:    constants.TransferMoneyTxType,
						Domain:  constants.SavingsPocketType,
						SubType: constants.PocketFundingTransactionSubType,
					},
					CommittedPostings: []*deposits_core_tx.Posting{
						{
							Credit:         false,
							Amount:         "5000",
							Currency:       "MYR",
							AccountID:      "main-account",
							AccountAddress: constants.DefaultAccountAddress,
							Asset:          "BANK_COMMERCIAL",
							Phase:          constants.PostingPhaseCommitted,
						},
						{
							Credit:         true,
							Amount:         "5000",
							Currency:       "MYR",
							AccountID:      "sub-account",
							AccountAddress: constants.DefaultAccountAddress,
							Asset:          "BANK_COMMERCIAL",
							Phase:          constants.PostingPhaseCommitted,
						},
					},
					RejectedPostings: nil,
				},
			},
			BatchDetails:       make(map[string]string),
			BatchRemarks:       "",
			ValueTimestamp:     time.Now(),
			InsertionTimestamp: time.Now(),
			Status:             constants.BatchStatusAccepted,
			ErrorType:          "",
			ErrorMessage:       "",
		}
		res, err := buildTransactionDatas(ctx, event)
		assert.Nil(t, err)
		assert.Equal(t, 2, len(res))
		sourceTxn := res[0]
		destTxn := res[1]
		assert.Equal(t, "main-account", sourceTxn.AccountID)
		assert.Equal(t, constants.DEBIT, sourceTxn.DebitOrCredit)
		assert.Equal(t, "sub-account", destTxn.AccountID)
		assert.Equal(t, constants.CREDIT, destTxn.DebitOrCredit)
	})
}
