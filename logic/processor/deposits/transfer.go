package deposits

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/db/mysql/queries"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/metrics"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var publishMetricStruct metrics.PublishMetricsImpl = &metrics.PublishMetrics{}

type txnDataKeys struct {
	AccountID                   string
	AccountAddress              string
	AccountPhase                string
	TmPostingInstructionBatchID string
	TmTransactionID             string
}

// HandleDepositsCoreStream common method handling deposits-core message stream
func HandleDepositsCoreStream(ctx context.Context, data *deposits_core_tx.DepositsCoreTx, stats statsd.Client, db *sql.DB) error {
	txnDatas, err := buildTransactionDatas(ctx, data)
	if len(txnDatas) == 0 {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("no transaction data to be processed for batch, %s", data.BatchID))
		return nil
	}
	if err != nil {
		return err
	}

	featureFlags := featureflag.FeatureFlagsFromContext(ctx)
	// insert the transaction data entities into db row by row
	if featureFlags == nil || !featureFlags.IsInsertTransactionsByBatchEnabled() {
		// insert the transaction data entities into db
		for _, txnData := range txnDatas {
			// searching of matching based on uniqueness constraint
			accountLevelTransactionData, upsertErr := updateOrInsertTransfer(ctx, txnData, db)
			if upsertErr != nil {
				return upsertErr
			}
			recordAccountCalendarActivity(ctx, accountLevelTransactionData)
			publishMetricStruct.PublishDepositsCoreMetrics(accountLevelTransactionData, stats)
		}
	}

	// batch insert the transaction data entities into db
	if featureFlags != nil && featureFlags.IsInsertTransactionsByBatchEnabled() {
		txnDatas, err = batchInsertTransfer(ctx, txnDatas)
		if err != nil {
			return err
		}
		for _, txnData := range txnDatas {
			recordAccountCalendarActivity(ctx, txnData)
			publishMetricStruct.PublishDepositsCoreMetrics(txnData, stats)
		}
	}
	return nil
}

func buildTransactionDatas(ctx context.Context, data *deposits_core_tx.DepositsCoreTx) ([]*storage.TransactionsData, error) {
	// creating batch level data
	batchLevelData, err := createBatchLevelEntry(data)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("batchDetails marshal failed, err:%v", err.Error()))
		return nil, err
	}
	var txnDatas []*storage.TransactionsData
	// This is empty json instead of "null"
	emptyMetaData := []byte("{}")

	// Iterating over all transaction in batch and build the transaction data entities
	for _, transfer := range data.TransactionEnquiries {
		transactionLevelData, marshalErr := createTransactionLevelEntry(ctx, batchLevelData, transfer)
		if marshalErr != nil {
			return nil, marshalErr
		}
		// Merge Committed & Rejected Postings
		var postings []*deposits_core_tx.Posting
		if transfer.CommittedPostings != nil {
			postings = append(postings, transfer.CommittedPostings...)
		}
		if transfer.RejectedPostings != nil {
			postings = append(postings, transfer.RejectedPostings...)
		}
		// Iterating over all accountIDs involved in transaction
		for _, posting := range postings {
			// we need to de-reference transactionLevelData so that we don't always inject the same object in the slice
			accountLevelTransactionData := *transactionLevelData
			accountLevelTransactionData.AccountID = posting.AccountID
			accountLevelTransactionData.AccountAddress = posting.AccountAddress
			accountLevelTransactionData.AccountAsset = posting.Asset
			accountLevelTransactionData.AccountPhase = posting.Phase
			accountLevelTransactionData.TransactionCurrency = posting.Currency
			accountLevelTransactionData.TransactionAmount = posting.Amount
			accountLevelTransactionData.Metadata = emptyMetaData
			if posting.Credit {
				accountLevelTransactionData.DebitOrCredit = constants.CREDIT
			} else {
				accountLevelTransactionData.DebitOrCredit = constants.DEBIT
			}
			txnDatas = append(txnDatas, &accountLevelTransactionData)
		}
	}
	return txnDatas, nil
}

// batchInsertTransfer inserts a batch of transaction data into db and returns the inserted rows if the batch event has not been processed before.
// The returned rows will be empty if there is no row being inserted. Fields that are supposed to be set in db runtime will remain as empty.
func batchInsertTransfer(ctx context.Context, txnDatas []*storage.TransactionsData) ([]*storage.TransactionsData, error) {
	batchID := txnDatas[0].TmPostingInstructionBatchID
	// check if the batch event has been processed before
	res, err := storage.TransactionsDataD.Find(ctx, data.EqualTo("TmPostingInstructionBatchID", batchID))

	if err != nil && !errors.Is(err, data.ErrNoData) {
		return nil, fmt.Errorf("failed to load transaction data from db, err: %s", err.Error())
	}
	if len(res) > 0 {
		ids := make([]string, len(res))
		for i := range res {
			ids[i] = fmt.Sprintf("%d", res[i].ID)
		}
		slog.FromContext(ctx).Debug(constants.DepositsCoreStreamLogTag, fmt.Sprintf(
			"found %d transactions data with id [%s] for posting batch %s, skip insertion", len(res), strings.Join(ids, ","), batchID),
		)
		return nil, nil
	}
	err = storage.TransactionsDataD.SaveBatch(ctx, txnDatas)
	if err != nil {
		return nil, fmt.Errorf("failed to batch save %d transaction datas to db, err: %s", len(txnDatas), err.Error())
	}
	return txnDatas, nil
}

// updateOrInsertTransfer searches matching Tx based on uniqueness of
// TmPostingInstructionBatchID - TmTransactionID - AccountID - AccountPhase - AccountAddress
// and update/insert based on filter response
func updateOrInsertTransfer(ctx context.Context, accountLevelTransactionData *storage.TransactionsData, db *sql.DB) (*storage.TransactionsData, error) {
	// As the transaction wrapper func only allows 1 input params, hence the transaction data object will be mutated in-place through closure
	txnErr := storage.WithTransaction(ctx, db, func(txn storage.Transaction) error {
		txnData, err := findTxnDataByKeys(ctx, txn, txnDataKeys{
			AccountID:                   accountLevelTransactionData.AccountID,
			AccountAddress:              accountLevelTransactionData.AccountAddress,
			AccountPhase:                accountLevelTransactionData.AccountPhase,
			TmPostingInstructionBatchID: accountLevelTransactionData.TmPostingInstructionBatchID,
			TmTransactionID:             accountLevelTransactionData.TmTransactionID,
		})
		if err != nil {
			return err
		}

		// If matching response not found, create new posting entry
		if txnData == nil || txnData.ID == 0 {
			err = insertTransactionData(ctx, db, accountLevelTransactionData)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("save failed, err:%v", err.Error()))
				return err
			}
			return nil
		}

		// If matching response found, update the posting entry
		accountLevelTransactionData.ID = txnData.ID
		err = updateTransactionDataBalanceAndMeta(ctx, db, accountLevelTransactionData)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("update failed, err:%v", err.Error()))
			return err
		}
		return nil
	})
	return accountLevelTransactionData, txnErr
}

// findTxnDataByKeys searches matching Tx based on the given combination of keys
//
//nolint:funlen
func findTxnDataByKeys(ctx context.Context, dbHandler storage.Transaction, keys txnDataKeys) (*storage.TransactionsData, error) {
	var txnData storage.TransactionsData

	rows, err := dbHandler.Query(
		queries.SelectTxnDataForPostingProcessingQuery,
		keys.TmPostingInstructionBatchID,
		keys.TmTransactionID,
		keys.AccountID,
		keys.AccountPhase,
		keys.AccountAddress,
	)
	defer func() {
		if rows != nil {
			_ = rows.Close()
		}
	}()

	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("Error getting matching transaction, %s", err.Error()))
		return nil, err
	}
	// We expect only 1 row result, so it's fine to just use 1 entity object
	for rows.Next() {
		err = rows.Scan(
			&txnData.ID,
			&txnData.ClientTransactionID,
			&txnData.ClientBatchID,
			&txnData.TmPostingInstructionBatchID,
			&txnData.TmTransactionID,
			&txnData.BatchRemarks,
			&txnData.BatchStatus,
			&txnData.BatchErrorType,
			&txnData.BatchDetails,
			&txnData.BatchErrorMessage,
			&txnData.TransactionDomain,
			&txnData.TransactionType,
			&txnData.TmTransactionType,
			&txnData.TransactionSubtype,
			&txnData.TransactionDetails,
			&txnData.TransactionViolations,
			&txnData.AccountID,
			&txnData.AccountAddress,
			&txnData.AccountAsset,
			&txnData.AccountPhase,
			&txnData.DebitOrCredit,
			&txnData.TransactionAmount,
			&txnData.TransactionCurrency,
			&txnData.BalanceAfterTransaction,
			&txnData.Metadata,
			&txnData.BatchInsertionTimestamp,
			&txnData.BatchValueTimestamp,
			&txnData.CreatedAt,
			&txnData.UpdatedAt,
		)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("Error in serializing transaction query result, %s", err.Error()))
			return nil, err
		}
	}
	if rows.Err() != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("Error in scanning transaction query result, %s", err.Error()))
		return nil, rows.Err()
	}
	return &txnData, nil
}

func updateTransactionDataBalanceAndMeta(_ context.Context, dbHandler storage.Transaction, txnData *storage.TransactionsData) error {
	txnData.UpdatedAt = time.Now()
	_, err := dbHandler.Exec(
		queries.UpdateTxnDataTransferCommand,
		txnData.ClientTransactionID,
		txnData.ClientBatchID,
		txnData.TmPostingInstructionBatchID,
		txnData.TmTransactionID,
		txnData.TmTransactionType,
		txnData.BatchRemarks,
		txnData.BatchStatus,
		txnData.BatchErrorType,
		txnData.BatchDetails,
		txnData.BatchErrorMessage,
		txnData.BatchInsertionTimestamp,
		txnData.BatchValueTimestamp,
		txnData.TransactionDomain,
		txnData.TransactionType,
		txnData.TransactionSubtype,
		txnData.TransactionDetails,
		txnData.TransactionViolations,
		txnData.AccountID,
		txnData.AccountAddress,
		txnData.AccountAsset,
		txnData.AccountPhase,
		txnData.DebitOrCredit,
		txnData.TransactionAmount,
		txnData.TransactionCurrency,
		txnData.UpdatedAt,
		txnData.ID,
	)
	if err != nil {
		return err
	}
	return err
}

// insertTransactionData inserts a new Tx with the attributes value from the input Tx.
// The new Tx ID will be set to the input Tx for upstream consumption.
func insertTransactionData(ctx context.Context, dbHandler storage.Transaction, txnData *storage.TransactionsData) error {
	now := time.Now()
	res, err := dbHandler.Exec(
		queries.InsertTxnDataCommand,
		txnData.ClientTransactionID,
		txnData.ClientBatchID,
		txnData.TmPostingInstructionBatchID,
		txnData.TmTransactionID,
		txnData.TmTransactionType,
		txnData.BatchRemarks,
		txnData.BatchStatus,
		txnData.BatchErrorType,
		txnData.BatchDetails,
		txnData.BatchErrorMessage,
		txnData.BatchInsertionTimestamp,
		txnData.BatchValueTimestamp,
		txnData.TransactionDomain,
		txnData.TransactionType,
		txnData.TransactionSubtype,
		txnData.TransactionDetails,
		txnData.TransactionViolations,
		txnData.AccountID,
		txnData.AccountAddress,
		txnData.AccountAsset,
		txnData.AccountPhase,
		txnData.DebitOrCredit,
		txnData.TransactionAmount,
		txnData.TransactionCurrency,
		txnData.BalanceAfterTransaction,
		txnData.Metadata,
		now,
		now,
	)
	if err != nil {
		return err
	}
	id, err := res.LastInsertId()
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("Error in getting transaction data id after insert, %s", err.Error()))
		return err
	}
	txnData.ID = utils.MustConvertToUint64(id)
	txnData.CreatedAt = now
	txnData.UpdatedAt = now
	return nil
}

// createTransactionLevelEntry create transaction level data
func createTransactionLevelEntry(ctx context.Context, batchLevelData *storage.TransactionsData, transfer deposits_core_tx.TransactionEnquiry) (*storage.TransactionsData, error) {
	// Marshaling violation
	violation, err := json.Marshal(transfer.Violations)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("violation marshal failed, err:%v", err.Error()))
		return nil, err
	}

	// Marshaling transactionDetails
	transactionDetails, err := json.Marshal(transfer.TransactionDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("transactionDetails marshal failed, err:%v", err.Error()))
		return nil, err
	}
	transactionLevelData := batchLevelData
	transactionLevelData.TmTransactionID = transfer.ID
	transactionLevelData.ClientTransactionID = transfer.TransactionID
	transactionLevelData.TmTransactionType = transfer.TransactionType
	transactionLevelData.TransactionDetails = transactionDetails
	transactionLevelData.TransactionViolations = violation
	transactionLevelData.TransactionDomain = transfer.TransactionCode.Domain
	transactionLevelData.TransactionType = transfer.TransactionCode.Type
	transactionLevelData.TransactionSubtype = transfer.TransactionCode.SubType
	transactionLevelData.CreatedAt = time.Now()
	transactionLevelData.UpdatedAt = time.Now()
	return transactionLevelData, nil
}

func createBatchLevelEntry(data *deposits_core_tx.DepositsCoreTx) (*storage.TransactionsData, error) {
	batchDetails, err := json.Marshal(data.BatchDetails)

	if err != nil {
		return &storage.TransactionsData{}, err
	}
	return &storage.TransactionsData{
		TmPostingInstructionBatchID: data.ID,
		ClientBatchID:               data.BatchID,
		BatchDetails:                batchDetails,
		BatchValueTimestamp:         data.ValueTimestamp,
		BatchInsertionTimestamp:     data.InsertionTimestamp,
		BatchStatus:                 data.Status,
		BatchErrorType:              data.ErrorType,
		BatchErrorMessage:           data.ErrorMessage,
		BatchRemarks:                data.BatchRemarks,
	}, nil
}

func recordAccountCalendarActivity(ctx context.Context, accountLevelTransactionData *storage.TransactionsData) {
	_, err := storage.CheckAndUpdateCalendarActivity(ctx, accountLevelTransactionData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsCoreStreamLogTag, fmt.Sprintf("Failed to record calendar activity, err: %s", err.Error()))
	}
}
