package logic

import (
	"context"

	"github.com/Rhymond/go-money"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
)

// FormattedAmountInCents denoting direction of money (in cents) flow
func FormattedAmountInCents(transaction *storage.TransactionsData, amountInCents int64) int64 {
	var finalAmount int64
	if transaction.DebitOrCredit == constants.DEBIT {
		finalAmount = -1 * amountInCents // implies amount is debited
	} else {
		finalAmount = 1 * amountInCents // implies amount is credited
	}
	return finalAmount
}

// FormattedAmount converts string amount in dollars to int64 cents, also denoting direction of money flow
func FormattedAmount(ctx context.Context, transaction *storage.TransactionsData) int64 {
	amountInCents := money.New(utils.GetAmountInCents(ctx, transaction.TransactionAmount), transaction.TransactionCurrency).Amount()
	//if transaction.DebitOrCredit == constants.DEBIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
	//	amountInCents = 1 * amountInCents // implies amount is credited
	//} else if transaction.DebitOrCredit == constants.CREDIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
	//	amountInCents = -1 * amountInCents // implies amount is debited
	//} else
	// above logic not applicable to DBMY
	if transaction.DebitOrCredit == constants.DEBIT {
		amountInCents = -1 * amountInCents // implies amount is debited
	} else {
		amountInCents = 1 * amountInCents // implies amount is credited
	}
	return amountInCents
}

// GetTransactionAmount determines whether to get the amount from transactions data or from transaction detail for settled amount
func GetTransactionAmount(ctx context.Context, transaction *storage.TransactionsData, detailTransaction *storage.CardTransactionDetail) int64 {
	var transactionAmount int64
	if detailTransaction == nil {
		return FormattedAmount(ctx, transaction)
	}
	transactionAmount = transaction.FormattedAmount(ctx)
	if detailTransaction.Status == constants.CompletedStatus && !transaction.IsAtmFeeTxn() && detailTransaction.TransferType == constants.TransferTypeCharge {
		transactionAmount = FormattedAmountInCents(transaction, detailTransaction.CaptureAmount)
	}

	return transactionAmount
}
