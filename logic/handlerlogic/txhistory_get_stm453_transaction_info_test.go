package handlerlogic

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestGetSTM453TransactionInfoValidator(t *testing.T) {
	t.Run("transactionID-parameter-missing", func(t *testing.T) {
		request := &api.GetSTM453TransactionInfoRequest{}
		expectResponse := &servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
		errorResponse := GetSTM453TransactionDetailRequestValidator(request)
		assert.Equal(t, expectResponse, errorResponse)
	})

	t.Run("valid-request", func(t *testing.T) {
		request := &api.GetSTM453TransactionInfoRequest{
			TransactionID: "14143-SGSG-124",
		}
		errorResponse := GetSTM453TransactionDetailRequestValidator(request)
		assert.Nil(t, errorResponse)
	})
}

func TestGetSTM453TransactionDetail(t *testing.T) {
	scenarios := []struct {
		desc             string
		paymentDBResults []*storage.PaymentDetail
	}{
		{
			desc:             "empty payment data",
			paymentDBResults: []*storage.PaymentDetail{},
		},
		{
			desc: "empty external id",
			paymentDBResults: []*storage.PaymentDetail{
				{
					Metadata: json.RawMessage("{\"externalID\":\"\"}"),
				},
			},
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			mockPaymentDao := &storage.MockIPaymentDetailDAO{}
			mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(scenario.paymentDBResults, nil)
			storage.PaymentDetailD = mockPaymentDao
			request := &api.GetSTM453TransactionInfoRequest{
				TransactionID: "14143-SGSG-124",
			}
			resp, err := GetSTM453TransactionDetail(context.Background(), request)
			assert.Nil(t, err)
			assert.Equal(t, &api.GetSTM453TransactionInfoResponse{}, resp)
		})
	}
}

func TestFetchPaymentDataForSTM453Transaction(t *testing.T) {
	t.Run("empty-payment-data", func(t *testing.T) {
		//mocking
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		request := &api.GetSTM453TransactionInfoRequest{
			TransactionID: "id",
		}
		response := fetchPaymentDataForSTM453Transaction(context.Background(), request)
		assert.Nil(t, response)
	})

	t.Run("happy-path", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataSTM453MockDBRows(), nil)
		storage.PaymentDetailD = mockStorageDAO

		exportedResponse := resources.PaymentDataSTM453MockDBRows()[0]
		request := &api.GetSTM453TransactionInfoRequest{TransactionID: "8880077782"}
		response := fetchPaymentDataForSTM453Transaction(context.Background(), request)
		assert.Equal(t, exportedResponse, response)
	})
}
func TestGetSTM453ResponseGenerator(t *testing.T) {
	locale := utils.GetLocale()
	t.Run("happy-path", func(t *testing.T) {
		response := getSTM453ResponseGenerator(resources.STM453TransactionInfoData(), "fastID123", resources.PaymentDataSTM453MockDBRows()[0])
		expectedResponse := &api.GetSTM453TransactionInfoResponse{
			Amount:               &api.Money{Val: -2000, CurrencyCode: locale.Currency},
			TransactionType:      "Incoming (Rejected)",
			TransactionSubtype:   "ATXN",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			FastTransactionID:    "fastID123",
			SenderAccountID:      "12345",
			ReceiverAccountID:    "54321",
			RecipientBank:        "GXSPSGS0XXX",
		}
		assert.Equal(t, expectedResponse, response)
	})
}
