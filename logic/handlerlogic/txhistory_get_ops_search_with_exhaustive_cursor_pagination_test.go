package handlerlogic

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/require"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/transaction-history/localise"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"gitlab.myteksi.net/dakota/flow"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"

	"github.com/stretchr/testify/assert"
)

func TestFetchTxnWithExhaustiveCursorPagination_validateGetOpsSearchRequest(t *testing.T) {
	scenarios := []struct {
		testDesc        string
		requestParams   *api.GetOpsSearchRequest
		isErrorExpected bool
		expectedError   error
	}{
		{
			testDesc: "happy case",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				Status:    "PROCESSING",
				StartDate: "2023-01-01",
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc: "happy case - identifier",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:     "12345",
				TransactionID: "123xyz",
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc: "happy case - identifier",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:     "12345",
				TransactionID: "123xyz",
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc: "happy case - with status",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:          "12345",
				Status:             "PROCESSING",
				TransactionType:    "TRANSFER_MONEY",
				TransactionSubtype: "INTRABANK",
				StartDate:          "2021-08-01",
				EndDate:            "2021-08-31",
				FromAmount:         100,
				ToAmount:           1000,
				PageSize:           5,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc: "sad case - missing identifier parameter",
			requestParams: &api.GetOpsSearchRequest{
				StartDate: "2021-08-01",
				EndDate:   "2021-08-31",
				PageSize:  2,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrMissingIdentifier.Code), 10), errors.ErrMissingIdentifier.Message),
		},
		{
			testDesc: "sad case - identifier-invalid-combination-accountID,externalID",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:  "12345",
				ExternalID: "123xyz",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidIdentifier.Code), 10), errors.ErrInvalidIdentifier.Message),
		},
		{
			testDesc: "sad case - identifier-invalid-combination-externalID,batchID",
			requestParams: &api.GetOpsSearchRequest{
				ExternalID:    "123xyz",
				TransactionID: "abc123",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidIdentifier.Code), 10), errors.ErrInvalidIdentifier.Message),
		},
		{
			testDesc: "sad case - identifier-invalid-combination-accountID,batchID,txnID",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:     "12345",
				TransactionID: "123xyz",
				BatchID:       "abc123",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidIdentifier.Code), 10), errors.ErrInvalidIdentifier.Message),
		},
		{
			testDesc: "sad case - identifier-invalid-combination-batchID,txnID",
			requestParams: &api.GetOpsSearchRequest{
				TransactionID: "123xyz",
				BatchID:       "abc123",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidIdentifier.Code), 10), errors.ErrInvalidIdentifier.Message),
		},
		{
			testDesc: "sad case - attribute-invalid-status",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				Status:    "PENDING",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnStatus.Code), 10), errors.ErrInvalidTxnStatus.Message),
		},
		{
			testDesc: "sad case - attribute-invalid-txn-type",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:       "12345",
				TransactionType: "SENT_MONEY",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnType.Code), 10), errors.ErrInvalidTxnType.Message),
		},
		{
			testDesc: "sad case - attribute-invalid-txn-subtype",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:          "12345",
				TransactionSubtype: "RPP",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnSubtype.Code), 10), errors.ErrInvalidTxnSubtype.Message),
		},
		{
			testDesc: "sad case - attribute-invalid-amount-range",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:  "12345",
				FromAmount: 1000,
				ToAmount:   100,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnAmountRange.Code), 10), errors.ErrInvalidTxnAmountRange.Message),
		},
		{
			testDesc: "sad case - attribute-negative-fromAmount",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:  "12345",
				FromAmount: -1000,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnAmount.Code), 10), errors.ErrInvalidTxnAmount.Message),
		},
		{
			testDesc: "sad case - attribute-negative-toAmount",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				ToAmount:  -1000,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnAmount.Code), 10), errors.ErrInvalidTxnAmount.Message),
		},
		{
			testDesc: "sad case - page-size-negative",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				PageSize:  -1,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidPageSize.Code), 10), errors.ErrInvalidPageSize.Message),
		},
		{
			testDesc: "sad case - page-size-greater-than-max",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				PageSize:  -1,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidPageSize.Code), 10), errors.ErrInvalidPageSize.Message),
		},
		{
			testDesc: "sad case - page-size-greater-than-max",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				PageSize:  -1,
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidPageSize.Code), 10), errors.ErrInvalidPageSize.Message),
		},
		{
			testDesc: "sad case - filter-invalid-combination-externalID",
			requestParams: &api.GetOpsSearchRequest{
				ExternalID: "12345",
				StartDate:  "2023-01-01",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidFilter.Code), 10), errors.ErrInvalidFilter.Message),
		},
		{
			testDesc: "sad case - filter-invalid-combination-txnID",
			requestParams: &api.GetOpsSearchRequest{
				TransactionID: "12345",
				StartDate:     "2023-01-01",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidFilter.Code), 10), errors.ErrInvalidFilter.Message),
		},
		{
			testDesc: "sad case - filter-invalid-combination-batchID",
			requestParams: &api.GetOpsSearchRequest{
				BatchID:   "12345",
				StartDate: "2023-01-01",
			},
			isErrorExpected: true,
			expectedError:   errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidFilter.Code), 10), errors.ErrInvalidFilter.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			p := &FetchTxnWithExhaustiveCursorPaginationPipe{
				req: s.requestParams,
			}
			err := flow.ExecSeq(context.Background(), p.validateGetOpsSearchRequest())
			if s.isErrorExpected {
				assert.EqualError(t, err, s.expectedError.Error())
			} else {
				assert.NoError(t, err, s.testDesc)
			}
		})
	}
}

func TestFetchTxnWithExhaustiveCursorPagination_fetchData(t *testing.T) {
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})

	finalTxnDataDBPageSize2 := []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}

	finalTxnDataDBPageSize4 := []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		AccountAsset:                "",
	}}

	scenarios := []struct {
		testDesc        string
		requestParams   *api.GetOpsSearchRequest
		isErrorExpected bool
		expectedError   error
		cursorData      dto.PaginationCursor
		finalTxnDataDB  []*storage.TransactionsData
		txnDetailsData  transactionDetails
	}{
		{
			testDesc: "happy-path-firstPage",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "**********",
				StartDate: "2021-08-01",
				EndDate:   "2021-08-31",
				PageSize:  2,
			},
			isErrorExpected: false,
			expectedError:   nil,
			finalTxnDataDB:  finalTxnDataDBPageSize2,
		},
		{
			testDesc: "happy-path-with externalID",
			requestParams: &api.GetOpsSearchRequest{
				ExternalID: "xyz123",
				PageSize:   2,
			},
			isErrorExpected: false,
			expectedError:   nil,
			finalTxnDataDB:  finalTxnDataDBPageSize2,
		},
		{
			testDesc: "happy case - with-pocket-txns",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "**********",
				StartDate: "",
				EndDate:   "",
				PageSize:  2,
			},
			isErrorExpected: false,
			expectedError:   nil,
			finalTxnDataDB:  finalTxnDataDBPageSize4,
		},
		{
			testDesc: "happy case - identifier",
			requestParams: &api.GetOpsSearchRequest{
				AccountID:     "12345",
				TransactionID: "123xyz",
			},
			isErrorExpected: false,
			expectedError:   nil,
			finalTxnDataDB:  finalTxnDataDBPageSize4,
		},
		{
			testDesc: "happy case - with status",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "12345",
				Status:    "PROCESSING",
				StartDate: "2021-08-01",
				EndDate:   "2021-08-31",
				PageSize:  5,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			locale := utils.GetLocale()

			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					DefaultCurrency: locale.Currency,
				},
				Locale: config.Locale{Language: "en"},
				Tenant: "MY",
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{
							MysqlMasterSlaveConfig: data.MysqlMasterSlaveConfig{},
						},
					},
				},
			}
			db, _, err := sqlmock.New()
			if err == nil {
				defer db.Close()
			}

			os.Setenv("LOCALISATION_PATH", "../../localise")
			localise.Init(mockAppConfig)

			defer func() { config.SetTenant("") }()
			config.SetTenant(tenants.TenantMY)

			constants.InitializeDynamicConstants(mockAppConfig)
			presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

			mockStorageDAO := &storage.MockIPaymentDetailDAO{}
			mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
			storage.PaymentDetailD = mockStorageDAO

			mockAccountServiceClient := &accountServiceMock.AccountService{}
			mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)

			mockStore := &storage.MockDatabaseStore{}
			g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)
			mockStore.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
			mockStore.On("GetTxnDataByExternalIDFromDB", mock.Anything, mock.Anything, mock.Anything).Return(
				finalTxnDataDBPageSize4, nil,
			)

			txnDetailsData := transactionDetails{
				paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
				cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
				loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
			}
			p := &FetchTxnWithExhaustiveCursorPaginationPipe{
				req:                  s.requestParams,
				AccountServiceClient: g.AccountServiceClient,
				AppConfig:            g.AppConfig,
				Store:                g.Store,
				accounts:             g.accounts,
				accountsErr:          g.accountsErr,
				responseSerializer:   g.getOpsSearchResponseGenerator,
				txnDetailsData:       txnDetailsData,
			}

			mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
			mockTransactionDataStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return(resources.GetOpsSearchFirstPageMockDBResponse(), nil)
			storage.TransactionsDataD = mockTransactionDataStorageDAO

			// with pocket txn
			mockTransactionDataStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return(resources.OpsSearchPocketTransactions(), nil).Once()
			mockTransactionDataStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return(resources.OpsSearchCounterPartyTransactionEntryForPocketTransactions(), nil)
			storage.TransactionsDataD = mockTransactionDataStorageDAO

			err = flow.ExecSeq(context.Background(), p.fetchData())
			if s.isErrorExpected {
				assert.EqualError(t, err, s.expectedError.Error())
			} else {
				assert.Equal(t, s.finalTxnDataDB, p.finalTxnDataDB)
				assert.NoError(t, err, s.testDesc)
			}
		})
	}
}

func TestFetchTxnWithExhaustiveCursorPagination_fetchWithExhaustivePagination(t *testing.T) {
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	finalTxnData := []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}

	scenarios := []struct {
		testDesc        string
		requestParams   *api.GetOpsSearchRequest
		dbResponse      []*storage.TransactionsData
		dbError         error
		isErrorExpected bool
		expectedError   error
		expectedLength  int
	}{
		{
			testDesc: "Success - Multi-page fetch within cursor limit",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "**********",
				PageSize:  10,
			},
			dbResponse:      finalTxnData,
			dbError:         nil,
			isErrorExpected: false,
			expectedLength:  3,
		},
		{
			testDesc: "Success - PageSize limit reached",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "**********",
				PageSize:  2,
			},
			dbResponse:      finalTxnData,
			dbError:         nil,
			isErrorExpected: false,
			expectedLength:  3,
		},
		{
			testDesc: "error-path-when-transaction-data-db-throws-error",
			requestParams: &api.GetOpsSearchRequest{
				AccountID: "**********",
				StartDate: "",
				EndDate:   "",
				PageSize:  10,
			},
			dbResponse:      nil,
			dbError:         errors.DefaultInternalServerError,
			isErrorExpected: true,
			expectedError:   errors.DefaultInternalServerError,
			expectedLength:  0,
		},
	}

	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		Locale: config.Locale{Language: "en"},
	}

	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	txnDetailsData := transactionDetails{
		paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
		cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
		loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
	}

	for _, scenario := range scenarios {
		s := scenario

		t.Run(scenario.testDesc, func(t *testing.T) {
			fetchPipe := &FetchTxnWithExhaustiveCursorPaginationPipe{
				AccountServiceClient: g.AccountServiceClient,
				AppConfig:            g.AppConfig,
				Store:                g.Store,
				accounts:             g.accounts,
				accountsErr:          g.accountsErr,
				responseSerializer:   g.getOpsSearchResponseGenerator,
				txnDetailsData:       txnDetailsData,
				cursorData:           dto.PaginationCursor{},
				req:                  s.requestParams,
			}

			mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
			if s.isErrorExpected {
				fetchPipe.req = &api.GetOpsSearchRequest{
					AccountID: "**********",
					StartDate: "",
					EndDate:   "",
					PageSize:  10,
				}
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(nil, errors.DefaultInternalServerError)
				storage.TransactionsDataD = mockTransactionDataStorageDAO
				mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows()[3:], nil)
				storage.PaymentDetailD = mockStorageDAO
			} else {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.GetOpsSearchFirstPageMockDBResponse(), nil)
				storage.TransactionsDataD = mockTransactionDataStorageDAO
			}

			// Call the method
			err := fetchPipe.fetchWithExhaustivePagination(context.Background())

			// Check for error expectation
			if s.isErrorExpected {
				assert.Error(t, errors.DefaultInternalServerError, err)
				assert.Equal(t, s.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, s.expectedLength, len(fetchPipe.finalTxnDataDB))
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestFetchTxnWithExhaustiveCursorPagination_prepareResponse(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)
	txnDetailsData := transactionDetails{
		paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
		cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
		loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
	}
	p := &FetchTxnWithExhaustiveCursorPaginationPipe{
		AccountServiceClient: g.AccountServiceClient,
		AppConfig:            g.AppConfig,
		Store:                g.Store,
		accounts:             g.accounts,
		accountsErr:          g.accountsErr,
		responseSerializer:   g.getOpsSearchResponseGenerator,
		txnDetailsData:       txnDetailsData,
	}

	t.Run("happy-path-next-page-exist", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchTransactionsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		p.cursorData = dto.PaginationCursor{}
		err := flow.ExecSeq(context.Background(), p.prepareResponse())

		require.NoError(t, err)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseForListTransaction(), p.resp)
	})

	t.Run("happy-path-no-next-page", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchTransactionsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}

		p.cursorData = dto.PaginationCursor{}
		err := flow.ExecSeq(context.Background(), p.prepareResponse())
		require.NoError(t, err)
		assert.Equal(t, responses.GetOpsSearchOnlyPageResponse(), p.resp)
	})

	t.Run("happy-path-prev-page-scrolling", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    2,
			EndingAfter: "MjAyMS0wOC0yMCAwNzo0MDowMCArMDUzMCBJU1QsMSwz",
		}
		p.cursorData = dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}

		err := flow.ExecSeq(context.Background(), p.prepareResponse())
		expected := responses.GetOpsSearchBackwardScrollingPrevPageResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		}

		p.cursorData = dto.PaginationCursor{}
		require.NoError(t, err)
		assert.Equal(t, expected, p.resp)
	})

	t.Run("happy-path-prev-next-page-exits", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchGetAllTransactionsPrevNextExistMockDBResponse()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
		}
		p.cursorData = dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		err := flow.ExecSeq(context.Background(), p.prepareResponse())
		require.NoError(t, err)
		expected := responses.GetOpsSearchPrevNextBothExistResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		}
		assert.Equal(t, expected, p.resp)
	})

	t.Run("happy-path-no-next-page-with-cards-txn", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchTransactionsCardsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := resources.OpsSearchCardDetailData()
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		p.cursorData = dto.PaginationCursor{}
		err := flow.ExecSeq(context.Background(), p.prepareResponse())
		require.NoError(t, err)
		assert.Equal(t, responses.GetOpsSearchCardsTxnOnlyPageResponse(), p.resp)
	})

	t.Run("happy-path-no-next-page-with-grab-txn", func(t *testing.T) {
		p.finalTxnDataDB = resources.OpsSearchTransactionsGrabDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailGrabData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		p.txnDetailsData = transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		p.req = &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		p.cursorData = dto.PaginationCursor{}
		err := flow.ExecSeq(context.Background(), p.prepareResponse())
		require.NoError(t, err)
		assert.Equal(t, responses.GetOpsSearchGrabTxnOnlyPageResponse(), p.resp)
	})
}
