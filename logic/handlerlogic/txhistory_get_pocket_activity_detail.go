package handlerlogic

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetPocketActivityDetailRequestValidator validates the request parameters
func GetPocketActivityDetailRequestValidator(req *api.GetPocketActivityDetailRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.PocketID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		})
	}
	if req.PocketType == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is a mandatory parameter.",
		})
	}
	if req.PocketType != "" && req.PocketType != constants.SavingsPocketType {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		})
	}
	if req.TransactionID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		})
	}
	return errors
}

// Prepares filter for fetching data from the TransactionData
func prepareDBFilters(req *api.GetPocketActivityDetailRequest, accountID string) []data.Condition {
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("AccountAddress", req.PocketID),
		data.EqualTo("ClientBatchID", req.TransactionID),
	}
	return filters
}

// GetPocketActivityDetail fetches the transactions from the DB and returns the formatted GetPocketActivityDetailResponse.
func (g *GetTransactionDetailStruct) GetPocketActivityDetail(ctx context.Context, req *api.GetPocketActivityDetailRequest, accountID string) (*api.GetPocketActivityDetailResponse, error) {
	filters := prepareDBFilters(req, accountID)
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetPocketActivityDetailHandlerLogTag, fmt.Sprintf("Error getting transaction history data, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyGetPocketActivityDetailResponse(), nil
	}
	return g.getPocketActivityDetailResponseGenerator(ctx, dbResponse[0]), nil
}

// getPocketActivityDetailResponseGenerator : Generates the GetPocketActivityDetail response.
func (g *GetTransactionDetailStruct) getPocketActivityDetailResponseGenerator(ctx context.Context, txnData *storage.TransactionsData) *api.GetPocketActivityDetailResponse {
	transactionsPaymentDetail := make(map[string]*storage.PaymentDetail)
	displayName := getCounterPartyDisplayName(ctx, txnData, make(map[string]*storage.PaymentDetail), nil)
	status := formatDepositsCoreInterest(txnData)
	iconURL := getDefaultIconURL(txnData)
	statusDescription := getStatusDescription(ctx, txnData, transactionsPaymentDetail)
	amountInCents := logic.FormattedAmount(ctx, txnData)
	transactionDescription, _ := generateFormattedDescription(ctx, txnData, nil, displayName)
	batchValueTS := utils.TruncateTillSecond(txnData.BatchValueTimestamp)
	transactionDetail := &api.GetPocketActivityDetailResponse{
		Amount:                 amountInCents,
		Currency:               txnData.TransactionCurrency,
		TransactionDescription: transactionDescription,
		TransactionRemarks:     getTransactionRemarks(ctx, txnData, nil),
		ClientTransactionID:    txnData.ClientBatchID,
		Status:                 status,
		StatusDescription:      statusDescription,
		CounterParty: &api.CounterParty{
			DisplayName: displayName,
			IconURL:     iconURL,
		},
		TransactionTimestamp: &batchValueTS,
	}
	return transactionDetail
}

// Response in case of ActivityDetail not present
func emptyGetPocketActivityDetailResponse() *api.GetPocketActivityDetailResponse {
	response := &api.GetPocketActivityDetailResponse{}
	return response
}
