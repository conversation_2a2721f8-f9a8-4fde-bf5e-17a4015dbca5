package handlerlogic

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetAllCxTransactionsWithExhaustiveCursorPaginationPipe ...
type GetAllCxTransactionsWithExhaustiveCursorPaginationPipe struct {
	req                  *dto.TransactionHistorySearchRequest
	resp                 *dto.TransactionsHistorySearchResponse
	responseSerializer   func(ctx context.Context, dbResponse []*storage.TransactionsData, req *dto.TransactionHistorySearchRequest, cursorData dto.PaginationCursor) (*dto.TransactionsHistorySearchResponse, error)
	AccountServiceClient accountService.AccountService
	AppConfig            *config.AppConfig
	Store                storage.DatabaseStore
	accounts             *accountService.GetAccountResponse
	accountsErr          error
	dbResponse           []*storage.TransactionsData
	// finalTxnDataDB collects all transaction data fetched from the database during pagination,
	// ensuring it is available for further processing and used to generate the final response.
	finalTxnDataDB []*storage.TransactionsData
	txnDetailsData transactionDetails
	cursorData     dto.PaginationCursor
	// defaultStartingBefore defines the starting point for pagination, using the startingBefore value from the request.
	defaultStartingBefore      string
	maxCursorLimit             int
	executionSleepDurationInMs int
}

// FetchTxnWithExhaustiveCursorPagination fetches transactions from the DB, filter and returns a formatted TransactionsHistorySearchResponse.
func (g *GetAccountTransactionsSearchStruct) FetchTxnWithExhaustiveCursorPagination(ctx context.Context, req *dto.TransactionHistorySearchRequest) (*dto.TransactionsHistorySearchResponse, error) {
	p := g.newGetAllCxTransactionsWithExhaustiveCursorPaginationPipe(req)

	steps := flow.Seq(
		p.fetchData(),
		p.prepareResponse(),
	)

	err := flow.ExecSeq(ctx, steps)
	if err != nil {
		return nil, err
	}
	return p.resp, nil
}

func (g *GetAccountTransactionsSearchStruct) newGetAllCxTransactionsWithExhaustiveCursorPaginationPipe(req *dto.TransactionHistorySearchRequest) *GetAllCxTransactionsWithExhaustiveCursorPaginationPipe {
	txnDetailsData := transactionDetails{
		paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
		cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
		loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
	}

	pipe := &GetAllCxTransactionsWithExhaustiveCursorPaginationPipe{
		req:                   req,
		responseSerializer:    g.getCxTransactionsResponseGeneratorForExhaustive, // Pass the function directly
		AccountServiceClient:  g.AccountServiceClient,
		accounts:              g.account,
		accountsErr:           g.accountErr,
		txnDetailsData:        txnDetailsData,
		defaultStartingBefore: req.StartingBefore,
		finalTxnDataDB:        make([]*storage.TransactionsData, 0, req.PageSize+1), // Pre-allocate with PageSize + 1
	}

	// Use the configured value or fallback to the default.
	maxCursorLimit := config.GetMaxCursorLimit()
	if maxCursorLimit != 0 {
		pipe.maxCursorLimit = maxCursorLimit
	} else {
		pipe.maxCursorLimit = defaultMaxCursorLimit
	}

	executionSleepDurationInMs := config.GetExecutionSleepDurationInMs()
	if executionSleepDurationInMs != 0 {
		pipe.executionSleepDurationInMs = executionSleepDurationInMs
	} else {
		pipe.executionSleepDurationInMs = defaultExecutionSleepDurationInMs
	}

	return pipe
}

// fetchData retrieves transaction data from the DB with or without exhaustive pagination.
func (p *GetAllCxTransactionsWithExhaustiveCursorPaginationPipe) fetchData() flow.StepFn {
	return func(ctx context.Context) error {
		var err error
		if p.req.PageSize == MinPageSize {
			p.req.PageSize = DefaultPageSize
		}
		p.cursorData, err = parsePaginationCursorData(p.req.StartingBefore, p.req.EndingAfter)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
			return customErr.DefaultInternalServerError
		}

		slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(p.cursorData)))

		err = p.fetchWithExhaustivePagination(ctx)

		return err
	}
}

// prepareResponse formats the final response after data is retrieved and processed.
func (p *GetAllCxTransactionsWithExhaustiveCursorPaginationPipe) prepareResponse() flow.StepFn {
	return func(ctx context.Context) error {
		if len(p.finalTxnDataDB) == 0 {
			p.resp = emptyGetCxTransactionsHistoryResponse()
			return nil
		}

		var err error
		p.resp, err = p.responseSerializer(ctx, p.finalTxnDataDB, p.req, p.cursorData)
		if err != nil {
			return err
		}
		return nil
	}
}

// fetchWithExhaustivePagination fetches data with cursor pagination logic.
// This function iteratively retrieves paginated data until either:
// - The accumulated *filtered* data exceeds the requested PageSize (indicating a next page), or
// - The database genuinely returns no more raw data, or
// - The maximum cursor pagination limit is reached.
// nolint
func (p *GetAllCxTransactionsWithExhaustiveCursorPaginationPipe) fetchWithExhaustivePagination(ctx context.Context) error {
	pagesFetched := 0
	// Local variable to manage the cursor for starting the next page fetch.
	// Initialized to the defaultStartingBefore value for the request.
	startingBeforeCursor := p.defaultStartingBefore
	// Local copy of the cursor data to allow modifications during the pagination process.
	cursorData := p.cursorData
	for {
		//param := mapGetOpsSearchRequestToOpsSearchParam(p.req, true)
		filters := cxTransactionListDBFilters(p.req, cursorData, true)
		// Fetch paginated data from the database using the current filters.
		err := p.paginatedFetch(ctx, filters)
		if err != nil {
			return err
		}

		slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Size of p.finalTxnDataDB: %d", len(p.finalTxnDataDB)))

		// If the accumulated data exceeds the requested PageSize.
		if len(p.finalTxnDataDB) > int(p.req.PageSize) {
			// Truncate the data to the requested PageSize + 1 for indicating there is more data.
			p.finalTxnDataDB = p.finalTxnDataDB[:p.req.PageSize+1]
			// Assign startingBeforeCursor back to p.defaultStartingBefore to use it when preparing the response.
			startingBeforeCursor = p.defaultStartingBefore
			slog.FromContext(ctx).Debug(constants.GetOpsSearchLogTag, fmt.Sprintf("Condition met: finalTxnDataDB length (%d) exceeds PageSize (%d)", len(p.finalTxnDataDB), int(p.req.PageSize)))
			break
		}

		// If it is last page or the pages fetched more than cursor limit
		maxCursorLimit := p.maxCursorLimit
		if len(p.dbResponse) <= int(p.req.PageSize) || pagesFetched >= maxCursorLimit {
			// Assign startingBeforeCursor back to p.defaultStartingBefore to use it when preparing the response.
			startingBeforeCursor = p.defaultStartingBefore
			slog.FromContext(ctx).Debug(constants.GetOpsSearchLogTag, fmt.Sprintf("Condition met: dbResponse length (%d) <= PageSize (%d) or pagesFetched (%d) >= MaxCursorLimit (%d)", len(p.dbResponse), int(p.req.PageSize), pagesFetched, maxCursorLimit))
			break
		}

		// Update the cursorData with details of the last transaction in the current response.
		lastTxn := p.dbResponse[len(p.dbResponse)-1]
		cursorData = dto.PaginationCursor{
			ID:                 int64(lastTxn.ID),
			TransactionID:      lastTxn.ClientTransactionID,
			Date:               lastTxn.BatchValueTimestamp.Format(CursorDataTimeLayout),
			FirstTransactionID: cursorData.FirstTransactionID,
		}

		// Prepare pagination parameters for the next iteration using the updated cursorData.
		paginationParams := utils.MapPaginationParameters(p.req.AccountID, startingBeforeCursor, p.req.EndingAfter, p.req.StartDate, p.req.EndDate, p.req.PageSize)
		// Determine the next cursor position based on the current response and pagination parameters.
		_, nextCursorID := nextPageLink(p.dbResponse, paginationParams, cursorData)

		// Update the local startingBeforeCursor for the next iteration.
		startingBeforeCursor = nextCursorID
		pagesFetched++
		slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Current pagesFetched: %d", pagesFetched))

		// Short pause between pagination requests to prevent tight looping
		// Pause for the specified duration
		time.Sleep(time.Duration(p.executionSleepDurationInMs) * time.Millisecond)

		// Finally update the request's StartingBefore field once at the end to reflect the final cursor position after pagination.
		// This ensures subsequent operations or response preparation can use the correct state.
		p.cursorData = cursorData
		p.req.StartingBefore = startingBeforeCursor
	}

	p.req.StartingBefore = startingBeforeCursor
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag,
		fmt.Sprintf("Exhaustive pagination finished. Final accumulated filtered data size: %d, Next cursor for response: %s",
			len(p.finalTxnDataDB), p.req.StartingBefore))
	return nil
}

// paginatedFetch executes a paginated fetch, appending fetched data to finalTxnDataDB.
func (p *GetAllCxTransactionsWithExhaustiveCursorPaginationPipe) paginatedFetch(ctx context.Context, filters []data.Condition) error {
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error getting data from transactionsData DB, err: %s", err.Error()))
		return customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("No data present in the database for the account: %s", p.req.AccountID))
		return nil
	}
	p.dbResponse = dbResponse
	currentFilteredResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, p.req)

	p.finalTxnDataDB = append(p.finalTxnDataDB, currentFilteredResponse...)

	return nil
}
