package handlerlogic

import (
	"context"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// GetCASAInterestEarnedRequestValidator validates the request parameters
func GetCASAInterestEarnedRequestValidator(req *api.GetCASAInterestEarnedRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}
	return errors
}

// GetCASAInterestEarned gets the total interest earned for the casa account
func GetCASAInterestEarned(ctx context.Context, req *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error) {
	var totalInterestEarned *api.Money
	var err error
	featFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featFlags != nil && featFlags.IsInterestAggregateV2Enabled() {
		totalInterestEarned, err = getTotalInterestEarnedV2(ctx, req.AccountID, constants.DefaultAccountAddress)
	} else {
		totalInterestEarned, err = getTotalInterestEarned(ctx, req.AccountID, constants.DefaultAccountAddress)
	}
	if err != nil {
		return nil, err
	}
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: totalInterestEarned,
	}, nil
}
