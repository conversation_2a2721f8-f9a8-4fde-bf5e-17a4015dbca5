package handlerlogic

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestValidateSafeID(t *testing.T) {
	t.Run("safeID present", func(t *testing.T) {
		safeID := "test-safe-id"
		errors := ValidateSafeID(safeID)
		assert.Equal(t, len(errors), 0)
	})
	t.Run("safeID missing", func(t *testing.T) {
		safeID := ""
		errors := ValidateSafeID(safeID)
		assert.Equal(t, errors, []servus.ErrorDetail{
			{
				Message: "`safeID` is a mandatory field",
			},
		})
	})
}
