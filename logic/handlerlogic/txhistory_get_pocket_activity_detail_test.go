package handlerlogic

import (
	"context"
	"errors"
	"os"
	"testing"

	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestGetPocketActivityDetailRequestValidator(t *testing.T) {
	t.Run("pocketID-missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketType:    "SAVINGS",
			TransactionID: "test-txn-id",
		}

		expectedError := servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		}
		errorResponse := GetPocketActivityDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("pocketType-missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "1234000",
			TransactionID: "test-txn-id",
		}

		expectedError := servus.ErrorDetail{
			Message: "'pocketType' is a mandatory parameter.",
		}
		errorResponse := GetPocketActivityDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("pocketType-invalid", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "1234000",
			PocketType:    "abc",
			TransactionID: "test-txn-id",
		}

		expectedError := servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		}
		errorResponse := GetPocketActivityDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("transactionID-missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
		}

		expectedError := servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
		errorResponse := GetPocketActivityDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
}

func TestGetPocketActivityDetail(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "id"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("resource-present-in-DB", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id1",
		}
		expectedResp := responses.GetPocketActivityDetailResponseBahasa()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.PocketTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetPocketActivityDetail(context.Background(), request, "**********")
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("no-resource-present-in-DB", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id1",
		}
		expectedResp := &api.GetPocketActivityDetailResponse{}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetPocketActivityDetail(context.Background(), request, "**********")
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("error-on-fetching-from-DB", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id1",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetPocketActivityDetail(context.Background(), request, "**********")
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}
