package handlerlogic

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	transactionStatements "gitlab.myteksi.net/dbmy/transaction-statements/api"
)

// GetAccountCalendarActivities contains logic to return list of month and year with non-zero transfers for an account.
func GetAccountCalendarActivities(ctx context.Context, accountList []string, enquiryDate time.Time, pastMonthsThresholdForCalendarActivity int) ([]api.Date, error) {
	startYear := enquiryDate.AddDate(0, -pastMonthsThresholdForCalendarActivity, 0).Year()
	filters := []data.Condition{
		data.ContainedIn("AccountID", utils.ConvertToInterface(accountList)...),
		data.GreaterThanOrEqualTo("Year", startYear),
		data.DescendingOrder("Year"),
	}
	matchResult, err := storage.AccountCalendarActivityD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAccountCalendarActivitiesLogicLogTag,
			fmt.Sprintf("Error getting calendar activity row, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}

	if len(matchResult) == 0 || err == data.ErrNoData {
		return []api.Date{}, nil
	}

	response := mapCalendarActivityToResponse(matchResult, enquiryDate, pastMonthsThresholdForCalendarActivity)
	return response, nil
}

// mapCalendarActivityToResponse transforms AccountCalendarActivity records to the required response format
func mapCalendarActivityToResponse(matchResult []*storage.AccountCalendarActivity, enquiryDate time.Time, pastMonthsThresholdForCalendarActivity int) []api.Date {
	activityList := make(map[string][]string)
	var activityListKeys []int
	for _, yearlyActivity := range matchResult {
		year := strconv.FormatInt(yearlyActivity.Year, 10)
		months := strings.Split(yearlyActivity.Months, ",")

		if _, exists := activityList[year]; exists {
			activityList[year] = append(activityList[year], months...)
		} else {
			activityList[year] = months
			activityListKeys = append(activityListKeys, int(yearlyActivity.Year))
		}
	}
	startDate := enquiryDate.AddDate(0, -pastMonthsThresholdForCalendarActivity, 0)
	startYear := startDate.Year()
	startMonth := startDate.Format(utils.TimestampMonthFormat)

	response := []api.Date{}
	for _, year := range activityListKeys {
		months := utils.GetUniqueStringSliceInDescendingOrder(activityList[strconv.Itoa(year)])
		if year == startYear {
			months = filterOutMonthsBeforeStartMonth(months, startMonth)
		}
		if len(months) <= 0 {
			continue
		}
		responseSet := api.Date{
			Year:   strconv.Itoa(year),
			Months: months,
		}
		response = append(response, responseSet)
	}

	return response
}

// FilterAccountCalanderActivities ...
func FilterAccountCalanderActivities(ctx context.Context, response []api.Date, enquiryDate time.Time, accountID string, isLending bool) []api.Date {
	// Remove the current month from the response if it's present. As the entries are fetched in descending order
	// of year, current month will always be present at the start of the response array.
	if shouldRemoveCurrentMonth(response, enquiryDate) {
		response[0].Months = response[0].Months[1:]
		// If there are no other months with transactions, remove the entry for the year from response
		if len(response[0].Months) == 0 {
			response = response[1:]
		}
	}

	// Remove the prev month from the response if statement is not generated yet. As the entries are fetched in descending order
	// of year, prev month will always be present at the start of the response array.
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	if !isLending && featFlag != nil && featFlag.IsCheckStatementStatusEnabled() && isStatementExistForPrevMonth(ctx, response, enquiryDate, accountID) {
		response[0].Months = response[0].Months[1:]
		// If there are no other months with transactions, remove the entry for the year from response
		if len(response[0].Months) == 0 {
			response = response[1:]
		}
	}

	return response
}

func filterOutMonthsBeforeStartMonth(months []string, startMonth string) []string {
	for index, month := range months {
		if startMonth > month {
			months = months[:index]
			break
		}
	}
	return months
}

func shouldRemoveCurrentMonth(response []api.Date, enquiryDate time.Time) bool {
	return len(response) > 0 && response[0].Year == strconv.Itoa(enquiryDate.Year()) && response[0].Months[0] == enquiryDate.Format(utils.TimestampMonthFormat)
}

func isStatementExistForPrevMonth(ctx context.Context, response []api.Date, enquiryDate time.Time, accountID string) bool {
	isStatementExist := false
	prevMonthDate := enquiryDate.AddDate(0, -1, 0)

	// Example:
	// the response is [2023: [11, 10]] and the enquiry date is 2024 Jan 6
	// it will go compare the enquiry day (6) is it same or smaller than the config day (7)
	// if no then it will not run the following code
	// else it will go check the prev month year (2023) same with the response (2023)
	// then it will check the prev month (12) is it same with the response (11),
	// if not same it will not run the following codes.

	if len(response) > 0 && enquiryDate.Day() <= config.GetStatementReadyByDays() && response[0].Year == strconv.Itoa(prevMonthDate.Year()) && response[0].Months[0] == prevMonthDate.Format(utils.TimestampMonthFormat) {
		getTransactionStatementMetadataRequest := transactionStatements.GetTransactionStatementMetadataRequest{
			AccountID: accountID,
			Month:     response[0].Months[0],
			Year:      response[0].Year,
		}
		statements := Dependencies.TransactionStatementsClient
		ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, commonCtx.GetUserID(ctx))
		ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, commonCtx.GetServiceID(ctx))
		_, err := statements.GetAccountTransactionsStatementMetadata(ctx, &getTransactionStatementMetadataRequest)
		if err != nil {
			clientErr, ok := err.(*errorhandling.Error)
			if ok && clientErr.HTTPCode != 404 {
				slog.FromContext(ctx).Warn(constants.GetAccountCalendarActivitiesLogicLogTag,
					fmt.Sprintf("Unexpected error getting statement metadata, err: %s", err.Error()))
			}
			isStatementExist = true
			return isStatementExist
		}

		return isStatementExist
	}
	return isStatementExist
}
