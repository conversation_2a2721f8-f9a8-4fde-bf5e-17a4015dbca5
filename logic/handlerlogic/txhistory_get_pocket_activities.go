package handlerlogic

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetPocketActivitiesRequestValidator validates the request parameters
func GetPocketActivitiesRequestValidator(req *api.GetPocketActivitiesRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.PocketID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		})
	}
	if req.PocketType == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is a mandatory parameter.",
		})
	}
	if req.PocketType != "" && req.PocketType != constants.SavingsPocketType {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		})
	}
	if req.PageSize > MaxPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pageSize' greater than maxPageSize.",
		})
	}
	if req.PageSize < MinPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pageSize' less than minPageSize.",
		})
	}

	return errors
}

// GetPocketActivitiesFromDB filters and fetches the pocket activities for the specified pocket fom the db.
func GetPocketActivitiesFromDB(ctx context.Context, req *api.GetPocketActivitiesRequest, accountID string) (*api.GetPocketActivitiesResponse, error) {
	if req.PageSize == MinPageSize {
		req.PageSize = DefaultPageSize
	}
	// parsing cursor if present
	cursorData, err := parsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(cursorData)))
	// creating DB filters
	filters := pocketTransactionListDBFilters(req, cursorData, accountID)
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error getting transactions history, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	// when no transactions found
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyGetPocketActivitiesResponse(), nil
	}
	return getPocketActivitiesResponseGenerator(ctx, dbResponse, req, cursorData), nil
}

// Returns the response for the GetPocketActivities API
func getPocketActivitiesResponseGenerator(ctx context.Context, dbResponse []*storage.TransactionsData, req *api.GetPocketActivitiesRequest, cursorData dto.PaginationCursor) *api.GetPocketActivitiesResponse {
	// method to filter out committed and pending.
	finalDBResponse := filterPocketActivities(dbResponse, req)

	if len(finalDBResponse) == 0 {
		return emptyGetPocketActivitiesResponse()
	}

	var transactionsList []api.TransactionHistoryResponse
	transactionsPerPage := utils.MinInt(int(req.PageSize), len(finalDBResponse))

	// invert final DB entries to show them in descending order
	if req.EndingAfter != "" {
		finalDBResponse = invertDBResponseForBackwardScrolling(finalDBResponse[:transactionsPerPage])
	}

	//iterating over all transactions
	for _, transaction := range finalDBResponse[:transactionsPerPage] {
		counterPartyDisplayName := getDisplayNameForPocketTransaction(transaction)
		status := formatDepositsCoreInterest(transaction)
		amountInCents := logic.FormattedAmount(ctx, transaction)
		iconURL := getDefaultIconURL(transaction)
		// format to response structure
		transactionsList = append(transactionsList, api.TransactionHistoryResponse{
			TransactionID:     transaction.ClientBatchID,
			DisplayName:       counterPartyDisplayName,
			IconURL:           iconURL,
			Amount:            amountInCents,
			Currency:          transaction.TransactionCurrency,
			Status:            status,
			CreationTimestamp: utils.TruncateTillSecond(transaction.BatchValueTimestamp),
		})
	}
	paginationParams := utils.MapPaginationParameters(req.PocketID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := paginationLinks(finalDBResponse, paginationParams, cursorData)
	return &api.GetPocketActivitiesResponse{Links: links, Data: transactionsList}
}

// For pocket transactions we will have interest payouts, fund addition and fund withdrawal transactions present
func getDisplayNameForPocketTransaction(transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string
	// For Interest payout transactions
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, transaction.TransactionType) {
		counterPartyDisplayName = constants.InterestPayoutDisplayName
		return counterPartyDisplayName
	}
	// For fund adding transactions
	if constants.PocketFundingTransactionSubType == transaction.TransactionSubtype {
		counterPartyDisplayName = constants.FundsAddedDisplayName
		return counterPartyDisplayName
	}
	// For fund withdrawal transactions
	if constants.PocketWithdrawalTransactionSubType == transaction.TransactionSubtype {
		counterPartyDisplayName = constants.FundsWithdrawnDisplayName
		return counterPartyDisplayName
	}
	return counterPartyDisplayName
}

// filterPocketActivities this method will fetch the finalDBResponse based on the pageSize
// For pocket transactions we can ignore the filtering based on the AccountPhase and Type as there will be only POSTING_PHASE_COMMITTED for each transaction of an account address.
func filterPocketActivities(dbResponse []*storage.TransactionsData, req *api.GetPocketActivitiesRequest) []*storage.TransactionsData {
	filteredDBResponse := filterOutIncomingFailedAndCancelledTransfer(dbResponse, req.PocketID)

	// Storing clientBatchID to maintain the order in which fetched from DB
	clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(filteredDBResponse)

	// computing number of rows to be added
	filteredRowsCount := computeNumberOfTransactionsInFilteredDBResponse(req.StartingBefore, req.EndingAfter, req.PageSize)

	// Select transactions as per required pageSize
	finalDBResponse, _ := createFinalDBTransactionList(clientBatchIDOrder, clientBatchIDToTransactionsDataMap, filteredRowsCount)
	return finalDBResponse
}

func pocketTransactionListDBFilters(req *api.GetPocketActivitiesRequest, cursorData dto.PaginationCursor, accountID string) []data.Condition {
	// add default filters
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("AccountAddress", req.PocketID),
		data.Limit(int(4 * req.PageSize)),
		data.NotContainedIn("TransactionType", utils.ConvertToInterface(constants.InterestAccrualTransactionTypes)...), // Not to show InterestAccrual Tx.
	}

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters, data.DescendingOrder("BatchValueTimestamp"))
	case req.EndingAfter != "":
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("BatchValueTimestamp"),
			data.UnEqualTo("ClientBatchID", cursorData.TransactionID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("BatchValueTimestamp"),
			data.UnEqualTo("ClientBatchID", cursorData.TransactionID),
		)
	}

	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, cursorData)
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("BatchValueTimestamp", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("BatchValueTimestamp", endingDate))
	}
	return filters
}

// emptyGetPocketActivitiesResponse will generate empty response structure in case no matching transactions are found
func emptyGetPocketActivitiesResponse() *api.GetPocketActivitiesResponse {
	response := &api.GetPocketActivitiesResponse{}
	response.Links = map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""}
	response.Data = []api.TransactionHistoryResponse{}
	return response
}
