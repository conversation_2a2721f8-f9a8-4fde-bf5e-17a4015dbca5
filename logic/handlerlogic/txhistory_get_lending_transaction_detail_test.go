package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	pairingServiceMock "gitlab.myteksi.net/dakota/payment/pairing-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetLendingTransactionDetailsValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &api.GetLendingTransactionDetailRequest{
			TransactionID: "dummy",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		}

		errorResponse := GetLendingTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("transactionID-parameter-missing", func(t *testing.T) {
		request := &api.GetLendingTransactionDetailRequest{
			AccountID: "12345",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
		errorResponse := GetLendingTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("valid-request", func(t *testing.T) {
		request := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "14143-SGSG-124",
		}
		errorResponse := GetLendingTransactionDetailRequestValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})
}

func TestGetLendingTransactionDetail(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockPairingServiceClient := &pairingServiceMock.PairingService{}
	mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(nil, nil)
	mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			LendingRepayment:   "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			LendingDrawdown:    "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	g := GetLendingTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient, PairingServiceClient: mockPairingServiceClient}

	t.Run("resource-present-in-DB", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetLendingTransactionDetailResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsDataMockDBRowsForLending()[1]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRowsForLendingTransaction(constants.CompletedStatus), nil)
		storage.PaymentDetailD = mockPaymentDao

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[0]}, nil)
		storage.LoanDetailD = mockLoanDetailDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("failed-transaction-present-in-DB", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetDrawdownTransactionDetailResponseForLending(constants.FailedStatus)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.AuthFailedDrawdownTransactionsForLending(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRowsForLendingTransaction(constants.CompletedStatus), nil)
		storage.PaymentDetailD = mockPaymentDao

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.FailedLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)
		storage.LoanDetailD = mockLoanDetailDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("recovery-transaction-present-in-DB", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "efg1111abd",
		}
		expectedResp := responses.GetLendingTransactionDetailResponse()[3]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, data.ErrNoData).Once()
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRowsForLendingTransaction(constants.CompletedStatus), nil)
		storage.PaymentDetailD = mockPaymentDao

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[2]}, nil)
		storage.LoanDetailD = mockLoanDetailDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("no-transaction-data-present-in-DB", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := customErr.BuildErrorResponse(customErr.ResourceNotFound, "No transaction data found")

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, data.ErrNoData)
		storage.TransactionsDataD = mockTransactionData
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.LoanDetailD = mockLoanDetailDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, expectedResp, err)
		assert.Nil(t, actualResp)
	})

	t.Run("no-payments-data-in-db", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := customErr.BuildErrorResponse(customErr.ResourceNotFound, "No payment data found")

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.PaymentDetailD = mockPaymentDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, expectedResp, err)
		assert.Nil(t, actualResp)
	})

	t.Run("no-loan-data-in-db", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := customErr.BuildErrorResponse(customErr.ResourceNotFound, "No loan detail data found")

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.LoanDetailD = mockLoanDetailDao

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, expectedResp, err)
		assert.Nil(t, actualResp)
	})

	t.Run("error-on-fetching-from-DB", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}

func TestGetLendingTransactionDetailsResponseGenerator(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockPairingServiceClient := &pairingServiceMock.PairingService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			LendingRepayment:   "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			LendingDrawdown:    "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(resources.GetAccountImageDetailsSampleResponse(), nil)
	mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)
	g := GetLendingTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient, PairingServiceClient: mockPairingServiceClient}

	txnData := resources.TransactionsMockDBRowsForLending()
	mockPaymentDao := &storage.MockIPaymentDetailDAO{}
	mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRowsForLendingTransaction(constants.CompletedStatus), nil)
	storage.PaymentDetailD = mockPaymentDao

	mockLoanDetailDao := &storage.MockILoanDetailDAO{}
	mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[2]}, nil)
	storage.LoanDetailD = mockLoanDetailDao

	req := &api.GetLendingTransactionDetailRequest{
		AccountID:     "12345",
		TransactionID: "abc123efg",
	}

	t.Run("transaction-detail-repayment", func(t *testing.T) {
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[2]}, nil)
		storage.LoanDetailD = mockLoanDetailDao
		response, _ := g.getLendingTransactionDetailResponse(context.Background(), txnData[1], req, nil)
		expectedResponse := responses.GetLendingTransactionDetailResponse()[1]
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("transaction-detail-overPaid-repayment", func(t *testing.T) {
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[1]}, nil)
		storage.LoanDetailD = mockLoanDetailDao
		response, _ := g.getLendingTransactionDetailResponse(context.Background(), txnData[1], req, nil)
		expectedResponse := responses.GetLendingTransactionDetailResponse()[2]
		assert.Equal(t, expectedResponse, response)
	})
}

func Test_generateMonthlyRepaymentDescriptions(t *testing.T) {
	type args struct {
		installment map[int64]*api.Money
	}
	tests := []struct {
		name     string
		args     args
		expected []string
	}{
		{
			name: "should return descriptions if non empty and in order",
			args: args{installment: map[int64]*api.Money{
				2: {
					CurrencyCode: "MYR",
					Val:          100,
				},
				1: {
					CurrencyCode: "MYR",
					Val:          99,
				},
			}},
			expected: []string{"2 monthly payments of RM1", "1 final payment of RM0.99"},
		}, {
			name: "should return descriptions if non empty and not in order",
			args: args{installment: map[int64]*api.Money{1: {
				CurrencyCode: "MYR",
				Val:          99,
			},
				2: {
					CurrencyCode: "MYR",
					Val:          100,
				},
			}},
			expected: []string{"2 monthly payments of RM1", "1 final payment of RM0.99"},
		},
		{
			name: "should return descriptions if non empty and single payment",
			args: args{installment: map[int64]*api.Money{1: {
				CurrencyCode: "MYR",
				Val:          99,
			}}},
			expected: []string{"1 final payment of RM0.99"},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			descriptions := generateMonthlyRepaymentDescriptions(test.args.installment)
			assert.Equal(t, test.expected, descriptions)
		})
	}
}

func Test_fetchRepaymentDetails(t *testing.T) {
	tests := []struct {
		name             string
		repaymentDetails *dto.RepaymentDetailsDTO
		expected         *api.RepaymentDetails
	}{
		{
			name: "should return paymentForInstallments when there is single payment with no savings",
			repaymentDetails: &dto.RepaymentDetailsDTO{
				TotalRepaymentAmount:        decimal.NewFromFloat(100.0),
				IsTotalInterestSaved:        false,
				TotalInterestSaved:          decimal.NewFromFloat(0.0),
				IsTotalPenalInterestCharged: false,
				PenalInterestCharged:        decimal.NewFromFloat(0.0),
				LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
					LoanName:                "loan name",
					AccountID:               "101",
					RepaymentAmount:         decimal.NewFromFloat(100.0),
					Currency:                "MYR",
					TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
					TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
					IsNormalInterestSaved:   false,
					NormalInterestSave:      decimal.NewFromFloat(0.0),
					IsPenalInterestCharged:  false,
					PenalInterestCharged:    decimal.NewFromFloat(0.0),
				}},
				Currency: "MYR",
			},
			expected: &api.RepaymentDetails{
				RepaymentID: "loanTxnID",
				TransferID:  "transferID",
				PaymentForInstalments: &api.PaymentForInstalments{
					TotalInterestSaved: &api.Money{
						CurrencyCode: "MYR",
						Val:          0,
					},
					RepaymentSummary: []api.RepaymentSummary{{
						LoanName: "loan name",
						PaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						RemainingPayableBeforePayment: &api.Money{
							CurrencyCode: "MYR",
							Val:          20000,
						},
						RemainingPayable: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						InterestSavedAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          0,
						},
					}},
				},
				PaymentForOverdue:     nil,
				LastRepaymentOverpaid: nil,
			},
		},
		{
			name: "should return paymentForInstallments when there are multiple payment with and without savings",
			repaymentDetails: &dto.RepaymentDetailsDTO{
				TotalRepaymentAmount:        decimal.NewFromFloat(201.0),
				IsTotalInterestSaved:        true,
				TotalInterestSaved:          decimal.NewFromFloat(1.0),
				IsTotalPenalInterestCharged: false,
				PenalInterestCharged:        decimal.NewFromFloat(0.0),
				LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
					LoanName:                "loan name",
					AccountID:               "101",
					RepaymentAmount:         decimal.NewFromFloat(100.0),
					Currency:                "MYR",
					TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
					TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
					IsNormalInterestSaved:   false,
					NormalInterestSave:      decimal.NewFromFloat(0.0),
					IsPenalInterestCharged:  false,
					PenalInterestCharged:    decimal.NewFromFloat(0.0),
				},
					{
						LoanName:                "loan name",
						AccountID:               "102",
						RepaymentAmount:         decimal.NewFromFloat(101.0),
						Currency:                "MYR",
						TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
						TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
						IsNormalInterestSaved:   true,
						NormalInterestSave:      decimal.NewFromFloat(1.0),
						IsPenalInterestCharged:  false,
						PenalInterestCharged:    decimal.NewFromFloat(0.0),
					}},
				Currency: "MYR",
			},
			expected: &api.RepaymentDetails{
				RepaymentID: "loanTxnID",
				TransferID:  "transferID",
				PaymentForInstalments: &api.PaymentForInstalments{
					TotalInterestSaved: &api.Money{
						CurrencyCode: "MYR",
						Val:          100,
					},
					RepaymentSummary: []api.RepaymentSummary{{
						LoanName: "loan name",
						PaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						RemainingPayableBeforePayment: &api.Money{
							CurrencyCode: "MYR",
							Val:          20000,
						},
						RemainingPayable: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						InterestSavedAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          0,
						},
					},
						{
							LoanName: "loan name",
							PaidAmount: &api.Money{
								CurrencyCode: "MYR",
								Val:          10100,
							},
							RemainingPayableBeforePayment: &api.Money{
								CurrencyCode: "MYR",
								Val:          20000,
							},
							RemainingPayable: &api.Money{
								CurrencyCode: "MYR",
								Val:          10000,
							},
							InterestSavedAmount: &api.Money{
								CurrencyCode: "MYR",
								Val:          100,
							},
						},
					},
				},
				PaymentForOverdue:     nil,
				LastRepaymentOverpaid: nil,
			},
		},
		{
			name: "should return paymentForOverdue when there is single payment with overdue payment",
			repaymentDetails: &dto.RepaymentDetailsDTO{
				TotalRepaymentAmount:        decimal.NewFromFloat(101.0),
				IsTotalInterestSaved:        false,
				TotalInterestSaved:          decimal.NewFromFloat(0.0),
				IsTotalPenalInterestCharged: true,
				PenalInterestCharged:        decimal.NewFromFloat(1.0),
				LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
					LoanName:                "loan name",
					AccountID:               "101",
					RepaymentAmount:         decimal.NewFromFloat(101.0),
					Currency:                "MYR",
					TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
					TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
					IsNormalInterestSaved:   false,
					NormalInterestSave:      decimal.NewFromFloat(0.0),
					IsPenalInterestCharged:  true,
					PenalInterestCharged:    decimal.NewFromFloat(1.0),
				}},
				Currency: "MYR",
			},
			expected: &api.RepaymentDetails{
				RepaymentID:           "loanTxnID",
				TransferID:            "transferID",
				PaymentForInstalments: nil,
				PaymentForOverdue: &api.PaymentForOverdue{
					TotalOverdueInterestPaid: &api.Money{
						CurrencyCode: "MYR",
						Val:          100,
					},
					RepaymentSummary: []api.RepaymentSummary{{
						LoanName: "loan name",
						PaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          10100,
						},
						RemainingPayableBeforePayment: &api.Money{
							CurrencyCode: "MYR",
							Val:          20000,
						},
						RemainingPayable: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						OverdueInterestPaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          100,
						},
					}},
				},
				LastRepaymentOverpaid: nil,
			},
		},
		{
			name: "should return paymentForInstallments and paymentForOverdue when there are multiple payment with savings and overdue",
			repaymentDetails: &dto.RepaymentDetailsDTO{
				TotalRepaymentAmount:        decimal.NewFromFloat(202.0),
				IsTotalInterestSaved:        true,
				TotalInterestSaved:          decimal.NewFromFloat(1.0),
				IsTotalPenalInterestCharged: true,
				PenalInterestCharged:        decimal.NewFromFloat(1.0),
				LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
					LoanName:                "loan name",
					AccountID:               "101",
					RepaymentAmount:         decimal.NewFromFloat(101.0),
					Currency:                "MYR",
					TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
					TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
					IsNormalInterestSaved:   false,
					NormalInterestSave:      decimal.NewFromFloat(0.0),
					IsPenalInterestCharged:  true,
					PenalInterestCharged:    decimal.NewFromFloat(1.0),
				},
					{
						LoanName:                "loan name",
						AccountID:               "102",
						RepaymentAmount:         decimal.NewFromFloat(101.0),
						Currency:                "MYR",
						TotalDueBeforeRepayment: decimal.NewFromFloat(200.0),
						TotalDueAfterRepayment:  decimal.NewFromFloat(100.0),
						IsNormalInterestSaved:   true,
						NormalInterestSave:      decimal.NewFromFloat(1.0),
						IsPenalInterestCharged:  false,
						PenalInterestCharged:    decimal.NewFromFloat(0.0),
					}},
				Currency: "MYR",
			},
			expected: &api.RepaymentDetails{
				RepaymentID: "loanTxnID",
				TransferID:  "transferID",
				PaymentForInstalments: &api.PaymentForInstalments{
					TotalInterestSaved: &api.Money{
						CurrencyCode: "MYR",
						Val:          100,
					},
					RepaymentSummary: []api.RepaymentSummary{{
						LoanName: "loan name",
						PaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          10100,
						},
						RemainingPayableBeforePayment: &api.Money{
							CurrencyCode: "MYR",
							Val:          20000,
						},
						RemainingPayable: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						InterestSavedAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          100,
						},
					}},
				},
				PaymentForOverdue: &api.PaymentForOverdue{
					TotalOverdueInterestPaid: &api.Money{
						CurrencyCode: "MYR",
						Val:          100,
					},
					RepaymentSummary: []api.RepaymentSummary{{
						LoanName: "loan name",
						PaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          10100,
						},
						RemainingPayableBeforePayment: &api.Money{
							CurrencyCode: "MYR",
							Val:          20000,
						},
						RemainingPayable: &api.Money{
							CurrencyCode: "MYR",
							Val:          10000,
						},
						OverdueInterestPaidAmount: &api.Money{
							CurrencyCode: "MYR",
							Val:          100,
						},
					}},
				},
				LastRepaymentOverpaid: nil,
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			repaymentDetails := fetchRepaymentDetails(test.repaymentDetails, "loanTxnID", "transferID")
			assert.Equal(t, repaymentDetails, test.expected)
		})
	}
}

func Test_getAccountDetails(t *testing.T) {
	account := json.RawMessage([]byte("{\"number\": \"***********\", \"fullName\": \"\", \"pairingID\": \"\", \"swiftCode\": \"\", \"displayName\": \"\", \"proxy_object\": {}}\t"))
	counterParty := json.RawMessage([]byte("{\"number\": \"*********\", \"fullName\": \"John Doe\", \"pairingID\": \"\", \"swiftCode\": \"ABNASGSGXXX\", \"displayName\": \"John Doe\", \"proxy_object\": {}}\t"))
	data := &storage.PaymentDetail{
		Amount:              1000,
		Account:             account,
		CounterPartyAccount: counterParty,
	}
	expected := dto.AccountDetail{
		PairingID:   "",
		Number:      "*********",
		SwiftCode:   "ABNASGSGXXX",
		DisplayName: "John Doe",
		FullName:    "John Doe",
		Proxy:       dto.ProxyObject{},
	}
	accountDetails := getAccountDetails(context.Background(), data)
	assert.Equal(t, accountDetails, expected)
}
