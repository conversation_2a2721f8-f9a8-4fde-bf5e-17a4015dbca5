package handlerlogic

import (
	"context"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// GetPocketInterestEarnedRequestValidator validates the request parameters
func GetPocketInterestEarnedRequestValidator(req *api.GetPocketInterestEarnedRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.PocketID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		})
	}
	if req.PocketType == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is a mandatory parameter.",
		})
	}
	if req.PocketType != "" && req.PocketType != constants.SavingsPocketType {
		errors = append(errors, servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		})
	}
	return errors
}

// GetPocketValidator validates the pocket in the request parameters
func GetPocketValidator(accountID string) error {
	if accountID == "" {
		return customErr.BuildErrorResponse(customErr.BadRequest, "The pocket in the request is invalid")
	}
	return nil
}

// GetPocketInterestEarned returns the interest earned in the specified pocket.
func GetPocketInterestEarned(ctx context.Context, req *api.GetPocketInterestEarnedRequest, accountID string) (*api.GetPocketInterestEarnedResponse, error) {
	totalInterestEarned, err := getTotalInterestEarned(ctx, accountID, req.PocketID)
	if err != nil {
		return nil, err
	}
	return &api.GetPocketInterestEarnedResponse{
		TotalInterestEarned: totalInterestEarned,
	}, nil
}
