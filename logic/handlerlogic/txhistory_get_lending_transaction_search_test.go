package handlerlogic

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetLendingTransactionSearchValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}
		expectedError := servus.ErrorDetail{
			Message: "`accountID` is a mandatory field",
		}
		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})

	t.Run("happy-path", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}
		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})

	t.Run("page-size-negative", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			PageSize:  -1,
		}

		expectedError := servus.ErrorDetail{
			Message: "`pageSize` less than minPageSize",
		}

		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})

	t.Run("page-size-greater-than-max", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			PageSize:  MaxPageSize + 1,
		}
		expectedError := servus.ErrorDetail{
			Message: "`pageSize` greater than maxPageSize",
		}

		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})

	t.Run("date-time-input-request", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}
		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})

	t.Run("date-time-input-wrong-format-request", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2022-10-1 00:00:00",
			EndDate:   "2022-10-12 00:00:00",
			PageSize:  2,
		}
		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 2, len(errorResponse))
	})

	t.Run("format-mismatch-request", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			PageSize:  2,
			StartDate: "2022-10-01",
			EndDate:   "2022-10-12",
		}
		errorResponse := GetLendingTransactionSearchValidator(request)
		assert.Equal(t, 2, len(errorResponse))
	})
}

func TestGetTransactionsResponseGeneratorForLending(t *testing.T) {
	scenarios := []struct {
		desc                   string
		dbRows                 []*storage.TransactionsData
		req                    *dto.TransactionHistorySearchRequest
		cursorData             dto.PaginationCursor
		expectedResponse       *api.GetLendingTransactionSearchResponse
		prevPageTxDataFindResp []*storage.TransactionsData
		prevPageTxDataFindErr  error
	}{
		{
			desc:   "happy-path-next-page-exist",
			dbRows: resources.TransactionsDataMockDBRowsForLending(),
			req: &dto.TransactionHistorySearchRequest{
				AccountID: "**********",
				StartDate: "2022-10-01T00:00:00Z",
				EndDate:   "2022-10-12T15:00:00Z",
				PageSize:  1,
			},
			cursorData:       dto.PaginationCursor{},
			expectedResponse: responses.GetAllTransactionsFirstPageResponseForLending(),
		},
		{
			desc:   "happy-path-prev-next-page-exits",
			dbRows: resources.GetAllTransactionsPrevNextExistMockDBResponseForLending(),
			req: &dto.TransactionHistorySearchRequest{
				AccountID:      "**********",
				StartDate:      "2021-08-01T00:00:00Z",
				EndDate:        "2021-08-31T00:00:00Z",
				PageSize:       1,
				StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
			},
			cursorData: dto.PaginationCursor{
				ID:                 1,
				FirstTransactionID: 3,
				Date:               "2021-08-20 07:40:00 +0530",
				TransactionID:      uuid.NewString(),
			},
			expectedResponse: responses.GetAllTransactionsPrevNextBothExistResponseForLending(),
		},
		{
			desc:   "happy-path-no-next-page",
			dbRows: resources.TransactionsDataMockDBRowsForLending(),
			req: &dto.TransactionHistorySearchRequest{
				AccountID: "**********",
				StartDate: "2021-08-01T00:00:00Z",
				EndDate:   "2021-08-31T00:00:00Z",
				PageSize:  5,
			},
			cursorData:       dto.PaginationCursor{},
			expectedResponse: responses.GetAllTransactionsNoNextPageResponseForLending(),
		},
	}
	for _, s := range scenarios {
		s := s
		t.Run(s.desc, func(t *testing.T) {
			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					DefaultCurrency: "MYR",
				},
				IconConfig: config.IconConfig{
					DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					LendingRepayment:   "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
					LendingDrawdown:    "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
				},
			}
			constants.InitializeDynamicConstants(mockAppConfig)
			// mocking PaymentDetail Call
			mockPaymentDetailDAO := &storage.MockIPaymentDetailDAO{}
			mockPaymentDetailDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
			storage.PaymentDetailD = mockPaymentDetailDAO

			mockTransactionDataDAO := &storage.MockITransactionsDataDAO{}
			mockTransactionDataDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.prevPageTxDataFindResp, s.prevPageTxDataFindErr)
			storage.TransactionsDataD = mockTransactionDataDAO

			loanDetailsMap := createLoanTxIDToLoanDetailMap(resources.LoanDetailsMockDBRowsForTxnSearch())

			mockAccountServiceClient := &accountServiceMock.AccountService{}
			mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(nil, nil, nil)
			g := GetLendingTransactionsSearchImpl{AccountServiceClient: mockAccountServiceClient}
			response, _ := g.getTransactionsResponseGenerator(context.Background(), s.dbRows, s.req, s.cursorData, loanDetailsMap)
			assert.Equal(t, s.expectedResponse, response)
		})
	}
}

func Test_GetAllTransactionsFromDBForLending(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			LendingRepayment:   "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			LendingDrawdown:    "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockLoanDetailDao := &storage.MockILoanDetailDAO{}
	mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailsMockDBRowsForTxnSearch(), nil)
	storage.LoanDetailD = mockLoanDetailDao

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(resources.GetAccountImageDetailsSampleResponse(), nil)
	g := GetLendingTransactionsSearchImpl{AccountServiceClient: mockAccountServiceClient}
	constants.InitializeDynamicConstants(mockAppConfig)

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := &api.GetLendingTransactionSearchResponse{
			Links: map[string]string{"next": "", "nextCursorID": ""},
			Data:  []api.LendingTransactionSearchData{},
		}
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  1,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLending(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsFirstPageResponseForLending()
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-firstPage-for-writeOff-repayment", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingDrawDown(), nil).Once()
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		storage.LoanDetailD = mockLoanDetailDao
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything).Return(resources.GetLoanDetailsByAccountID(), nil).Once()

		expectedResponse := responses.GetAllTransactionsIncludingLendingRecovery()
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-firstPage-for-writeOff-and-normal-repayment", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  4,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingDrawDownAndRecovery(), nil).Once()
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		storage.LoanDetailD = mockLoanDetailDao
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything).Return(resources.GetLoanDetailsByAccountID(), nil).Once()

		expectedResponse := responses.GetAllTransactionsIncludingLendingRecovery()
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-firstPage-for-repayment", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingRepayment(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[1]}, nil)
		storage.LoanDetailD = mockLoanDetailDao

		expectedResponse := responses.GetAllTransactionsFirstPageResponseForLendingRepayment()
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-firstPage-when-no-loan-data", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingRepayment(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.LoanDetailD = mockLoanDetailDao

		expectedResponse := &api.GetLendingTransactionSearchResponse{
			Links: map[string]string{"next": "", "nextCursorID": "", "prev": "", "prevCursorID": ""},
		}
		response, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("error-with-db", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("error"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		_, err := g.GetAllTransactionsFromDBForLending(context.Background(), request)
		assert.Error(t, err)
	})
}

func createLoanTxIDToLoanDetailMap(loanDetails []*storage.LoanDetail) map[string]*storage.LoanDetail {
	loanTxIDToLoanDetailMap := make(map[string]*storage.LoanDetail)
	for _, loanDetail := range loanDetails {
		loanTxIDToLoanDetailMap[loanDetail.LoanTransactionID] = loanDetail
	}
	return loanTxIDToLoanDetailMap
}

func Test_fetchPreviousPageLastTxDataIfCursorExist(t *testing.T) {
	transactionID, accountID := uuid.NewString(), uuid.NewString()
	dummyErr := errors.New("some error")
	scenarios := []struct {
		desc            string
		cursorData      dto.PaginationCursor
		req             *dto.TransactionHistorySearchRequest
		findResp        []*storage.TransactionsData
		findErr         error
		expectedResp    *storage.TransactionsData
		expectedErr     error
		isErrorExpected bool
	}{
		{
			desc:       "cursor is not present, should not return error, should return nil as response",
			cursorData: dto.PaginationCursor{},
			req:        &dto.TransactionHistorySearchRequest{AccountID: accountID},
		},
		{
			desc:            "error to fetch db data, should return error, should return nil as response",
			cursorData:      dto.PaginationCursor{TransactionID: transactionID},
			req:             &dto.TransactionHistorySearchRequest{AccountID: accountID},
			findErr:         dummyErr,
			expectedErr:     dummyErr,
			isErrorExpected: true,
		},
		{
			desc:       "no matching data in db, should not return error, should return nil as response",
			cursorData: dto.PaginationCursor{TransactionID: transactionID},
			req:        &dto.TransactionHistorySearchRequest{AccountID: accountID},
			findErr:    data.ErrNoData,
		},
		{
			desc:         "happy case, should not return error, should return transactions data db object",
			cursorData:   dto.PaginationCursor{TransactionID: transactionID},
			req:          &dto.TransactionHistorySearchRequest{AccountID: accountID},
			findResp:     []*storage.TransactionsData{{ClientBatchID: transactionID, AccountID: accountID}},
			expectedResp: &storage.TransactionsData{ClientBatchID: transactionID, AccountID: accountID},
		},
	}
	for _, s := range scenarios {
		s := s
		t.Run(s.desc, func(t *testing.T) {
			mockStorageDAO := &storage.MockITransactionsDataDAO{}
			mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.findResp, s.findErr)
			storage.TransactionsDataD = mockStorageDAO

			resp, err := fetchPreviousPageLastTxDataIfCursorExist(context.Background(), s.cursorData, s.req)
			if s.isErrorExpected {
				assert.Equal(t, s.expectedErr.Error(), err.Error())
				assert.Nil(t, resp)
			} else {
				assert.Nil(t, err)
				if s.expectedResp != nil {
					assert.Equal(t, s.expectedResp.AccountID, resp.AccountID)
					assert.Equal(t, s.expectedResp.ClientBatchID, resp.ClientBatchID)
				} else {
					assert.Nil(t, resp)
				}
			}
		})
	}
}
