package handlerlogic

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
)

func TestParsePaginationCursorData(t *testing.T) {
	t.Run("no-cursor-passed", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{}
		expectedResponse := dto.PaginationCursor{}
		response, err := parsePaginationCursorData(request.StartingBefore, request.EndingAfter)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			EndingAfter: "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGE0MWQ4YjViY2ExMTRlMTNiZWNlNjAxY2Y1MjlkYzhm",
		}
		expectedResponse := responses.DecodeCursorHappyPathResponse()
		response, err := parsePaginationCursorData(request.StartingBefore, request.EndingAfter)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("error-path", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			EndingAfter: "randomCursorString123",
		}
		expectedResponse := dto.PaginationCursor{}
		response, err := parsePaginationCursorData(request.StartingBefore, request.EndingAfter)

		assert.Error(t, err, errors.New("Received unexpected error: \nillegal base64 data at input byte 20"))
		assert.Equal(t, expectedResponse, response)
	})
}

func TestEncodeCursor(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		request := dto.PaginationCursor{
			Date:               time.Unix(1629425400, 0).UTC().Format("2006-01-02T15:04:05Z07:00"),
			ID:                 1,
			FirstTransactionID: 3,
			TransactionID:      "a41d8b5bca114e13bece601cf529dc8f",
		}
		expectedResponse := responses.EncodeCursorHappyPathResponse()
		response := encodeCursor(request)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestDecodeCursor(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		request := responses.EncodeCursorHappyPathResponse()
		expectedResponse := responses.DecodeCursorHappyPathResponse()
		response, err := decodeCursor(request)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("error-path", func(t *testing.T) {
		request := "randomCursorErrorString"
		response, err := decodeCursor(request)

		assert.Error(t, err, errors.New("Received unexpected error: \nillegal base64 data at input byte 20"))
		assert.Equal(t, dto.PaginationCursor{}, response)
	})
}

func TestNextPageLink(t *testing.T) {
	t.Run("has-next-page", func(t *testing.T) {
		dbResponse := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{FirstTransactionID: dbResponse[0].ID}
		expectedNextLink, expectedNextCursorID := responses.NextPageExistResponse()
		paginationParameters := dto.PaginationParameters{
			AccountID:      request.AccountID,
			StartingBefore: request.StartingBefore,
			StartDate:      request.StartDate,
			EndingAfter:    request.EndingAfter,
			EndDate:        request.EndDate,
			PageSize:       request.PageSize,
		}
		nextLink, nextCursorID := nextPageLink(dbResponse, paginationParameters, cursorData)
		assert.Equal(t, expectedNextLink, nextLink)
		assert.Equal(t, expectedNextCursorID, nextCursorID)
	})

	t.Run("no-next-page", func(t *testing.T) {
		dbResponse := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{FirstTransactionID: dbResponse[0].ID}
		paginationParameters := dto.PaginationParameters{
			AccountID:      request.AccountID,
			StartingBefore: request.StartingBefore,
			StartDate:      request.StartDate,
			EndingAfter:    request.EndingAfter,
			EndDate:        request.EndDate,
			PageSize:       request.PageSize,
		}
		nextLink, nextCursorID := nextPageLink(dbResponse, paginationParameters, cursorData)
		assert.Equal(t, "", nextLink)
		assert.Equal(t, "", nextCursorID)
	})
}

func TestPrevPageLink(t *testing.T) {
	t.Run("has-prev-page", func(t *testing.T) {
		dbResponse := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       2,
			StartingBefore: "RandomCursorString",
		}
		cursorData := dto.PaginationCursor{FirstTransactionID: dbResponse[1].ID}
		expectedPrevLink, expectedPrevCursorID := responses.PrevPageExistResponse()
		paginationParameters := dto.PaginationParameters{
			AccountID:      request.AccountID,
			StartingBefore: request.StartingBefore,
			StartDate:      request.StartDate,
			EndingAfter:    request.EndingAfter,
			EndDate:        request.EndDate,
			PageSize:       request.PageSize,
		}
		prevLink, prevCursorID := prevPageLink(dbResponse, paginationParameters, cursorData)
		assert.Equal(t, expectedPrevLink, prevLink)
		assert.Equal(t, expectedPrevCursorID, prevCursorID)
	})

	t.Run("no-prev-page", func(t *testing.T) {
		dbResponse := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{FirstTransactionID: dbResponse[0].ID}
		paginationParameters := dto.PaginationParameters{
			AccountID:      request.AccountID,
			StartingBefore: request.StartingBefore,
			StartDate:      request.StartDate,
			EndingAfter:    request.EndingAfter,
			EndDate:        request.EndDate,
			PageSize:       request.PageSize,
		}
		prevLink, prevCursorID := prevPageLink(dbResponse, paginationParameters, cursorData)
		assert.Equal(t, "", prevLink)
		assert.Equal(t, "", prevCursorID)
	})
}
