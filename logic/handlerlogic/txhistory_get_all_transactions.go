package handlerlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/presenters/cardtransactionpresenter"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/presenters/loantransactionpresenter"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/presenters/transactionpresenter"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

const (
	// DefaultPageSize ...
	DefaultPageSize = 10
	// MaxPageSize ...
	MaxPageSize = 250
	// MinPageSize ...
	MinPageSize = 0
)

// GetAccountTransactionsSearchStruct ...
type GetAccountTransactionsSearchStruct struct {
	AccountServiceClient accountService.AccountService
	account              *accountService.GetAccountResponse
	accountErr           error
}

// GetAccountOnce ...
func (g *GetAccountTransactionsSearchStruct) GetAccountOnce(ctx context.Context, accountID string) (*accountService.GetAccountResponse, error) {
	if g.account != nil || g.accountErr != nil {
		return g.account, g.accountErr
	}
	acc, err := g.AccountServiceClient.GetAccountDetailsByAccountID(ctx, &accountService.GetAccountRequest{
		AccountID:    accountID,
		FetchBalance: false,
	})
	g.account = acc
	g.accountErr = err
	return acc, err
}

// GetTransactionListValidator validates the request parameters
func GetTransactionListValidator(req *api.GetTransactionsHistoryRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "`accountID` is a mandatory field",
		})
	}

	if req.PageSize > MaxPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "`pageSize` greater than maxPageSize",
		})
	}

	if req.PageSize < MinPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "`pageSize` less than minPageSize",
		})
	}

	return errors
}

// GetAllTransactionsFromDB : prepare filters, fetch transactions and format response
func (g *GetAccountTransactionsSearchStruct) GetAllTransactionsFromDB(ctx context.Context, req *api.GetTransactionsHistoryRequest, transactionLimit SearchTransactionLimit) (*api.GetTransactionsHistoryResponse, error) {
	if req.PageSize == MinPageSize {
		req.PageSize = DefaultPageSize
	}
	// parsing cursor if present
	cursorData, err := parsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(cursorData)))

	// creating DB filters
	filters := transactionListDBFilters(ctx, req, cursorData, transactionLimit)
	// dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	anyRes, err := storage.FindReplicaConditionally(ctx, storage.TransactionsDataD, filters, storage.ReplicaByCallerIdentity(commonCtx.GetClientIdentity(ctx)))
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error getting transactions history, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	dbResponse, _ := anyRes.([]*storage.TransactionsData)

	// when no transactions found
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyGetTransactionsHistoryResponse(), nil
	}
	return g.getTransactionsResponseGenerator(ctx, dbResponse, req, cursorData), nil
}

// transactionListDBFilters creates filter condition
// nolint: dupl
func transactionListDBFilters(ctx context.Context, req *api.GetTransactionsHistoryRequest, cursorData dto.PaginationCursor, transactionLimit SearchTransactionLimit) []data.Condition {
	// add default filters
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.Limit(int(4 * req.PageSize)),
		data.NotContainedIn("TransactionType", utils.ConvertToInterface(
			constants.InterestAccrualTransactionTypes)...),
		data.NotContainedIn("TransactionType", utils.ConvertToInterface(
			constants.TaxAccrualTransactionTypes)...),
	}

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters,
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"))
	case req.EndingAfter != "":
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("BatchValueTimestamp"),
			data.AscendingOrder("ID"),
			data.UnEqualTo("ClientTransactionID", cursorData.TransactionID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"),
			data.UnEqualTo("ClientTransactionID", cursorData.TransactionID),
		)
	}

	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, cursorData)
	if transactionLimit.ShouldOverrideStartingDate(ctx, startingDate) {
		startingDate = transactionLimit.MinStartDate.Format(CursorDataTimeLayout)
	}
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("BatchValueTimestamp", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("BatchValueTimestamp", endingDate))
	}
	return filters
}

// getTransactionsResponseGenerator contains logic to generate response from DB data after filtering.
// assumption: dbResponse accountID is same as req.AccountID
func (g *GetAccountTransactionsSearchStruct) getTransactionsResponseGenerator(ctx context.Context, dbResponse []*storage.TransactionsData, req *api.GetTransactionsHistoryRequest, cursorData dto.PaginationCursor) *api.GetTransactionsHistoryResponse {
	// method to filter out committed and pending.
	finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)

	if len(finalDBResponse) == 0 {
		return emptyGetTransactionsHistoryResponse()
	}

	// fetch corresponding payment details
	transactionsPaymentDetail := fetchPaymentDetail(ctx, clientBatchTxIDs.PaymentBatchIDs, req)
	// fetch corresponding payment details
	transactionsCardDetail := fetchCardDetail(ctx, clientBatchTxIDs.CardBatchIDs, req)
	// fetch corresponding lending details
	transactionsLendingDetail := fetchLoanDetail(ctx, clientBatchTxIDs.LoanBatchIDs, req)

	var transactionsList []api.TransactionHistoryResponse
	transactionsPerPage := utils.MinInt(int(req.PageSize), len(finalDBResponse))

	// invert final DB entries to show them in descending order
	if req.EndingAfter != "" {
		finalDBResponse = invertDBResponseForBackwardScrolling(finalDBResponse[:transactionsPerPage])
	}

	// fetch pocketID to name map if required
	pocketIDToNameMap := g.getBoostPocketIDToNameMap(ctx, req.AccountID, finalDBResponse)

	for _, transaction := range finalDBResponse[:transactionsPerPage] {
		isChildAccount, _ := g.isChildAccount(ctx, transaction.AccountID)
		isCASAAccount, _ := g.isCASAAccount(ctx, transaction.AccountID)
		var transactionDetail *api.TransactionHistoryResponse
		switch {
		case transaction.TransactionDomain == constants.DebitCardDomain && !transaction.IsCardOpsTxn() && !transaction.IsRewardsTxn() && !transaction.IsCardMaintenanceFeeTxn():
			transactionDetail = cardtransactionpresenter.GenerateCardTxnResponse(ctx, transaction, transactionsCardDetail)
		case transaction.TransactionDomain == constants.LendingDomain || transaction.TransactionDomain == constants.BizLendingDomain:
			transactionDetail = loantransactionpresenter.GenerateLoanCasaTxnResponse(ctx, transaction, isCASAAccount, transactionsLendingDetail)
		default:
			transactionDetail = transactionpresenter.GeneratePaymentCasaTxnResponse(ctx, transaction, isChildAccount, transactionsPaymentDetail, pocketIDToNameMap)
		}
		if transactionDetail != nil {
			transactionsList = append(transactionsList, *transactionDetail)
		}
	}
	paginationParams := utils.MapPaginationParameters(req.AccountID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := paginationLinks(finalDBResponse, paginationParams, cursorData)
	return &api.GetTransactionsHistoryResponse{Links: links, Data: transactionsList}
}

//nolint:dupl
func (g *GetAccountTransactionsSearchStruct) getBoostPocketIDToNameMap(ctx context.Context, accountIDInReq string, dbResponse []*storage.TransactionsData) map[string]string {
	ff := featureflag.FeatureFlagsFromContext(ctx)
	if ff == nil || !ff.IsBoostPocketNameQueryEnabled() {
		return nil
	}

	if !g.isPocketDetailsRequired(ctx, accountIDInReq) {
		return nil
	}

	pocketIDs := getBoostPocketIDsFromDBResponse(ctx, dbResponse, constants.GetAllTransactionsLogicLogTag)
	pocketDetails, err := helper.ListPocketDetails(ctx, pocketIDs, g.AccountServiceClient, constants.GetAllTransactionsLogicLogTag)
	if err != nil {
		// error logged internally
		return nil
	}

	return transactionpresenter.BuildPocketIDToNameMap(ctx, pocketDetails, constants.GetAllTransactionsLogicLogTag)
}

func getBoostPocketIDsFromDBResponse(ctx context.Context, dbResponse []*storage.TransactionsData, logTag string) []string {
	pocketIDSet := make(map[string]struct{})

	for _, txn := range dbResponse {
		isTxnCodeForTxnsBetweenCASAAndBP := txn.TransactionDomain == constants.DepositsDomain && txn.TransactionType == constants.TransferMoneyTxType &&
			(txn.TransactionSubtype == constants.PocketFundingTransactionSubType || txn.TransactionSubtype == constants.PocketWithdrawalTransactionSubType)
		if !isTxnCodeForTxnsBetweenCASAAndBP {
			continue
		}

		batchDetails, err := txn.GetBatchDetails(ctx, logTag) // errors logged internally
		if err != nil {
			continue
		}

		if batchDetails["sub_account_type"] != constants.BoostPocket {
			continue
		}

		// extract pocketID from batch details
		pocketIDSet[batchDetails["pocket_id"]] = struct{}{}
	}

	return lo.Keys(pocketIDSet)
}

func (g *GetAccountTransactionsSearchStruct) isPocketDetailsRequired(ctx context.Context, accountIDInReq string) bool {
	// get account details to check if the accountID in the request is the main account
	acc, err := g.GetAccountOnce(ctx, accountIDInReq)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, "Error fetching account details", slog.Error(err))
		return false
	}
	if acc == nil || acc.Account == nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, "Account details not found")
		return false
	}

	if utils.IsChildAccount(acc) {
		// this is a call from FE pocket activity list screen,
		// we don't need to fill counterparty name with pocket name
		return false
	}

	return true
}

// fetchPaymentDetail gets payment's metadata related to transaction.
// nolint: dupl
func fetchPaymentDetail(ctx context.Context, clientBatchTxIDs []string, req *api.GetTransactionsHistoryRequest) map[string]*storage.PaymentDetail {
	response := make(map[string]*storage.PaymentDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.ContainedIn("TransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil || len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, paymentDetail := range dbResponse {
		response[paymentDetail.TransactionID] = paymentDetail
	}
	return response
}

// fetchCardDetail gets card's metadata related to transaction.
// nolint: dupl
func fetchCardDetail(ctx context.Context, clientBatchTxIDs []string, req *api.GetTransactionsHistoryRequest) map[string]*storage.CardTransactionDetail {
	response := make(map[string]*storage.CardTransactionDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.ContainedIn("CardTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.CardTransactionDetailD.Find(ctx, filters...)
	if err != nil || len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, detail := range dbResponse {
		response[detail.CardTransactionID] = detail
	}
	return response
}

// fetchLoanDetail gets lending's metadata related to transaction.
// nolint: dupl
func fetchLoanDetail(ctx context.Context, clientBatchTxIDs []string, req *api.GetTransactionsHistoryRequest) map[string]*storage.LoanDetail {
	response := make(map[string]*storage.LoanDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in loan detail table
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.ContainedIn("PaymentTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.LoanDetailD.FindOnSlave(ctx, filters...)
	if err != nil || len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, detail := range dbResponse {
		response[detail.PaymentTransactionID] = detail
	}
	return response
}

// filterTransactionsOnAccountPhaseAndType this method will ensure only one entry per batch will be present
// Logic (** ORDER MATTERS **): Based on
// https://wiki.grab.com/pages/viewpage.action?spaceKey=Digibank&title=Product+requirements+for+Transaction+History%2C+Transaction+Detail+and+Statements+for+R2
// nolint: dupl, gocognit
func filterTransactionsOnAccountPhaseAndType(dbResponse []*storage.TransactionsData, req *api.GetTransactionsHistoryRequest) ([]*storage.TransactionsData, dto.ClientTypesBatchIDs) {
	// Filter out all incoming failed and cancelled transfer, processing will be filtered out by below loop
	filteredDBResponse := filterOutIncomingFailedAndCancelledTransfer(dbResponse, constants.DefaultAccountAddress)

	// Filter out apply earmark failed transfer, processing will be filtered out by below loop
	filteredDBResponse = filterOutApplyEarmarkFailedTransfer(filteredDBResponse, constants.DefaultAccountAddress)

	// Filter out settled or released card txns
	filteredDBResponse = filterOutSettledOrReleasedCardTxn(filteredDBResponse, constants.DefaultAccountAddress)

	// Storing clientBatchID to maintain the order in which fetched from DB
	clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(filteredDBResponse)

	// Filtering bases on AccountPhase
	for clientBatchID, transactions := range clientBatchIDToTransactionsDataMap {
		var committedPostingTx, pendingOutGoingTx, releaseTx []*storage.TransactionsData
		for _, item := range transactions {
			if item.AccountAddress != constants.DefaultAccountAddress {
				continue
			}
			switch item.AccountPhase {
			case constants.PostingPhaseCommitted:
				committedPostingTx = append(committedPostingTx, item)
			case constants.PostingPhasePendingOutgoing:
				if item.TmTransactionType == constants.OutboundAuthorisation {
					pendingOutGoingTx = append(pendingOutGoingTx, item)
				} else if item.TmTransactionType == constants.Release && (item.TransactionType == constants.ReleaseEarmarkTransactionType || item.TransactionType == constants.NewCardIssuanceFeeTransactionType) {
					releaseTx = append(releaseTx, item)
				}
			}
		}
		clientBatchIDToTransactionsDataMap[clientBatchID] = []*storage.TransactionsData{}

		switch {
		case committedPostingTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = committedPostingTx
		case releaseTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = releaseTx
		case pendingOutGoingTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = pendingOutGoingTx
		}
		slog.FromContext(context.Background()).Info(constants.GetAllTransactionsLogicLogTag,
			fmt.Sprintf("database row selected for batchID: %s, transaction to show: %s", clientBatchID,
				strings.Join(
					lo.Map(clientBatchIDToTransactionsDataMap[clientBatchID], func(item *storage.TransactionsData, _ int) string {
						return strconv.FormatUint(item.ID, 10)
					}), ",")),
		)
	}

	// computing number of rows to be added
	filteredRowsCount := computeNumberOfTransactionsInFilteredDBResponse(req.StartingBefore, req.EndingAfter, req.PageSize)

	// Select transactions as per required pageSize
	finalDBResponse, filteredClientBatchIDs := createFinalDBTransactionList(clientBatchIDOrder, clientBatchIDToTransactionsDataMap, filteredRowsCount)
	return finalDBResponse, filteredClientBatchIDs
}

// emptyGetTransactionsHistoryResponse will generate empty response structure in case no matching txns is found
func emptyGetTransactionsHistoryResponse() *api.GetTransactionsHistoryResponse {
	response := &api.GetTransactionsHistoryResponse{}
	response.Links = map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""}
	response.Data = []api.TransactionHistoryResponse{}
	return response
}

func (g *GetAccountTransactionsSearchStruct) isChildAccount(ctx context.Context, accountID string) (bool, error) {
	accountDetail, err := g.GetAccountOnce(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("error while fetching account details: %s", err.Error()))
		return false, err
	}
	return accountDetail.Account.ParentAccountID != "", nil
}

func (g *GetAccountTransactionsSearchStruct) isCASAAccount(ctx context.Context, accountID string) (bool, error) {
	accountDetail, err := g.GetAccountOnce(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("error while fetching account details: %s", err.Error()))
		return false, err
	}
	return accountDetail.Account.ProductVariantID == constants.DepositsAccount, nil
}

func (g *GetAccountTransactionsSearchStruct) listCounterPartyNameForOpsTransactions(ctx context.Context, transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string
	isChild, _ := g.isChildAccount(ctx, transaction.AccountID)
	if isChild {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	} else {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	}
	return counterPartyDisplayName
}

func (g *GetAccountTransactionsSearchStruct) listCounterPartyDisplayNameForPocketDBMY(ctx context.Context, transaction *storage.TransactionsData, batchDetails map[string]string) string {
	var counterPartyDisplayName string
	isChild, _ := g.isChildAccount(ctx, transaction.AccountID)
	if isChild {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = localise.Translate(constants.MoneyAdded)
		} else {
			counterPartyDisplayName = localise.Translate(constants.MoneyWithdrawn)
		}
	} else {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		} else {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		}
	}
	return counterPartyDisplayName
}

// nolint: dupl
func (g *GetAccountTransactionsSearchStruct) listCounterPartyDisplayNamePocketView(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail) string {
	var counterPartyDisplayName string
	var batchDetails map[string]string

	err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
	}
	// For DBMY Pocket View
	switch config.GetTenant() {
	case tenants.TenantMY:
		if utils.SearchStringArray(constants.OpsInitiateTransactionType, transaction.TransactionType) {
			counterPartyDisplayName = g.listCounterPartyNameForOpsTransactions(ctx, transaction)
		} else if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyDisplayName = g.listCounterPartyDisplayNameForPocketDBMY(ctx, transaction, batchDetails)
		}
	}
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}
	return getCounterPartyDisplayName(ctx, transaction, transactionsPaymentDetail, nil)
}
