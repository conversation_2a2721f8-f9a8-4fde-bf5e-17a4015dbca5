package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/common/tenants"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetTransactionDetailsValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &api.GetTransactionDetailRequest{
			TransactionID: "sdgagsdgs",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		}

		errorResponse := GetTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("transactionID-parameter-missing", func(t *testing.T) {
		request := &api.GetTransactionDetailRequest{
			AccountID: "12345",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
		errorResponse := GetTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("valid-request", func(t *testing.T) {
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "14143-SGSG-124",
		}
		errorResponse := GetTransactionDetailRequestValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})
}

func TestGetTransactionDetail(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	mockAppConfig := &config.AppConfig{
		IconConfig: config.IconConfig{
			TransferOut: "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		Locale: config.Locale{Language: "en"},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)

	t.Run("resource-present-in-DB", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(resources.GetAccountDetailsByAccountIDResponseParent(), nil)
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("should return child account name in source of fund when child account is debited", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[2]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
		s := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		actualResp, err := s.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("no-resource-present-in-DB", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := &api.GetTransactionDetailResponse{}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("should return source of fund as empty when transaction is of not outbound", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "1444",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		transactionsData := resources.TransactionsMockDBRows()[0]
		transactionsData.DebitOrCredit = constants.CREDIT
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{transactionsData}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao
		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, actualResp.SourceOfFund, "")
		assert.Nil(t, err)
	})
	t.Run("error-on-fetching-from-DB", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
	t.Run("should return source of fund as empty when account fetch is giving error", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(nil, errors.New("some error "))
		s := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		actualResp, err := s.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, "", actualResp.SourceOfFund)
		assert.Nil(t, err)
	})
	t.Run("multiple-entries-txn-db", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := responses.GetInterestEarnedTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.InterestPayoutTransactionsMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.PaymentDetailD = mockPaymentDao

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("empty-finalDb-response", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := &api.GetTransactionDetailResponse{}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PendingIncomingTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
}

func TestFilterTxnOnAccountPhaseAndType(t *testing.T) {
	t.Run("release-transaction", func(t *testing.T) {
		dbResponse := resources.ReleaseTransactions()
		expectedResponse := (*storage.TransactionsData)(nil)
		finalDBResponse := filterTxnOnAccountPhaseAndType(dbResponse)
		assert.Equal(t, expectedResponse, finalDBResponse)
	})
	t.Run("committed-settlement-transaction", func(t *testing.T) {
		dbResponse := resources.CommittedSettlementTransactions()
		expectedResponse := resources.CommittedSettlementTransactions()[2]
		finalDBResponse := filterTxnOnAccountPhaseAndType(dbResponse)
		assert.Equal(t, expectedResponse, finalDBResponse)
	})
	t.Run("pending-outbound-authorisation-transaction", func(t *testing.T) {
		dbResponse := resources.PendingOutboundAuthTransactions()
		expectedResponse := resources.PendingOutboundAuthTransactions()[0]
		finalDBResponse := filterTxnOnAccountPhaseAndType(dbResponse)
		assert.Equal(t, expectedResponse, finalDBResponse)
	})
}

func TestGetStatusDescription(t *testing.T) {
	t.Run("description-present", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[3]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "Rejected due to account is closed", response)
	})
	t.Run("reason-present-description-not-present", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[3]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "ACCOUNT_VIOLATION_ACCOUNT_NOT_PRESENT", response)
	})
	t.Run("description-not-present", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[1]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "", response)
	})
	t.Run("Interest-payout-rejected-reason", func(t *testing.T) {
		txnData := resources.InterestPayoutReversalTransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[3]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "Rejected due to account is closed", response)
	})
}

func TestStatusDescriptionFailedTransactions(t *testing.T) {
	t.Run("contract_violation_transaction", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[0]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "CONTRACT_VIOLATION_BREACH_TERMS_AND_CONDITIONS", response)
	})
	t.Run("rejected_by_partner_transaction", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[1]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "DDA Terminated. code from adapter: 1041", response)
	})
	t.Run("payment_limit_transaction", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[2]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "Maximum amount exceeded for PAYMENT_TRANSFER_LIMIT.ACCOUNT_ID.DAILY_CALENDAR. Amount has to be between 1 and 111", response)
	})
	t.Run("account_violation_transaction", func(t *testing.T) {
		txnData := resources.InterestPayoutReversalTransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[3]
		response := getStatusDescription(context.Background(), txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "ACCOUNT_VIOLATION_ACCOUNT_NOT_PRESENT", response)
	})
}

func TestGetTransactionDetailsResponseGenerator(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	txnData := resources.TransactionsMockDBRows()
	mockPaymentDao := &storage.MockIPaymentDetailDAO{}
	mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
	storage.PaymentDetailD = mockPaymentDao

	req := &api.GetTransactionDetailRequest{
		AccountID:     "12345",
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
	}
	mockAppConfig := &config.AppConfig{
		IconConfig: config.IconConfig{
			TransferOut: "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		Locale: config.Locale{Language: "en"},
	}
	constants.InitializeDynamicConstants(mockAppConfig)

	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)

	t.Run("payment-transaction", func(t *testing.T) {
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData[0], req, "abc123efg")
		assert.Equal(t, responses.GetTransactionDetailResponse()[2], response)
	})

	t.Run("grab-transaction", func(t *testing.T) {
		txnData1 := resources.GrabTransactionsMockDBRows()
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows()[3:], nil)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd12345",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[0], request, "abcd12345")
		assert.Equal(t, responses.GetTransactionDetailForGRABResponse()[0], response)
	})

	t.Run("interest-payout-transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData
		response := g.getTransactionDetailsResponseGenerator(context.Background(), resources.InterestPayoutTransactionsMockDBRows()[1], req, "abc123efg")
		assert.Equal(t, responses.GetInterestEarnedTransactionResponse()[0], response)
	})
	t.Run("tax-payout-transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData
		response := g.getTransactionDetailsResponseGenerator(context.Background(), resources.TaxPayoutTransactionsMockDBRows()[1], req, "abc123efg")
		assert.Equal(t, responses.GetTaxDeductedTransactionResponse()[0], response)
	})
	t.Run("pocket-transfer-transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response := g.getTransactionDetailsResponseGenerator(context.Background(), resources.PocketTransactions()[1], req, "test-client-batch-id1")
		assert.Equal(t, responses.GetPocketTransactionResponses()[0], response)
	})
	t.Run("pocket-withdrawal-transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response := g.getTransactionDetailsResponseGenerator(context.Background(), resources.PocketTransactions()[3], req, "test-client-batch-id1")
		assert.Equal(t, responses.GetPocketTransactionResponses()[1], response)
	})
	t.Run("should return sub account name as source of fund for pocket to main withdrawal transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response := g.getTransactionDetailsResponseGenerator(context.Background(), resources.PocketTransactions()[2], req, "")
		assert.Equal(t, "MAIN_ACCOUNT", response.SourceOfFund)
	})
	t.Run("should return source of fund as empty for pocket to main withdrawal transaction", func(t *testing.T) {
		mockPaymentData := &storage.MockIPaymentDetailDAO{}
		mockPaymentData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentData

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		transactionsData := resources.PocketTransactions()[2]
		transactionsData.BatchDetails = nil
		response := g.getTransactionDetailsResponseGenerator(context.Background(), transactionsData, req, "")
		assert.Equal(t, "", response.SourceOfFund)
	})

	t.Run("Invalid payment type", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentDataMockDBRows()[3]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData[0], req, "abc123efg")
		assert.Equal(t, responses.GetTransactionDetailResponse()[1], response)
	})
	t.Run("moomoo-transaction-spend-money", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		locale := utils.GetLocale()
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
			IconConfig: config.IconConfig{
				TransferOut: "https:/assets.bank/image.png",
			},
		}

		os.Setenv("LOCALISATION_PATH", "../../localise")
		config.SetTenant(tenants.TenantMY)
		constants.InitializeDynamicConstants(mockAppConfig)
		localise.Init(mockAppConfig)
		presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
		txnData1 := resources.MooMooTransactionsMockDBRows()
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMooMooSpendMoneyMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd12345",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[0], request, "abcd12345")
		responseExpected := responses.GetTransactionDetailForMooMooResponse()[0]
		assert.Equal(t, responseExpected.Amount, response.Amount)
		assert.Equal(t, responseExpected.TransactionDescription, response.TransactionDescription)
		assert.Equal(t, *responseExpected.CounterParty, *response.CounterParty)
		assert.Equal(t, *responseExpected.Category, *response.Category)
	})

	t.Run("moomoo-transaction-spend-cash-out", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		locale := utils.GetLocale()
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
			IconConfig: config.IconConfig{
				TransferIn: "https:/assets.bank/image.png",
			},
		}

		os.Setenv("LOCALISATION_PATH", "../../localise")
		config.SetTenant(tenants.TenantMY)
		constants.InitializeDynamicConstants(mockAppConfig)
		localise.Init(mockAppConfig)
		presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
		txnData1 := resources.MooMooTransactionsMockDBRows()
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMooMooCashOutMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd1234567",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[1], request, "abcd1234567")
		responseExpected := responses.GetTransactionDetailForMooMooResponse()[1]
		assert.Equal(t, responseExpected.Amount, response.Amount)
		assert.Equal(t, responseExpected.TransactionDescription, response.TransactionDescription)
		assert.Equal(t, *responseExpected.CounterParty, *response.CounterParty)
		assert.Equal(t, *responseExpected.Category, *response.Category)
	})

	t.Run("moomoo-transaction-spend-spend-money-reversal", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		locale := utils.GetLocale()
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
			IconConfig: config.IconConfig{
				TransferIn: "https:/assets.bank/image.png",
			},
		}

		os.Setenv("LOCALISATION_PATH", "../../localise")
		config.SetTenant(tenants.TenantMY)
		constants.InitializeDynamicConstants(mockAppConfig)
		localise.Init(mockAppConfig)
		presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
		txnData1 := resources.MooMooTransactionsMockDBRows()
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMooMooSpendMoneyRevMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd123456",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[2], request, "abcd123456")
		responseExpected := responses.GetTransactionDetailForMooMooResponse()[2]
		assert.Equal(t, responseExpected.Amount, response.Amount)
		assert.Equal(t, responseExpected.TransactionDescription, response.TransactionDescription)
		assert.Equal(t, *responseExpected.CounterParty, *response.CounterParty)
		assert.Equal(t, *responseExpected.Category, *response.Category)
	})

	t.Run("new card issuance fee", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		locale := utils.GetLocale()
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
			IconConfig: config.IconConfig{
				Mastercard: "https:/assets.bank/image.png",
			},
		}

		os.Setenv("LOCALISATION_PATH", "../../localise")
		config.SetTenant(tenants.TenantMY)
		constants.InitializeDynamicConstants(mockAppConfig)
		localise.Init(mockAppConfig)
		presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
		txnData1 := lo.Filter(resources.CardMaintenanceFeeTransactionsDBMockRows(), func(txn *storage.TransactionsData, _ int) bool {
			return txn.TransactionType == constants.NewCardIssuanceFeeTransactionType
		})
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd1234567",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[0], request, "abcd1234567")
		responseExpected := responses.GetCardNewIssuanceFeeTransactionDetailResponse()[0]
		var txnDetail dto.TransactionDetail
		err := json.Unmarshal([]byte(txnData1[0].TransactionDetails), &txnDetail)
		require.NoErrorf(t, err, "error unmarshalling transaction details")
		assert.Equal(t, responseExpected.Amount, response.Amount)
		assert.Equal(t, responseExpected.TransactionDescription, response.TransactionDescription)
		assert.Equal(t, *responseExpected.CounterParty, *response.CounterParty)
		assert.Equal(t, txnDetail.CardID, response.CardTransactionDetail.CardID)
		assert.Equal(t, "**"+txnDetail.TailCardNumber, response.CardTransactionDetail.TailCardNumber)
	})

	t.Run("new card issuance fee waived", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		locale := utils.GetLocale()
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
			IconConfig: config.IconConfig{
				Mastercard: "https:/assets.bank/image.png",
			},
		}

		os.Setenv("LOCALISATION_PATH", "../../localise")
		config.SetTenant(tenants.TenantMY)
		constants.InitializeDynamicConstants(mockAppConfig)
		localise.Init(mockAppConfig)
		presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
		txnData1 := lo.Filter(resources.CardMaintenanceFeeWaiverTransactionsDBMockRows(), func(txn *storage.TransactionsData, _ int) bool {
			return txn.TransactionType == constants.NewCardIssuanceFeeWaiverTransactionType
		})
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, data.ErrNoData)
		storage.PaymentDetailD = mockPaymentDao1
		request := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd1234567",
		}
		response := g.getTransactionDetailsResponseGenerator(context.Background(), txnData1[0], request, "abcd1234567")
		responseExpected := responses.GetCardNewIssuanceFeeWaivedTransactionDetailResponse()[0]
		assert.Equal(t, responseExpected.Amount, response.Amount)
		assert.Equal(t, responseExpected.TransactionDescription, response.TransactionDescription)
		assert.Equal(t, *responseExpected.CounterParty, *response.CounterParty)
		var txnDetail dto.TransactionDetail
		err := json.Unmarshal([]byte(txnData1[0].TransactionDetails), &txnDetail)
		require.NoErrorf(t, err, "error unmarshalling transaction details")
		assert.Equal(t, txnDetail.CardID, response.CardTransactionDetail.CardID)
		assert.Equal(t, "**"+txnDetail.TailCardNumber, response.CardTransactionDetail.TailCardNumber)
	})
}

func TestGXRewardGetTransactionDetails(t *testing.T) {
	defer func() { config.SetTenant("") }()
	locale := utils.GetLocale()
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}

	os.Setenv("LOCALISATION_PATH", "../../localise")
	config.SetTenant(tenants.TenantMY)
	constants.InitializeDynamicConstants(mockAppConfig)
	localise.Init(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("GX Rewards cashback created by Ops", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GXRewardCashBackTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, "GX reward", actualResp.TransactionDescription)
		assert.Equal(t, "GX rewards input", actualResp.CounterParty.TransactionDetails["recipient_reference"])
		assert.Nil(t, err)
	})

	t.Run("Rewards cashback from normal campaign", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.NormalRewardCashBackTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetTransactionDetail(context.Background(), req)
		assert.Equal(t, "Add money reward", actualResp.TransactionDescription)
		assert.Equal(t, "RM8 cashback", actualResp.CounterParty.TransactionDetails["recipient_reference"])
		assert.Nil(t, err)
	})
}

func Test_filterTxnOnAccountPhaseAndType(t *testing.T) {
	t.Run("should return release posting in cancelled new card issuance fee posting batch", func(t *testing.T) {
		transactions := resources.CardIssuanceFeeCancelledBatchDBMockRows()
		originalPaymentD := storage.PaymentDetailD
		defer func() { storage.PaymentDetailD = originalPaymentD }()
		mockPaymentDao1 := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao1.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.PaymentDetailD = mockPaymentDao1
		transactionRes := filterTxnOnAccountPhaseAndType(transactions)
		assert.Equalf(t, constants.Release, transactionRes.TmTransactionType, "filtered transaction should be release transaction")
	})
}
