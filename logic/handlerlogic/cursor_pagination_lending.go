package handlerlogic //nolint:dupl

import (
	"fmt"
	"strconv"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/servicename"
)

const (
	// PreviousPageLinkForLending ...
	PreviousPageLinkForLending = "/v1/lending/transactions-search?pageSize=%v&endingAfter=%v"
	// NextPageLinkForLending ...
	NextPageLinkForLending = "/v1/lending/transactions-search?pageSize=%v&startingBefore=%v"
)

// ################### Building Pagination Links for Lending ##########################################

// buildPaginationLinksForLending create links required for subsequent pagination calls
func buildPaginationLinksForLending(dbResponse []*storage.TransactionsData, page []*storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) map[string]string {
	// if first page request, FirstTransactionID is equal to ID of the first fetched transaction
	var nextLink, nextCursorID, prevLink, prevCursorID string
	if paginationParameters.EndingAfter == "" && paginationParameters.StartingBefore == "" {
		cursorData.FirstTransactionID = dbResponse[0].ID
	}
	var hasSinglePage = utils.MustConvertToInt64(len(dbResponse)) > paginationParameters.PageSize || paginationParameters.EndingAfter != ""
	if hasSinglePage {
		nextLink, nextCursorID = createNextPageLinkForLending("", dbResponse, paginationParameters, cursorData)
	}
	var hasPreviousPage = page[0].ClientTransactionID != strconv.FormatUint(cursorData.FirstTransactionID, 10)
	if hasPreviousPage {
		prevLink, prevCursorID = createPrevPageLinkForLending(page[0], paginationParameters, cursorData)
	}
	return map[string]string{"prev": prevLink, "prevCursorID": prevCursorID, "next": nextLink, "nextCursorID": nextCursorID}
}

// createNextPageLinkForLending: method to build next page link for lending search
//
// nolint:dupl
func createNextPageLinkForLending(clientIdentity string, dbResponse []*storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) (string, string) {
	var nextLink, nextCursorID string
	var nextPageCursorData dto.PaginationCursor
	if utils.MustConvertToInt64(len(dbResponse)) > paginationParameters.PageSize || paginationParameters.EndingAfter != "" {
		nextPageCursorData = dto.PaginationCursor{
			ID:                 utils.MustConvertToInt64(dbResponse[paginationParameters.PageSize-1].ID),
			TransactionID:      dbResponse[paginationParameters.PageSize-1].ClientBatchID,
			Date:               dbResponse[paginationParameters.PageSize-1].BatchValueTimestamp.Format("2006-01-02T15:04:05Z07:00"),
			FirstTransactionID: cursorData.FirstTransactionID,
		}
	} else if utils.MustConvertToInt64(len(dbResponse)) <= paginationParameters.PageSize {
		if clientIdentity == servicename.CustomerPortal.ToString() {
			return "", ""
		}
		nextPageCursorData = dto.PaginationCursor{
			ID:                 utils.MustConvertToInt64(dbResponse[len(dbResponse)-1].ID),
			TransactionID:      dbResponse[len(dbResponse)-1].ClientBatchID,
			Date:               dbResponse[len(dbResponse)-1].BatchValueTimestamp.Format("2006-01-02T15:04:05Z07:00"),
			FirstTransactionID: cursorData.FirstTransactionID,
		}
	}
	nextLink, nextCursorID = createPaginationLinksForLending(NextPageLinkForLending, paginationParameters, nextPageCursorData)
	return nextLink, nextCursorID
}

// createPrevPageLinkForLending:  method to build prev page link for lending search
//
// nolint:dupl
func createPrevPageLinkForLending(firstTransactionofPage *storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) (string, string) {
	var prevLink, prevCursorID string
	var prevPageCursorData dto.PaginationCursor
	if paginationParameters.StartingBefore == "" && paginationParameters.EndingAfter == "" {
		prevLink = ""
		prevCursorID = ""
	} else if firstTransactionofPage.ID == cursorData.FirstTransactionID {
		// If the first ID of page equal to FirstTransactionID, this implies not previousPage exist
		prevLink = ""
		prevCursorID = ""
	} else {
		prevPageCursorData = dto.PaginationCursor{
			ID:                 utils.MustConvertToInt64(firstTransactionofPage.ID),
			TransactionID:      firstTransactionofPage.ClientBatchID,
			Date:               firstTransactionofPage.BatchValueTimestamp.Format("2006-01-02T15:04:05Z07:00"),
			FirstTransactionID: cursorData.FirstTransactionID,
		}
		prevLink, prevCursorID = createPaginationLinksForLending(PreviousPageLinkForLending, paginationParameters, prevPageCursorData)
	}
	return prevLink, prevCursorID
}

// createPaginationLinks will create pagination links based on next/prev page cursor data.
func createPaginationLinksForLending(link string, paginationParameter dto.PaginationParameters, newCursorData dto.PaginationCursor) (string, string) {
	newCursor := encodeCursor(newCursorData)
	formattedLink := fmt.Sprintf(link, paginationParameter.PageSize, newCursor)
	if paginationParameter.StartDate != "" {
		formattedLink += fmt.Sprintf("&startDate=%v", paginationParameter.StartDate)
	}
	if paginationParameter.EndDate != "" {
		formattedLink += fmt.Sprintf("&endDate=%v", paginationParameter.EndDate)
	}
	return formattedLink, newCursor
}
