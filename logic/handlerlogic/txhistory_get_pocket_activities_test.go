package handlerlogic

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestGetPocketActivitiesRequestValidator(t *testing.T) {
	t.Run("pocketID-missing", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		expectedError := servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		}
		errorResponse := GetPocketActivitiesRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("pocketType-missing", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:  "1234000",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := []servus.ErrorDetail{
			{
				Message: "'pocketType' is a mandatory parameter.",
			},
		}
		errorResponse := GetPocketActivitiesRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse)
	})
	t.Run("pocketType-invalid", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SPEND",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		expectedError := servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		}
		errorResponse := GetPocketActivitiesRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("pageSize greater than the MaxPageSize", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   251,
		}

		expectedError := servus.ErrorDetail{
			Message: "'pageSize' greater than maxPageSize.",
		}
		errorResponse := GetPocketActivitiesRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
	t.Run("pageSize smaller than the MinPageSize", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   -1,
		}
		expectedError := servus.ErrorDetail{
			Message: "'pageSize' less than minPageSize.",
		}
		errorResponse := GetPocketActivitiesRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
}

func TestGetPocketActivitiesFromDB(t *testing.T) {
	t.Run("no transactions present in DB", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "12345000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := responses.GetPocketActivitiesEmptyResponse()

		response, err := GetPocketActivitiesFromDB(context.Background(), request, "12345")
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("transactions present for the pocket", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "8880077782000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesFirstPageResponse()
		response, err := GetPocketActivitiesFromDB(context.Background(), request, "8880077782")
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
	t.Run("happy-path-prev-next-page-exist", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:       "8880077782000",
			PocketType:     "SAVINGS",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwxLGVmZzEyM2FiZA==",
		}
		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTxnWithNextAndPrevDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesPrevNextCursorResponse()
		response, err := GetPocketActivitiesFromDB(context.Background(), request, "8880077782")
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
	t.Run("happy-path-backwardScrolling", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:    "8880077782000",
			PocketType:  "SAVINGS",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    3,
			EndingAfter: "MjAyMS0wOC0yMFQwMjozMTo0MFosMywxLGVmZzExMTFhYmQ=",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.ReversePocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesBackwardScrollingPrevPageResponse()
		response, err := GetPocketActivitiesFromDB(context.Background(), request, "8880077782")
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
	t.Run("error in fetching data from transaction db", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "12345000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := GetPocketActivitiesFromDB(context.Background(), request, "12345")
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
}

func TestGetDisplayNameForPocketTransaction(t *testing.T) {
	t.Run("interest-transaction", func(t *testing.T) {
		txRow := resources.InterestPayoutTransactionOrderedInput()[0]

		response := getDisplayNameForPocketTransaction(txRow)
		assert.Equal(t, "Interest Earned", response)
	})

	t.Run("funding transaction", func(t *testing.T) {
		txRow := resources.PocketTransactionsDataMockDBRows()[0]
		response := getDisplayNameForPocketTransaction(txRow)
		assert.Equal(t, "Funds added", response)
	})

	t.Run("funding withdrawal transaction", func(t *testing.T) {
		txRow := resources.PocketTransactionsDataMockDBRows()[2]
		response := getDisplayNameForPocketTransaction(txRow)
		assert.Equal(t, "Funds withdrawn", response)
	})
}
