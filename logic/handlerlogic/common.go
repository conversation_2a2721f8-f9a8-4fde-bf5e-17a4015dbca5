package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"

	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// computeDateRange will compute the time range based on input params and cursor.
func computeDateRange(startingBefore string, endingAfter string, startDate string, endDate string, cursorData dto.PaginationCursor) (string, string) {
	var startingDate, endingDate string
	switch {
	case endingAfter == "" && startingBefore == "":
		startingDate = startDate
		endingDate = endDate
	case endingAfter != "":
		startingDate = cursorData.Date
		endingDate = endDate
	case startingBefore != "":
		startingDate = startDate
		endingDate = cursorData.Date
	}
	return startingDate, endingDate
}

// filterOutIncomingFailedAndCancelledTransfer will exclude all the incoming failed and cancelled
func filterOutIncomingFailedAndCancelledTransfer(dbResponse []*storage.TransactionsData, accountAddress string) []*storage.TransactionsData {
	var finalResult []*storage.TransactionsData
	var clientBatchIDsToExclude []string
	for _, item := range dbResponse {
		// For handling pocket transactions
		if item.AccountAddress != accountAddress {
			continue
		}
		if item.DebitOrCredit == constants.CREDIT && item.BatchStatus == constants.BatchStatusRejected {
			// BatchStatus rejected and incoming meaning failed transaction
			clientBatchIDsToExclude = append(clientBatchIDsToExclude, item.ClientBatchID)
		}
		// For DBMY, receive money reversal txn type need to be shown
		// if item.DebitOrCredit == constants.DEBIT && item.TransactionType == constants.ReceiveMoneyRevTxType {
		//	// Incoming and Receive Money reversal meaning cancelled transaction
		//	clientBatchIDsToExclude = append(clientBatchIDsToExclude, item.ClientBatchID)
		// }
		if !utils.SearchStringArray(clientBatchIDsToExclude, item.ClientBatchID) {
			finalResult = append(finalResult, item)
		}
	}
	return finalResult
}

// filterOutApplyEarmarkFailedTransfer will exclude failed apply earmark transactions
func filterOutApplyEarmarkFailedTransfer(dbResponse []*storage.TransactionsData, accountAddress string) []*storage.TransactionsData {
	var finalResult []*storage.TransactionsData
	clientBatchIDsToInclude := make(map[string]struct{})

	for _, item := range dbResponse {
		// For handling pocket transactions
		if item.AccountAddress != accountAddress {
			continue
		}
		if item.TransactionType == constants.ApplyEarmarkTransactionType && item.BatchStatus == constants.BatchStatusRejected {
			// Apply earmark transaction with batch status rejected means a failed transaction
			clientBatchIDsToInclude[item.ClientBatchID] = struct{}{}
		}

		if _, include := clientBatchIDsToInclude[item.ClientBatchID]; !include {
			finalResult = append(finalResult, item)
		}
	}
	return finalResult
}

// filterOutSettledOrReleasedCardTxn will exclude settled and released card transactions
// nolint: unparam
func filterOutSettledOrReleasedCardTxn(dbResponse []*storage.TransactionsData, accountAddress string) []*storage.TransactionsData {
	finalResult := make([]*storage.TransactionsData, 0, len(dbResponse))

	for _, item := range dbResponse {
		// For handling pocket transactions
		if item.AccountAddress != accountAddress {
			continue
		}
		if item.IsCardTxn() {
			if item.TmTransactionType == constants.SettlementTxType || item.TmTransactionType == constants.Release {
				continue
			}
		}
		finalResult = append(finalResult, item)
	}
	return finalResult
}

// createClientBatchIDToTransactionsDataMap method to group txnData by clientBatchID,
// so that one entry per clientBatchID can be return in response.
func createClientBatchIDToTransactionsDataMap(dbResponse []*storage.TransactionsData) ([]string, map[string][]*storage.TransactionsData) {
	var clientBatchIDOrder []string
	clientBatchIDToTransactionsDataMap := make(map[string][]*storage.TransactionsData)
	for _, transactionData := range dbResponse {
		if _, ok := clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID]; !ok {
			clientBatchIDOrder = append(clientBatchIDOrder, transactionData.ClientBatchID)
			clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID] = []*storage.TransactionsData{transactionData}
		} else {
			clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID] = append(clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID], transactionData)
		}
	}
	return clientBatchIDOrder, clientBatchIDToTransactionsDataMap
}

// createFinalDBTransactionList creates final list of transactions to be shown
func createFinalDBTransactionList(clientBatchIDOrder []string,
	clientBatchIDToTransactionsDataMap map[string][]*storage.TransactionsData, filteredRowsCount int64) ([]*storage.TransactionsData, dto.ClientTypesBatchIDs) {
	// Select transactions as per required pageSize
	var finalDBResponse []*storage.TransactionsData
	var paymentBatchIDs []string
	var cardBatchIDs []string
	var loanBatchIDs []string

	for _, item := range clientBatchIDOrder {
		if utils.MustConvertToInt64(len(finalDBResponse)) < filteredRowsCount {
			txDataList := clientBatchIDToTransactionsDataMap[item]
			if len(txDataList) != 0 {
				finalDBResponse = append(finalDBResponse, txDataList...)
				clientBatchID := txDataList[0].ClientBatchID
				switch txDataList[0].TransactionDomain {
				case constants.DebitCardDomain:
					cardBatchIDs = append(cardBatchIDs, clientBatchID)
				case constants.LendingDomain:
					loanBatchIDs = append(loanBatchIDs, clientBatchID)
					paymentBatchIDs = append(paymentBatchIDs, clientBatchID) // we will need the payment detail for LENDING trx
				case constants.BizLendingDomain:
					loanBatchIDs = append(loanBatchIDs, clientBatchID)
					paymentBatchIDs = append(paymentBatchIDs, clientBatchID)
				default:
					paymentBatchIDs = append(paymentBatchIDs, clientBatchID)
				}
			}
		}
	}
	minRowCount := utils.MinInt(len(finalDBResponse), int(filteredRowsCount))
	return finalDBResponse[:minRowCount], dto.ClientTypesBatchIDs{
		PaymentBatchIDs: paymentBatchIDs,
		CardBatchIDs:    cardBatchIDs,
		LoanBatchIDs:    loanBatchIDs,
	}
}

// invertDBResponseForBackwardScrolling will return inverted array.
// In case of backward scrolling, results are fetched in ascending order therefore rotating them
func invertDBResponseForBackwardScrolling(dbResponse []*storage.TransactionsData) []*storage.TransactionsData {
	var invertedDBResponse []*storage.TransactionsData
	for index := len(dbResponse) - 1; index >= 0; index-- {
		invertedDBResponse = append(invertedDBResponse, dbResponse[index])
	}
	return invertedDBResponse
}

// computeNumberOfTransactionsInFilteredDBResponse will compute total number of rows to be return in response based on pageSize
func computeNumberOfTransactionsInFilteredDBResponse(startingBefore string, endingAfter string, pageSize int64) int64 {
	// computing number of rows to be added
	var filteredRowsCount int64
	switch {
	case endingAfter == "" && startingBefore == "":
		filteredRowsCount = pageSize + 1
	case endingAfter != "":
		filteredRowsCount = pageSize
	case startingBefore != "":
		filteredRowsCount = pageSize + 1
	}
	return filteredRowsCount
}

// getTransactionStatus contains logic to return transaction status based on DB data
func getTransactionStatus(transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail) string {
	var status string
	// For Interest Transactions
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, transaction.TransactionType) || utils.SearchStringArray(constants.TaxPayoutTransactionTypes, transaction.TransactionType) || constants.OPSTransactionType == transaction.TransactionType {
		status = formatDepositsCoreInterest(transaction)
		return status
	}

	// For other Transactions(Currently Payments)
	if transaction.ClientBatchID != "" {
		paymentDetail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		if ok {
			status = paymentDetail.Status
			if paymentDetail.Status == constants.AuthorizedStatus {
				status = constants.ProcessingStatus
			}
		} else { // For now, this will happen for manual posting only
			status = formatDepositsCoreInterest(transaction)
		}
	}
	return status
}

// formatDepositsCoreInterest returns formatted status based on TM BatchStatus field.
func formatDepositsCoreInterest(transaction *storage.TransactionsData) string {
	var status string
	if transaction.BatchStatus == constants.BatchStatusRejected {
		status = constants.FailedStatus
	} else if transaction.BatchStatus == constants.BatchStatusAccepted {
		status = constants.CompletedStatus
	}
	return status
}

// getCounterPartyDisplayName will return the counterparty displayName based on data from payments and TM.
func getCounterPartyDisplayName(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail, pocketIDToNameMap map[string]string) string {
	var counterPartyDisplayName string
	var batchDetails map[string]string
	// For Internal Transactions
	counterPartyDisplayName = counterPartyName(transaction)
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	} else if transaction.ClientBatchID != "" {
		err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
		} else if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			return getCounterPartyNameForPocketTransactions(transaction, batchDetails, pocketIDToNameMap)
		} else if utils.SearchStringArray(constants.LendingTransactionTypes, transaction.TransactionType) {
			return getCounterPartyDisplayNameForLendingTransactions(transaction)
		}

		paymentDetail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		// For now, this will happen for manual posting only.
		if !ok {
			return counterPartyNameForManualPosting(ctx, transaction, batchDetails)
		} // For other Transactions(Currently Payments)
		counterPartyDisplayName = getCounterPartyAccountNameFromPaymentDetail(ctx, paymentDetail)
	}
	return counterPartyDisplayName
}

func getCounterPartyAccountNameFromPaymentDetail(ctx context.Context, paymentDetail *storage.PaymentDetail) string {
	var counterPartyDisplayName string
	var counterPartyAccountDetails dto.AccountDetail
	var metadata map[string]string

	if paymentDetail.TransactionSubType == constants.Grab {
		if err := json.Unmarshal(paymentDetail.Metadata, &metadata); err != nil {
			slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing Metadata, err: %s", err.Error()))
			return counterPartyDisplayName
		}
		if config.GetTenant() == tenants.TenantMY {
			counterPartyDisplayName = presenterhelper.GetCounterPartyDisplayNameForGrabTxn(ctx, paymentDetail)
		} else {
			counterPartyDisplayName = metadata["grab_activity_type"]
		}
		return counterPartyDisplayName
	}
	if paymentDetail.TransactionSubType == constants.MooMoo {
		return "MooMoo"
	}
	err := json.Unmarshal(paymentDetail.CounterPartyAccount, &counterPartyAccountDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing CounterPartyAccount, err: %s", err.Error()))
		return counterPartyDisplayName
	}

	tenant := config.GetTenant()
	switch tenant {
	case tenants.TenantMY:
		if counterPartyAccountDetails.DisplayName != "" {
			counterPartyDisplayName = cases.Title(language.Und).String(counterPartyAccountDetails.DisplayName)
		} else {
			counterPartyDisplayName = cases.Title(language.Und).String(counterPartyAccountDetails.FullName)
		}
	default:
		if counterPartyAccountDetails.DisplayName != "" {
			counterPartyDisplayName = counterPartyAccountDetails.DisplayName
		} else {
			counterPartyDisplayName = counterPartyAccountDetails.FullName
		}
	}
	return counterPartyDisplayName
}

func getAccountDisplayNameFromPaymentDetail(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail) string {
	paymentDetail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
	if !ok {
		return ""
	}
	var accountDisplayName string
	var accountDetails dto.AccountDetail

	err := json.Unmarshal(paymentDetail.Account, &accountDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing Account, err: %s", err.Error()))
		return accountDisplayName
	}
	if accountDetails.DisplayName != "" {
		accountDisplayName = accountDetails.DisplayName
	} else {
		accountDisplayName = accountDetails.FullName
	}
	return accountDisplayName
}

func counterPartyName(transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string
	tenant := config.GetTenant()
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, transaction.TransactionType) {
		counterPartyDisplayName = localise.Translate(constants.InterestEarned)
	}
	if utils.SearchStringArray(constants.TaxPayoutTransactionTypes, transaction.TransactionType) {
		counterPartyDisplayName = localise.Translate(constants.TaxOnInterestDesc)
	}
	if utils.SearchStringArray(constants.SendMoneyFeeTransactionTypes, transaction.TransactionType) {
		counterPartyDisplayName = localise.Translate(constants.TransactionFee)
	}
	switch tenant {
	case tenants.TenantMY:
		if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, transaction.TransactionType) {
			counterPartyDisplayName = localise.Translate(constants.DBMYInterestEarned)
		}
	}
	return counterPartyDisplayName
}

func getCounterPartyNameForPocketTransactions(transaction *storage.TransactionsData, batchDetails, pocketIDToNameMap map[string]string) string {
	// Boost Pocket
	if batchDetails["sub_account_type"] == constants.BoostPocket {
		return getCounterPartyNameForBoostPocketTransactions(transaction, batchDetails, pocketIDToNameMap)
	}

	// Savings Pocket
	var counterPartyDisplayName string
	if transaction.TransactionSubtype == constants.PocketFundingTransactionSubType {
		if transaction.DebitOrCredit == constants.DEBIT {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		} else if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		}
	} else if transaction.TransactionSubtype == constants.PocketWithdrawalTransactionSubType {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		} else if transaction.DebitOrCredit == constants.DEBIT {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		}
	}
	return counterPartyDisplayName
}

func getCounterPartyNameForBoostPocketTransactions(transaction *storage.TransactionsData, batchDetails, pocketIDToNameMap map[string]string) string {
	switch transaction.TransactionSubtype {
	case constants.PocketFundingTransactionSubType:
		switch transaction.DebitOrCredit {
		case constants.DEBIT:
			pocketID := batchDetails["pocket_id"]
			return pocketIDToNameMap[pocketID]
		case constants.CREDIT:
			return batchDetails["source_display_name"]
		}
	case constants.PocketWithdrawalTransactionSubType:
		switch transaction.DebitOrCredit {
		case constants.CREDIT:
			return batchDetails["source_display_name"]
		case constants.DEBIT:
			pocketID := batchDetails["pocket_id"]
			return pocketIDToNameMap[pocketID]
		}
	}

	return ""
}

func getCounterPartyDisplayNameForLendingTransactions(transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string
	if transaction.TransactionType == constants.DrawdownTransactionType {
		counterPartyDisplayName = localise.Translate(constants.LendingDrawdown)
	} else if transaction.TransactionType == constants.RepaymentTransactionType {
		counterPartyDisplayName = localise.Translate(constants.LendingRepayment)
	}
	return counterPartyDisplayName
}

// counterPartyNameForManualPosting gets displayName for manual posting and intrabank payment-ops-trf
func counterPartyNameForManualPosting(ctx context.Context, transaction *storage.TransactionsData, batchDetails map[string]string) string {
	var counterPartyDisplayName string
	if transaction.TransactionType == constants.OPSTransactionType {
		return constants.OPSDisplayName
	} else if transaction.IsOpsRewardCashback() {
		return transaction.GetRewardsCampaignName(ctx)
	}
	if transaction.DebitOrCredit == constants.DEBIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
		counterPartyDisplayName = batchDetails["source_display_name"]
	} else if transaction.DebitOrCredit == constants.CREDIT && utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
		counterPartyDisplayName = batchDetails["destination_display_name"]
	} else if transaction.DebitOrCredit == constants.DEBIT {
		counterPartyDisplayName = batchDetails["destination_display_name"]
	} else {
		counterPartyDisplayName = batchDetails["source_display_name"]
	}
	return counterPartyDisplayName
}

// nolint: dupl
// getTotalInterestEarned filters the interest transactions from the DB and return the total interest earned for the specified account.
func getTotalInterestEarned(ctx context.Context, accountID string, accountAddress string) (*api.Money, error) {
	interestEarnedFilters := prepareInterestEarnedFilters(accountID, accountAddress)
	dbResponse, err := storage.InterestAggregateD.Find(ctx, interestEarnedFilters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Error getting interest payout data from db, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyMoneyResponse(), nil
	}
	var interestEarned int64
	for _, interestTxn := range dbResponse {
		interestEarned += interestTxn.TotalInterestEarned
	}
	return &api.Money{
		CurrencyCode: dbResponse[0].Currency,
		Val:          interestEarned,
	}, nil
}

// nolint: dupl
// getTotalInterestEarnedV2 filters the interest transactions from the V2 DB and return the total interest earned for the specified account.
func getTotalInterestEarnedV2(ctx context.Context, accountID string, accountAddress string) (*api.Money, error) {
	interestEarnedFilters := prepareInterestEarnedFilters(accountID, accountAddress)
	dbResponse, err := storage.InterestAggregateV2D.Find(ctx, interestEarnedFilters...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Error getting interest payout data from db, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || errors.Is(err, data.ErrNoData) {
		return emptyMoneyResponse(), nil
	}
	var interestEarned int64
	for _, interestTxn := range dbResponse {
		for transferType, val := range interestTxn.TotalInterestEarned {
			if transferType == constants.InterestPayoutReversalTransactionType ||
				transferType == constants.BonusInterestPayoutReversalTransactionType {
				interestEarned -= val
			} else {
				interestEarned += val
			}
		}
	}
	return &api.Money{
		CurrencyCode: dbResponse[0].Currency,
		Val:          interestEarned,
	}, nil
}

// nolint: dupl
// getTotalInterestEarnedDBMY filters the interest transactions from the DB and return the total interest earned for the specific account list.
func getTotalInterestEarnedDBMY(ctx context.Context, sccountList []string, accountAddress string) (*api.Money, error) {
	interestEarnedFilters := buildInterestEarnedFiltersDBMY(sccountList, accountAddress)
	dbResponse, err := storage.InterestAggregateD.Find(ctx, interestEarnedFilters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Error getting interest payout data from db, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyMoneyResponse(), nil
	}
	var interestEarned int64
	for _, interestTxn := range dbResponse {
		interestEarned += interestTxn.TotalInterestEarned
	}
	return &api.Money{
		CurrencyCode: dbResponse[0].Currency,
		Val:          interestEarned,
	}, nil
}

// nolint: dupl
// getTotalInterestEarnedDBMYV2 filters the interest transactions from the DB and return the total interest earned for the specific account list.
func getTotalInterestEarnedDBMYV2(ctx context.Context, sccountList []string, accountAddress string) (*api.Money, error) {
	interestEarnedFilters := buildInterestEarnedFiltersDBMY(sccountList, accountAddress)
	dbResponse, err := storage.InterestAggregateV2D.Find(ctx, interestEarnedFilters...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Error getting interest payout data from db, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || errors.Is(err, data.ErrNoData) {
		return emptyMoneyResponse(), nil
	}
	var interestEarned int64
	for _, interestTxn := range dbResponse {
		for transferType, val := range interestTxn.TotalInterestEarned {
			if transferType == constants.InterestPayoutReversalTransactionType ||
				transferType == constants.BonusInterestPayoutReversalTransactionType {
				interestEarned -= val
			} else {
				interestEarned += val
			}
		}
	}
	return &api.Money{
		CurrencyCode: dbResponse[0].Currency,
		Val:          interestEarned,
	}, nil
}

func prepareInterestEarnedFilters(accountID string, accountAddress string) []data.Condition {
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
	}
	if accountAddress != "" {
		// case where we are getting the total earned interest of a specific pockets
		filters = append(filters, data.EqualTo("AccountAddress", accountAddress))
	}
	return filters
}

func buildInterestEarnedFiltersDBMY(accountList []string, accountAddress string) []data.Condition {
	filters := []data.Condition{
		data.ContainedIn("AccountID", utils.ConvertToInterface(accountList)...),
		data.EqualTo("AccountAddress", accountAddress),
	}
	return filters
}

func getTransactionRemarks(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail) string {
	var remarks string
	if paymentData != nil {
		remarks = getRemarksFromMetadata(ctx, paymentData)
	} else {
		remarks = txnData.BatchRemarks
	}
	return remarks
}

func getRemarksFromMetadata(ctx context.Context, paymentData *storage.PaymentDetail) string {
	var metadata map[string]string
	err := json.Unmarshal(paymentData.Metadata, &metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("Error unmarshalling payment metadata, err: %s", err.Error()))
	}
	return metadata["remarks"]
}

// getDefaultIconURL func returns the IconURL for a transaction
func getDefaultIconURL(transaction *storage.TransactionsData) string {
	iconURL, ok := constants.IconURLMap[transaction.TransactionSubtype]
	if !ok {
		return constants.IconURLMap["DefaultTransaction"]
	}
	return iconURL
}

// getTransactionCategoryAndIconURL gets the transaction category
// nolint: funlen
func getTransactionCategoryAndIconURL(transaction *storage.TransactionsData) (*api.Category, string) {
	switch transaction.TransactionType {
	case constants.PocketTransactionTypes[0]:
		return getCustomCategoryAndIconURLForTransferMoney(transaction)
	case constants.SendMoneyFeeTransactionType:
		return &api.Category{
			Name: "Transfer Fee",
		}, constants.IconURLMap[constants.TransferFee]
	case "SEND_MONEY":
		return &api.Category{
			Name: "Transfer Out",
		}, constants.IconURLMap[constants.TransferOut]
	case "RECEIVE_MONEY":
		return &api.Category{
			Name: "Transfer In",
		}, constants.IconURLMap[constants.TransferIn]
	case "INTEREST_PAYOUT":
		return &api.Category{}, constants.IconURLMap[constants.InterestPayout]
	case "TAX_PAYOUT":
		return &api.Category{}, constants.IconURLMap[constants.TaxOnInterest]
	case "ADJUSTMENT":
		return &api.Category{Name: "Adjustment Transaction"}, constants.IconURLMap[constants.Adjustment]
	default:
		return &api.Category{}, constants.IconURLMap["DefaultTransaction"]
	}
}

func getCustomCategoryAndIconURLForTransferMoney(transaction *storage.TransactionsData) (*api.Category, string) {
	switch transaction.TransactionSubtype {
	case constants.PocketFundingTransactionSubType:
		if transaction.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Withdrawal"}, constants.IconURLMap[constants.TransferOut]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	case constants.PocketWithdrawalTransactionSubType:
		if transaction.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Withdrawal"}, constants.IconURLMap[constants.PocketWithdrawalTransactionSubType]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	case constants.IntraBank:
		if transaction.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Transfer Out"}, constants.IconURLMap[constants.TransferOut]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	default:
		return &api.Category{}, constants.IconURLMap["DefaultTransaction"]
	}
}

// getCardTransactionStatus will return the status from card transaction detail table
func getCardTransactionStatus(cardDetailData *storage.CardTransactionDetail) string {
	if cardDetailData != nil {
		/*
		 *  To enable product requirement of displaying status as `PROCESSING` when authorization is completed but
		 *	not settled yet, `AUTHORIZED` status is remapped to `PROCESSING`.
		 */
		if cardDetailData.Status == constants.AuthorizedStatus {
			return constants.ProcessingStatus
		}

		return cardDetailData.Status
	}
	return ""
}

// func fetchReorderedClientBatchIDPosition(idPositionMap map[string]int, clientBatchIDOrder []string) map[string]int {
//	for i, item := range clientBatchIDOrder {
//		idPositionMap[item] = i
//	}
//	return idPositionMap
// }

// getIconURLForMainAccountTransfers will return images involving main account related transfers
// nolint:unparam
func getIconURLForCardsTransaction(transactionType string, transactionSubtype string) string {
	// TODO: Return icon url based on transaction type and subtype. Currently returns default.
	return constants.IconURLMap["DefaultTransaction"]
}

// Build card transaction detail response from storage.CardTransactionDetail
func buildCardTransactionDetailResponse(ctx context.Context, cardTransactionDetail *storage.CardTransactionDetail, transaction *storage.TransactionsData) *dto.CxCardTransactionDetail {
	metaData := make(map[string]interface{})

	if cardTransactionDetail == nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, "No card transaction details provided")
		return &dto.CxCardTransactionDetail{}
	}

	bankFee := presenterhelper.GetBankFee(ctx, transaction, cardTransactionDetail)
	exchangeRate := presenterhelper.GetExchangeRate(ctx, transaction, cardTransactionDetail)
	settlementDate := presenterhelper.GetSettlementDate(cardTransactionDetail)

	cardTransactionDetailResponse := &dto.CxCardTransactionDetail{
		CardID:         cardTransactionDetail.CardID,
		TailCardNumber: "••" + cardTransactionDetail.TailCardNumber, // prepend •• to the tail card number
		BankFee:        bankFee,
		ExchangeRate:   exchangeRate,
		SettlementDate: settlementDate,
	}

	unmarshallErr := json.Unmarshal(cardTransactionDetail.Metadata, &metaData)
	if unmarshallErr != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, "Unmarshalling for card transaction metadata failed", slog.Error(unmarshallErr))
		return cardTransactionDetailResponse
	}

	if cardTransactionDetail.TransferType == "REFUND" && metaData["refundAmount"] != "" {
		cardTransactionDetailResponse.CardRefundDetail = &dto.CardRefundDetail{
			OriginalChargeID: fmt.Sprint(metaData["OriginalChargeID"]),
		}
	}
	return cardTransactionDetailResponse
}

func mapCounterParties(dtoCounterParties []dto.CounterParty) []api.CounterParty {
	var counterParties []api.CounterParty
	for _, dtoCounterParty := range dtoCounterParties {
		counterParties = append(counterParties, api.CounterParty{
			DisplayName:        dtoCounterParty.DisplayName,
			AccountNumber:      dtoCounterParty.AccountNumber,
			TransactionDetails: dtoCounterParty.TransactionDetails,
		})
	}
	return counterParties
}

// ParseTransactionHistoryForCXResponse : convert response to GetTransactionsHistoryCXResponse DTO.
// nolint: dupl
func ParseTransactionHistoryForCXResponse(response *dto.TransactionsHistorySearchResponse) *api.GetTransactionsHistoryCXResponse {
	transactionsResponse := []api.TransactionHistoryCXResponse{}
	for _, transaction := range response.Data {
		cxResponse := api.TransactionHistoryCXResponse{
			TransactionID:     transaction.TransactionID,
			BatchID:           transaction.BatchID,
			IconURL:           transaction.IconURL,
			AccountIconDetail: (*api.AccountIconDetail)(transaction.AccountIconDetail),
			CounterParty:      (*api.CounterParty)(transaction.CounterParty),
			CounterParties:    mapCounterParties(transaction.CounterParties),
			Amount:            transaction.Amount,
			Currency:          transaction.Currency,
			Status:            transaction.Status,
			CreationTimestamp: transaction.CreationTimestamp,
			TransactionCode: &api.TransactionCode{
				Domain:  transaction.TransactionCode.Domain,
				Type:    transaction.TransactionCode.Type,
				SubType: transaction.TransactionCode.SubType,
			},
			Tags:                           transaction.Tags,
			TransactionDescription:         transaction.TransactionDescription,
			TransactionRemarks:             transaction.TransactionRemarks,
			IsPartialSettlement:            transaction.IsPartialSettlement,
			CapturedAmountTillDate:         transaction.CapturedAmountTillDate,
			CapturedOriginalAmountTillDate: transaction.CapturedOriginalAmountTillDate,
		}
		if transaction.IsQR {
			cxResponse.TransactionType = api.TransactionType_QR_PAYMENT
		}
		if transaction.TransactionCode.Domain == constants.DebitCard && transaction.CardTransactionDetail != nil {
			cardTransactionDetail := transaction.CardTransactionDetail
			cxResponse.CardTransactionDetail = &api.CxCardTransactionDetail{
				CardID:         cardTransactionDetail.CardID,
				TailCardNumber: cardTransactionDetail.TailCardNumber,
				BankFee:        cardTransactionDetail.BankFee,
				ExchangeRate:   cardTransactionDetail.ExchangeRate,
				SettlementDate: cardTransactionDetail.SettlementDate,
			}
			if cardTransactionDetail.CardRefundDetail != nil {
				cxResponse.CardTransactionDetail.CardRefundDetail = buildCardRefundResponse(cardTransactionDetail)
			}
		}
		transactionsResponse = append(transactionsResponse, cxResponse)
	}
	return &api.GetTransactionsHistoryCXResponse{
		Links: response.Links,
		Data:  transactionsResponse,
	}
}

func buildCardRefundResponse(cardTransactionDetail *dto.CxCardTransactionDetail) *api.CardRefundDetail {
	refundDetail := cardTransactionDetail.CardRefundDetail
	refundResponseDetail := api.CardRefundDetail{
		OriginalChargeID: refundDetail.OriginalChargeID,
	}
	return &refundResponseDetail
}

// getAmountWithSign converts string amount denoting the money flow
func getAmountWithSign(transaction *storage.TransactionsData, loanDetail *storage.LoanDetail) *api.Money {
	amount := loanDetail.Amount
	if presenterhelper.IsDrawDown(transaction) {
		amount = -1 * amount
	}
	return &api.Money{
		CurrencyCode: loanDetail.Currency,
		Val:          amount,
	}
}

// getLendingTransactionStatus will return the status from loan detail table
func getLendingTransactionStatus(loanDetailData *storage.LoanDetail) string {
	return loanDetailData.Status
}

// getIconURLByTransactionType returns the icon URL based on the transaction type
func getIconURLByTransactionType(transaction *storage.TransactionsData) string {
	if helper.IsDrawDown(transaction) {
		return constants.IconURLMap[constants.DrawdownTransactionType]
	} else if helper.IsRepayment(transaction) || helper.IsAkpkRepayment(transaction) {
		return constants.IconURLMap[constants.RepaymentTransactionType]
	}
	return constants.IconURLMap["DefaultTransaction"]
}

func getDrawdownAndRepaymentDetailsDTO(loanDetailData *storage.LoanDetail) (dto.DrawdownDetailsDTO, dto.RepaymentDetailsDTO, error) {
	var drawDownDetails dto.DrawdownDetailsDTO
	var repaymentDetails dto.RepaymentDetailsDTO
	if loanDetailData.TransactionType == constants.DrawdownTransactionType && loanDetailData.DisbursementDetail != nil {
		err := json.Unmarshal(loanDetailData.DisbursementDetail, &drawDownDetails)
		if err != nil {
			return drawDownDetails, repaymentDetails, err
		}
	} else if loanDetailData.TransactionType == constants.RepaymentTransactionType && loanDetailData.RepaymentDetail != nil {
		err := json.Unmarshal(loanDetailData.RepaymentDetail, &repaymentDetails)
		if err != nil {
			return drawDownDetails, repaymentDetails, err
		}
	}
	return drawDownDetails, repaymentDetails, nil
}

func checkIfRppAccount(accountDetails *dto.AccountDetail) bool {
	return accountDetails.Proxy.Channel == constants.RppChannelType
}

// ValidateProductVariant ...
func validateProductVariant(requestProductVariantCode string, productVariantCodes ...string) *servus.ErrorDetail {
	if requestProductVariantCode != "" {
		for _, productVariantCode := range productVariantCodes {
			if productVariantCode == requestProductVariantCode {
				return nil
			}
		}
		return &servus.ErrorDetail{Message: "'productVariantCode' is invalid."}
	}
	return nil
}
