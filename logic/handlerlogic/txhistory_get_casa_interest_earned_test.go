package handlerlogic

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestGetCASAInterestEarnedRequestValidator(t *testing.T) {
	t.Run("valid-request", func(t *testing.T) {
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		errorResponse := GetCASAInterestEarnedRequestValidator(req)
		assert.Equal(t, 0, len(errorResponse))
	})
	t.Run("missing-parameter-accountID", func(t *testing.T) {
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "",
		}
		errorResponse := GetCASAInterestEarnedRequestValidator(req)
		expectResponse := servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		}
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
}

func TestGetCASAInterestEarned(t *testing.T) {
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	t.Run("happy path", func(t *testing.T) {
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASAInterestEarnedValidResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows()[:1], nil)
		storage.InterestAggregateD = mockInterestAggregate

		response, err := GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("empty-response-request", func(t *testing.T) {
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		expectedResp := responses.GetCASAInterestEarnedEmptyResponse()

		actualResp, err := GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("error-response-request", func(t *testing.T) {
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		expectedErr := customErr.DefaultInternalServerError

		actualResp, err := GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}
