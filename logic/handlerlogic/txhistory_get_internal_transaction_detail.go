package handlerlogic

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/presenters/transactionpresenter"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetInternalTransactionDetailRequestValidator validates the request parameters ...
func GetInternalTransactionDetailRequestValidator(req *api.GetInternalTransactionDetailRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}

	if req.TransactionID == "" && req.BatchID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		})
	}

	if req.TransactionID != "" && req.BatchID != "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "Request is invalid. Only 'transactionID' or 'batchID' may be used.",
		})
	}
	return errors
}

// GetInternalTransactionDetail fetches the transactions from the DB and returns the formatted GetInternalTransactionDetailResponse.
func (g *GetTransactionDetailStruct) GetInternalTransactionDetail(ctx context.Context, req *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error) {
	filters := prepareInternalTransactionDataFilters(req)
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetInternalTransactionDetailLogTag, fmt.Sprintf("Error getting transaction history data, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No data present in transactionDataDB")
	}
	finalTransactionData := filterTxnOnAccountPhaseAndType(dbResponse)
	if finalTransactionData == nil {
		return emptyGetInternalTransactionDetailsResponse(), nil
	}
	batchID := dbResponse[0].ClientBatchID

	response, err := g.getInternalTransactionDetailsConditionally(ctx, finalTransactionData, req, batchID)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// Prepares filter for fetching data from the TransactionData
func prepareInternalTransactionDataFilters(req *api.GetInternalTransactionDetailRequest) []data.Condition {
	if req.BatchID == "" {
		filters := []data.Condition{
			data.EqualTo("AccountID", req.AccountID),
			data.EqualTo("ClientTransactionID", req.TransactionID),
		}
		return filters
	}
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("ClientBatchID", req.BatchID),
	}
	return filters
}

// Response in case of TransactionDetails not present
func emptyGetInternalTransactionDetailsResponse() *api.GetInternalTransactionDetailResponse {
	response := &api.GetInternalTransactionDetailResponse{}
	return response
}

// Generate only necessary fields in response depends on type of tx id query
func (g *GetTransactionDetailStruct) getInternalTransactionDetailsConditionally(ctx context.Context, txnData *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) (*api.GetInternalTransactionDetailResponse, error) {
	if req.BatchID == "" {
		return g.getInternalTransactionDetailsResponseGenerator(ctx, txnData, req, batchID)
	}
	return g.getInternalTransactionDetailsResponseForReceipt(ctx, txnData, req, batchID)
}

// nolint: funlen
// Gather the response elements and return the final response
func (g *GetTransactionDetailStruct) getInternalTransactionDetailsResponseGenerator(ctx context.Context, txnData *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) (*api.GetInternalTransactionDetailResponse, error) {
	if txnData.IsCardDomain() {
		return g.getInternalCardTransactionDetailsResponseGenerator(ctx, txnData, req, batchID), nil
	}
	if txnData.IsLendingDomain() || txnData.IsBizLendingDomain() {
		return g.getInternalLoanTransactionDetailsResponseGenerator(ctx, txnData, req, batchID), nil
	}
	return g.getInternalDepositTransactionDetailsResponseGenerator(ctx, txnData, req, batchID)
}

// nolint: funlen
// Gather the response elements and return the final response
func (g *GetTransactionDetailStruct) getInternalTransactionDetailsResponseForReceipt(ctx context.Context, txnData *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) (*api.GetInternalTransactionDetailResponse, error) {
	if txnData.TransactionDomain == constants.DebitCardDomain {
		return g.getInternalCardTransactionDetailsResponseGenerator(ctx, txnData, req, batchID), nil
	}

	var (
		transactionDescription         string
		externalTransactionID          string
		cashAccountCode                string
		serviceType                    string
		displayName                    string
		originDisplayName              string
		counterPartyTransactionDetails = make(map[string]string)
	)

	paymentData := fetchInternalPaymentData(ctx, req, batchID)
	transactionsPaymentDetail := make(map[string]*storage.PaymentDetail)
	if paymentData != nil {
		transactionsPaymentDetail[batchID] = paymentData
	}
	status := getTransactionStatus(txnData, transactionsPaymentDetail)
	pocketIDToNameMap := g.getInternalPocketIDToNameMap(ctx, txnData)
	displayName = getCounterPartyDisplayName(ctx, txnData, transactionsPaymentDetail, pocketIDToNameMap)
	originDisplayName = getAccountDisplayNameFromPaymentDetail(ctx, txnData, transactionsPaymentDetail)
	amount := logic.FormattedAmount(ctx, txnData)
	txnScenarioKey := txnData.GetTxnScenarioKey()

	if paymentData != nil {
		if txnData.TransactionSubtype == constants.RPP && status != constants.PaymentStatusProcessing {
			externalTransactionID = paymentData.GetPaymentMetadata(ctx).ExternalID
			if externalTransactionID == "" {
				return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "Failed to get externalTransactionID")
			}
			cashAccountCode = paymentData.GetPaymentMetadata(ctx).CashAccountCode
			if cashAccountCode == "" {
				cashAccountCode = "CASA" // default cash account code is CASA aka default; transactions will fall back to default
			}
		}
		serviceType = paymentData.GetPaymentMetadata(ctx).ServiceType // serviceType is required to differentiate between QR and non-QR transfer
		counterPartyAccount := paymentData.GetCounterPartyAccount(ctx)
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DescriptionFunc(ctx, paymentData, displayName)
		counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DetailsFunc(ctx, txnData, paymentData)
		counterPartyTransactionDetails["swift_code"] = counterPartyAccount.SwiftCode
		counterPartyTransactionDetails["account_id"] = counterPartyAccount.Number
		counterPartyTransactionDetails["pairing_id"] = counterPartyAccount.PairingID // receipt requires PairingID to get proxy value
		helper.MapNonResidentDetail(ctx, counterPartyTransactionDetails, paymentData)
	} else {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DescriptionFunc(ctx, txnData, displayName)

		transactionDetails := presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DetailsFunc(ctx, txnData, nil)
		serviceType = transactionDetails["service_type"]
		counterPartyTransactionDetails["account_id"] = transactionDetails["counterparty_account_id"]
	}

	batchValueTS := utils.TruncateTillSecond(txnData.BatchValueTimestamp)
	transactionDetail := &api.GetInternalTransactionDetailResponse{
		Amount:        amount,
		Currency:      txnData.TransactionCurrency,
		TransactionID: txnData.ClientTransactionID,
		CreditOrDebit: txnData.DebitOrCredit,
		Status:        status,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp:   &batchValueTS,
		TransactionType:        txnData.TransactionType,
		TransactionSubtype:     txnData.TransactionSubtype,
		ExternalTransactionID:  externalTransactionID,
		CashAccountCode:        cashAccountCode,
		ServiceType:            serviceType,
		TransactionDescription: transactionDescription,
		Account: &api.Account{
			DisplayName: originDisplayName,
		},
	}
	return transactionDetail, nil
}

func (g *GetTransactionDetailStruct) getInternalPocketIDToNameMap(ctx context.Context, txnData *storage.TransactionsData) map[string]string {
	pocketDetails, _ := helper.GetSinglePocketDetailIfRequired(ctx, txnData, g.AccountServiceClient, constants.GetInternalTransactionDetailLogTag)
	return transactionpresenter.BuildPocketIDToNameMap(ctx, pocketDetails, constants.GetInternalTransactionDetailLogTag)
}

func fetchInternalPaymentData(ctx context.Context, req *api.GetInternalTransactionDetailRequest, batchID string) *storage.PaymentDetail {
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("TransactionID", batchID),
	}
	response, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInternalTransactionDetailLogTag, fmt.Sprintf("Error find data in paymentDetailDB, err: %s", err.Error()))
		return nil
	}
	if len(response) == 0 {
		slog.FromContext(ctx).Warn(constants.GetInternalTransactionDetailLogTag, "No data present in the paymentDetailDB")
		return nil
	}
	return response[0]
}

func (g *GetTransactionDetailStruct) getInternalDepositTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) (*api.GetInternalTransactionDetailResponse, error) {
	pocketDetails, _ := helper.GetSinglePocketDetailIfRequired(ctx, transaction, g.AccountServiceClient, constants.GetInternalTransactionDetailLogTag)
	return getInternalDepositTransactionDetail(ctx, transaction, req, batchID, pocketDetails)
}

func getInternalDepositTransactionDetail(ctx context.Context, txnData *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string, pocketDetails []accountService.SavingsPocketDetail) (*api.GetInternalTransactionDetailResponse, error) {
	var (
		transactionDescription         string
		externalTransactionID          string
		displayName                    string
		serviceType                    string
		counterPartyTransactionDetails = make(map[string]string)
	)

	paymentData := fetchInternalPaymentData(ctx, req, batchID)
	transactionsPaymentDetail := make(map[string]*storage.PaymentDetail)
	if paymentData != nil {
		transactionsPaymentDetail[batchID] = paymentData
	}
	status := getTransactionStatus(txnData, transactionsPaymentDetail)
	pocketIDToNameMap := transactionpresenter.BuildPocketIDToNameMap(ctx, pocketDetails, constants.GetInternalTransactionDetailLogTag)
	displayName = getCounterPartyDisplayName(ctx, txnData, transactionsPaymentDetail, pocketIDToNameMap)
	amount := logic.FormattedAmount(ctx, txnData)
	txnScenarioKey := txnData.GetTxnScenarioKey()

	if paymentData != nil {
		if txnData.TransactionSubtype == constants.RPP && status != constants.PaymentStatusProcessing {
			externalTransactionID = paymentData.GetPaymentMetadata(ctx).ExternalID
			if externalTransactionID == "" {
				return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "Failed to get externalTransactionID")
			}
		}
		counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DetailsFunc(ctx, txnData, paymentData)
		if txnData.TransactionSubtype != constants.MooMoo {
			counterPartyAccount := paymentData.GetCounterPartyAccount(ctx)
			counterPartyTransactionDetails["swift_code"] = counterPartyAccount.SwiftCode
			counterPartyTransactionDetails["account_id"] = counterPartyAccount.Number
		}
		helper.MapNonResidentDetail(ctx, counterPartyTransactionDetails, paymentData)
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DescriptionFunc(ctx, paymentData, displayName)
	} else {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DescriptionFunc(ctx, txnData, displayName)

		transactionDetails := presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].DetailsFunc(ctx, txnData, nil)
		serviceType = transactionDetails["service_type"]
		counterPartyTransactionDetails["account_id"] = transactionDetails["counterparty_account_id"]
	}
	batchValueTS := utils.TruncateTillSecond(txnData.BatchValueTimestamp)
	transactionDetail := &api.GetInternalTransactionDetailResponse{
		Amount:        amount,
		Currency:      txnData.TransactionCurrency,
		TransactionID: txnData.ClientTransactionID,
		CreditOrDebit: txnData.DebitOrCredit,
		Status:        status,
		CounterParty: &api.CounterParty{
			DisplayName:        displayName,
			TransactionDetails: counterPartyTransactionDetails,
		},
		TransactionTimestamp:   &batchValueTS,
		TransactionType:        txnData.TransactionType,
		TransactionSubtype:     txnData.TransactionSubtype,
		ExternalTransactionID:  externalTransactionID,
		ServiceType:            serviceType,
		TransactionDescription: transactionDescription,
	}
	return transactionDetail, nil
}
