package logic

import (
	"context"
	errors2 "errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// FetchLoanDetailForLoanTransactionID ...
func FetchLoanDetailForLoanTransactionID(ctx context.Context, accountID string, loanTxnID string, logTag string) (*storage.LoanDetail, error) {
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("LoanTransactionID", loanTxnID),
	}
	response, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil && errors2.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(logTag, "No data present in the loanDetailDB")
		return nil, errors.BuildErrorResponse(errors.ResourceNotFound, "No loan detail data found")
	}
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Error while fetching data from loanDetail, err: %s", err.Error()))
		return nil, errors.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(logTag, "Successfully fetched data from loanDetailDB")
	return response[0], nil
}
