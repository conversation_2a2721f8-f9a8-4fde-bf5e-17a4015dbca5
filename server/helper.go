package server

import (
	"reflect"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
)

func registerDefaults(appCfg *config.AppConfig) {
	emptyLocaleConfig := config.Locale{}
	if reflect.DeepEqual(emptyLocaleConfig, appCfg.Locale) {
		appCfg.Locale.Currency = constants.DefaultCurrency
	}

	if reflect.DeepEqual(emptyLocaleConfig, appCfg.Locale) {
		appCfg.Locale.Language = constants.DefaultLanguage
	}
}
