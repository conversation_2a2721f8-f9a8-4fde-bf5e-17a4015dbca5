package config

import "gitlab.com/gx-regional/dbmy/transaction-history/featureflag"

var _ featureflag.Repo = (*FeatureFlagRepoImpl)(nil)

// FeatureFlag ...
type FeatureFlag struct {
	Enabled bool `json:"enabled"`
}

// FeatureFlags is the data structure that holds the feature flags
type FeatureFlags struct {
	// InsertTransactionsByBatch ...
	InsertTransactionsByBatch     FeatureFlag `json:"InsertTransactionsByBatch"`
	ReplicaRead                   FeatureFlag `json:"replicaRead"`
	CheckStatementStatus          FeatureFlag `json:"checkStatementStatus"`
	BatchifyDailyInterestAgg      FeatureFlag `json:"batchifyDailyInterestAgg"`
	BatchifyOneTimeInterestAgg    FeatureFlag `json:"batchifyOneTimeInterestAgg"`
	RetryableDepositCoreStream    FeatureFlag `json:"retryableDepositCoreStream"`
	RetryableDepositBalanceStream FeatureFlag `json:"retryableDepositBalanceStream"`
	DepositCoreStream             FeatureFlag `json:"depositCoreStream"`
	DepositBalanceStream          FeatureFlag `json:"depositBalanceStream"`
	BizAuthorisation              FeatureFlag `json:"bizAuthorisation"`
	// UpdatePostingInstructionBatchForLending ...
	UpdatePostingInstructionBatchForLending FeatureFlag `json:"updatePostingInstructionBatchForLending"`
	EnableTransactionsSearchDurationFilter  FeatureFlag `json:"enableTransactionsSearchDurationFilter"`
	EnableBizFlexiCredit                    FeatureFlag `json:"enableBizFlexiCredit"`
	EnableSearchTransactionLimit            FeatureFlag `json:"enableSearchTransactionLimit"`
	EnableExhaustiveCursorPagination        FeatureFlag `json:"enableExhaustiveCursorPagination"`
	EnableExhaustiveCursorPaginationForCX   FeatureFlag `json:"enableExhaustiveCursorPaginationForCX"`
	EnableAuthzWithProfileID                FeatureFlag `json:"enableAuthzWithProfileID"`
	RetryableInterestAggregationStream      FeatureFlag `json:"retryableInterestAggregationStream"`
	EnableInterestAggregateV2               FeatureFlag `json:"enableInterestAggregateV2"`
	EnableBoostPocketNameQuery              FeatureFlag `json:"enableBoostPocketNameQuery"`
}

// FeatureFlagRepoImpl implements featureflag.Repo
type FeatureFlagRepoImpl struct {
	features FeatureFlags
}

// IsReplicaReadEnabled ...
func (f *FeatureFlagRepoImpl) IsReplicaReadEnabled() bool {
	return f.features.ReplicaRead.Enabled
}

// IsRetryableDepositCoreStreamEnabled ...
func (f *FeatureFlagRepoImpl) IsRetryableDepositCoreStreamEnabled() bool {
	return f.features.RetryableDepositCoreStream.Enabled
}

// IsRetryableDepositBalanceStreamEnabled ...
func (f *FeatureFlagRepoImpl) IsRetryableDepositBalanceStreamEnabled() bool {
	return f.features.RetryableDepositBalanceStream.Enabled
}

// IsInsertTransactionsByBatchEnabled ...
func (f *FeatureFlagRepoImpl) IsInsertTransactionsByBatchEnabled() bool {
	return f.features.InsertTransactionsByBatch.Enabled
}

// IsCheckStatementStatusEnabled ...
func (f *FeatureFlagRepoImpl) IsCheckStatementStatusEnabled() bool {
	return f.features.CheckStatementStatus.Enabled
}

// IsUpdatePostingInstructionBatchForLendingEnabled ...
func (f *FeatureFlagRepoImpl) IsUpdatePostingInstructionBatchForLendingEnabled() bool {
	return f.features.UpdatePostingInstructionBatchForLending.Enabled
}

// IsBatchifyDailyInterestAggEnabled ...
func (f *FeatureFlagRepoImpl) IsBatchifyDailyInterestAggEnabled() bool {
	return f.features.BatchifyDailyInterestAgg.Enabled
}

// IsRetryableInterestAggStreamEnabled ...
func (f *FeatureFlagRepoImpl) IsRetryableInterestAggStreamEnabled() bool {
	return f.features.RetryableInterestAggregationStream.Enabled
}

// IsBatchifyOneTimeInterestAggEnabled ...
func (f *FeatureFlagRepoImpl) IsBatchifyOneTimeInterestAggEnabled() bool {
	return f.features.BatchifyOneTimeInterestAgg.Enabled
}

// IsDepositCoreStreamEnabled ...
func (f *FeatureFlagRepoImpl) IsDepositCoreStreamEnabled() bool {
	return f.features.DepositCoreStream.Enabled
}

// IsDepositBalanceStreamEnabled ...
func (f *FeatureFlagRepoImpl) IsDepositBalanceStreamEnabled() bool {
	return f.features.DepositBalanceStream.Enabled
}

// IsTransactionsSearchDurationFilterEnabled ...
func (f *FeatureFlagRepoImpl) IsTransactionsSearchDurationFilterEnabled() bool {
	return f.features.EnableTransactionsSearchDurationFilter.Enabled
}

// IsBizAuthorisationEnabled ...
func (f *FeatureFlagRepoImpl) IsBizAuthorisationEnabled() bool {
	return f.features.BizAuthorisation.Enabled
}

// IsBizFlexiCreditEnabled ...
func (f *FeatureFlagRepoImpl) IsBizFlexiCreditEnabled() bool {
	return f.features.EnableBizFlexiCredit.Enabled
}

// IsTransactionsSearchDurationLimitEnabled ...
func (f *FeatureFlagRepoImpl) IsTransactionsSearchDurationLimitEnabled() bool {
	return f.features.EnableSearchTransactionLimit.Enabled
}

// IsExhaustiveCursorPaginationEnabled ...
func (f *FeatureFlagRepoImpl) IsExhaustiveCursorPaginationEnabled() bool {
	return f.features.EnableExhaustiveCursorPagination.Enabled
}

// IsExhaustiveCursorPaginationEnabledForCX ...
func (f *FeatureFlagRepoImpl) IsExhaustiveCursorPaginationEnabledForCX() bool {
	return f.features.EnableExhaustiveCursorPaginationForCX.Enabled
}

// IsAuthzWithProfileIDEnabled ...
func (f *FeatureFlagRepoImpl) IsAuthzWithProfileIDEnabled() bool {
	return f.features.EnableAuthzWithProfileID.Enabled
}

// IsInterestAggregateV2Enabled ...
func (f *FeatureFlagRepoImpl) IsInterestAggregateV2Enabled() bool {
	return f.features.EnableInterestAggregateV2.Enabled
}

// IsBoostPocketNameQueryEnabled ...
func (f *FeatureFlagRepoImpl) IsBoostPocketNameQueryEnabled() bool {
	return f.features.EnableBoostPocketNameQuery.Enabled
}
