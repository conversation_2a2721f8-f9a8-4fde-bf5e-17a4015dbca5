// Package config provides the structs to model the config file.
package config

import (
	"github.com/myteksi/hystrix-go/hystrix"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"

	rsconfig "gitlab.myteksi.net/dakota/common/retryable-stream/sqs/config"
)

var tenant string

const (
	mySQLDatetimePrecision = 6
)

// SetTenant ...
func SetTenant(currentTenant string) {
	tenant = currentTenant
}

// GetTenant ...
func GetTenant() string {
	return tenant
}

var statementReadyByDays int

// SetStatementReadyByDays ...
func SetStatementReadyByDays(currentStatementReadyByDays int) {
	statementReadyByDays = currentStatementReadyByDays
}

// GetStatementReadyByDays ...
func GetStatementReadyByDays() int {
	return statementReadyByDays
}

// maxCursorLimit the maximum number of pages or iterations allowed when fetching paginated data
var maxCursorLimit int

// SetMaxCursorLimit ...
func SetMaxCursorLimit(currentMaxCursorLimit int) {
	maxCursorLimit = currentMaxCursorLimit
}

// GetMaxCursorLimit ...
func GetMaxCursorLimit() int {
	return maxCursorLimit
}

// executionSleepDurationInMs the number of ms to pause execution (0.5sec)
var executionSleepDurationInMs int

// SetExecutionSleepDurationInMs ...
func SetExecutionSleepDurationInMs(currentSleepDurationInMs int) {
	executionSleepDurationInMs = currentSleepDurationInMs
}

// GetExecutionSleepDurationInMs ...
func GetExecutionSleepDurationInMs() int {
	return executionSleepDurationInMs
}

// Loader ...
type Loader struct{}

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	PaymentsEngineKafka                   *KafkaConfig                            `json:"peConfig"`
	DepositsCoreKafka                     *KafkaConfig                            `json:"dcConfig"`
	DepositsBalanceKafka                  *KafkaConfig                            `json:"dbConfig"`
	DigicardTxnKafka                      *KafkaConfig                            `json:"digicardTxnKafkaConfig"`
	LoanCoreTxKafka                       *KafkaConfig                            `json:"loanCoreTxKafkaConfig"`
	LoanExpTxKafka                        *KafkaConfig                            `json:"loanExpTxKafkaConfig"`
	InterestAggKafka                      *KafkaConfig                            `json:"interestAggKafkaConfig"`
	CustomerMasterConfig                  CustomerMasterConfig                    `json:"customerMasterConfig"`
	AccountServiceConfig                  AccountServiceConfig                    `json:"accountServiceConfig"`
	PairingServiceConfig                  *ServiceConfig                          `json:"pairingServiceConfig"`
	PaymentExperienceConfig               PaymentExperienceConfig                 `json:"paymentExperienceConfig"`
	TransactionStatementsConfig           TransactionStatementsConfig             `json:"transactionStatementsConfig"`
	DepositsExpConfig                     *ServiceConfig                          `json:"depositsExpConfig"`
	TransactionHistoryClientConfig        ClientConfig                            `json:"transactionHistoryClientConfig"`
	TransactionHistoryServiceConfig       TransactionHistoryServiceConfig         `json:"transactionHistoryServiceConfig"`
	IconConfig                            IconConfig                              `json:"iconConfig"`
	WorkerConfig                          WorkerConfig                            `json:"workerConfig"`
	PopulateInterestAggregateOneTimerConf PopulateInterestAggregateOneTimerConfig `json:"populateInterestAggregateOneTimer"`
	SnowflakeConfig                       SnowflakeConfig                         `json:"snowflakeConfig"`
	SQSConfig                             rsconfig.SQSConfig                      `json:"sqsConfig"`
	STM453TransactionInfoFeatureFlag      STM453TransactionInfoFeatureFlag        `json:"stm453TransactionInfoFeatureFlag"`
	// this is a release toggle for short term usage, and the toggling logic should be removed once the feat is released
	DigicardFeatureFlag                       DigicardFeatureFlag                       `json:"digicardFeatureFlag"`
	FeatureFlags                              FeatureFlags                              `json:"featureFlags"`
	Locale                                    Locale                                    `json:"locale"`
	Tenant                                    string                                    `json:"tenant"`
	ExhaustivePaginationConfig                ExhaustivePaginationConfig                `json:"exhaustivePaginationConfig"`
	CustomerExperienceConfig                  CustomerExperienceConfig                  `json:"customerExperienceConfig"`
	CachingConfig                             CachingConfig                             `json:"cachingConfig"`
	InterestAggregationConfig                 InterestAggregationConfig                 `json:"interestAggregationConfig"`
	PopulateInterestAggregateV2OneTimerConfig PopulateInterestAggregateV2OneTimerConfig `json:"populateInterestAggregateV2OneTimerConfig"`
}

// ServiceConfig ...
type ServiceConfig struct {
	ServiceName    string     `json:"serviceName"`
	BaseURL        string     `json:"baseURL"`
	CircuitBreaker *CBSetting `json:"circuitBreaker"`
}

// KafkaConfig ...
type KafkaConfig struct {
	Brokers         []string             `json:"brokers"`
	TopicName       string               `json:"topicName"`
	ClusterType     string               `json:"clusterType"`
	EnableTLS       bool                 `json:"enableTLS"`
	InitOffset      sndconfig.OffsetType `json:"initOffset"`
	ClientID        string               `json:"clientID"`
	Enable          bool                 `json:"enable"`
	ConsumerGroupID string               `json:"consumerGroupID"`
}

// ClientConfig ...
type ClientConfig struct {
	MaxIdleConnsPerHost           int `json:"maxIdleConnsPerHost"`
	IdleConnTimeoutInMillis       int `json:"idleConnTimeoutInSMillis"`
	TimeoutInMillis               int `json:"timeoutInMillis"`
	RequestLogLockTimeoutInMillis int `json:"requestLogLockTimeoutInMillis"`
}

// CustomerMasterConfig ...
type CustomerMasterConfig struct {
	BaseURL string `json:"baseURL"`
}

// CustomerExperienceConfig ...
type CustomerExperienceConfig struct {
	BaseURL string `json:"baseURL"`
}

// AccountServiceConfig ...
type AccountServiceConfig struct {
	BaseURL string `json:"baseURL"`
}

// CBSetting is the circuit breaker configurations for storage
// It adds ErrorHandler to ignore context.Canceled errors.
type CBSetting struct {
	hystrix.CommandConfig
	IgnoredHTTPCode []int `json:"ignoredHTTPCode"`
}

// PaymentExperienceConfig ...
type PaymentExperienceConfig struct {
	BaseURL string `json:"baseURL"`
}

// TransactionStatementsConfig ...
type TransactionStatementsConfig struct {
	BaseURL              string `json:"baseURL"`
	StatementReadyByDays int    `json:"statementReadyByDays"`
}

// TransactionHistoryServiceConfig ...
// nolint
type TransactionHistoryServiceConfig struct {
	DefaultCurrency                          string `json:"defaultCurrency"`
	PastMonthsThresholdForCalendarActivity   int    `json:"pastMonthsThresholdForCalendarActivity"`
	MaxRetryForBalanceStream                 int    `json:"maxRetryForBalanceStream"`
	SleepDurationForBalanceStream            int    `json:"sleepDurationForBalanceStream"`
	TransactionsSearchFilterDurationInMonths int    `json:"transactionsSearchFilterDurationInMonths"`
	TransactionsSearchDurationLimitInMonths  int    `json:"transactionsSearchDurationLimitInMonths"`
	APIRequestTimeoutInSec                   int    `json:"apiRequestTimeoutInSec"`
}

// ExhaustivePaginationConfig ...
type ExhaustivePaginationConfig struct {
	MaxCursorLimit             int `json:"maxCursorLimit"`
	ExecutionSleepDurationInMs int `json:"executionSleepDurationInMs"`
}

// IconConfig ...
type IconConfig struct {
	InterestEarn            string `json:"interestEarn"`
	BankAdjustment          string `json:"bankAdjustment"`
	PocketFunding           string `json:"pocketFunding"`
	MainMoneyIn             string `json:"mainMoneyIn"`
	MainMoneyOut            string `json:"mainMoneyOut"`
	MainPocketWithdrawal    string `json:"mainPocketWithdrawal"`
	PocketPocketWithdrawal  string `json:"pocketPocketWithdrawal"`
	SavingsPocketTransfer   string `json:"savingsPocketTransfer"`
	SavingsPocketWithdrawal string `json:"savingsPocketWithdrawal"`
	Grab                    string `json:"grab"`
	Rewards                 string `json:"rewards"`
	DefaultTransaction      string `json:"defaultTransaction"`
	Withdrawal              string `json:"withdrawal"`
	TransferIn              string `json:"transferIn"`
	TransferOut             string `json:"transferOut"`
	TransferFee             string `json:"transferFee"`
	InterestPayout          string `json:"interestPayout"`
	InterestReversal        string `json:"interestReversal"`
	TaxOnInterest           string `json:"taxOnInterest"`
	Adjustment              string `json:"adjustment"`
	Mastercard              string `json:"mastercard"`
	Merchant                string `json:"merchant"`
	LendingDrawdown         string `json:"lendingDrawdown"`
	LendingRepayment        string `json:"lendingRepayment"`
	Repayment               string `json:"repayment"`
	Drawdown                string `json:"drawdown"`
	Insurance               string `json:"insurance"`
}

// WorkerConfig ...
type WorkerConfig struct {
	UpdateInterestAggregateDBForDailyInterestPayout UpdateInterestAggregateWorker `json:"updateInterestAggregateWorkerConf"`
}

// UpdateInterestAggregateWorker ...
type UpdateInterestAggregateWorker struct {
	LockDurationInMinutes             int    `json:"lockDurationInMinutes"`
	CronExpression                    string `json:"cronExpression"`
	Enabled                           bool   `json:"enabled"`
	BatchSizeInRow                    int    `json:"batchSizeInRow"`
	BatchCOPInSecond                  int    `json:"batchCOPInSecond"`
	UpdateBatchSizeInRow              int    `json:"updateBatchSizeInRow"`
	ParentBatchSubPaginationInSeconds int    `json:"parentBatchSubPaginationInSeconds"`
	RefreshAggregation                bool   `json:"refreshAggregation"`
}

// PopulateInterestAggregateOneTimerConfig ...
type PopulateInterestAggregateOneTimerConfig struct {
	StartDate             string `json:"startDate"`
	EndDate               string `json:"endDate"`
	RunOneTimer           bool   `json:"runOneTimer"`
	BatchSizeInDays       int    `json:"batchSizeInDays"`
	LockDurationInMinutes int    `json:"lockDurationInMinutes"`
	RefreshAggregation    bool   `json:"refreshAggregation"`
	UpdateBatchSizeInRow  int    `json:"updateBatchSizeInRow"`
}

// SnowflakeConfig ...
type SnowflakeConfig struct {
	DSNOPSReader string `json:"dsnOPSReader"`
}

// STM453TransactionInfoFeatureFlag ...
type STM453TransactionInfoFeatureFlag struct {
	Enabled bool `json:"enabled"`
}

// DigicardFeatureFlag ...
type DigicardFeatureFlag struct {
	Enabled bool `json:"enabled"`
}

// Name ...
func (a *AppConfig) Name() string {
	return a.ServiceName
}

// GetOwnerInfo ...
func (a *AppConfig) GetOwnerInfo() servus.OwnerInfo {
	return a.OwnerInfo
}

// LogConfig ...
func (a *AppConfig) LogConfig() *servus.LogConfig {
	return a.Logger
}

// StatsDConfig ...
func (a *AppConfig) StatsDConfig() *servus.StatsDConfig {
	return a.StatsD
}

// GetHost ...
func (a *AppConfig) GetHost() string {
	return a.Host
}

// GetPort ...
func (a *AppConfig) GetPort() int {
	return a.Port
}

// GetDescription ...
func (a *AppConfig) GetDescription() string {
	return a.Description
}

// Load loads your application's configuration
func (l *Loader) Load(appCfg interface{}) error {
	// implement config loading
	return nil
}

// Locale ...
type Locale struct {
	Currency string `json:"currency"`
	Language string `json:"language"`
}

// GetMySQLDatetimePrecision returns datetime precision config (0 -> second, 3 -> ms, 6 -> microsecond) for datetime columns
func (a *AppConfig) GetMySQLDatetimePrecision() int {
	return mySQLDatetimePrecision
}

// NewFeatureFlagRepoImpl ...
func (a *AppConfig) NewFeatureFlagRepoImpl() *FeatureFlagRepoImpl {
	return &FeatureFlagRepoImpl{
		features: a.FeatureFlags,
	}
}

// CachingConfig ...
type CachingConfig struct {
	TTL         int             `json:"ttl"`
	RedisConfig *redis.ConfigV2 `json:"redisConfig"`
}

// InterestAggregationConfig ...
type InterestAggregationConfig struct {
	RetentionDay    int             `json:"retentionDay"`
	StartMonth      string          `json:"startMonth"`
	ExcludeAccounts ExcludeAccounts `json:"excludeAccounts"`
}

// ExcludeAccounts ...
type ExcludeAccounts map[string]bool

// PopulateInterestAggregateV2OneTimerConfig ...
type PopulateInterestAggregateV2OneTimerConfig struct {
	LockDurationInMinutes int    `json:"lockDurationInMinutes"`
	CronExpression        string `json:"cronExpression"`
	BatchSize             int    `json:"batchSize"`
	CheckPoint            string `json:"checkpoint"`
	RunCrossCheck         bool   `json:"runCrossCheck"`
	RunPopulate           bool   `json:"runPopulate"`
}
