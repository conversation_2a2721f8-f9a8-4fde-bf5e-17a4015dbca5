// Package client ..
package client

import (
	"fmt"
	"net/http"
	"reflect"
	"time"

	"github.com/myteksi/hystrix-go/hystrix"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/tracing"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2/client"
	"gitlab.myteksi.net/dakota/klient"
	pairingService "gitlab.myteksi.net/dakota/payment/pairing-service/api/client"
	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api/client"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api/client"
	extActiveProfile "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2"
	"gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/activeprofile"
	"gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/klientopts"
	customerExperience "gitlab.myteksi.net/dbmy/customer-experience/api/client"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2/client"
	transactionStatements "gitlab.myteksi.net/dbmy/transaction-statements/api/client"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
)

// RegisterClients ...
func RegisterClients(app *servus.Application) {
	app.MustRegister("client.customerMaster", customerMasterClient)
	app.MustRegister("client.customerMasterDBMY", customerMasterDBMYClient)
	app.MustRegister("client.accountService", accountServiceClient)
	app.MustRegister("client.paymentExperience", paymentExperienceClient)
	app.MustRegister("client.transactionStatements", transactionStatementsClient)
	app.MustRegister("client.pairingService", pairingServiceClient)
	app.MustRegister("client.customerExperienceClient", CustomerExperienceClient)
	app.MustRegister("client.externalActiveProfileClient", ExternalActiveProfileClient)
}

func customerMasterClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*customerMaster.CustomerMasterClient, error) {
	return customerMaster.NewCustomerMasterClient(
		appCfg.CustomerMasterConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

func customerMasterDBMYClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*customerMasterDBMY.CustomerMasterClient, error) {
	return customerMasterDBMY.NewCustomerMasterClient(
		appCfg.CustomerMasterConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

func accountServiceClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*accountService.AccountServiceClient, error) {
	return accountService.NewAccountServiceClient(
		appCfg.AccountServiceConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

func paymentExperienceClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*paymentExperience.PaymentExperienceClient, error) {
	return paymentExperience.NewPaymentExperienceClient(
		appCfg.PaymentExperienceConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

func transactionStatementsClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*transactionStatements.TransactionStatementsClient, error) {
	return transactionStatements.NewTransactionStatementsClient(
		appCfg.TransactionStatementsConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

func makeHTTPClient(appCfg *config.AppConfig) *http.Client {
	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: appCfg.TransactionHistoryClientConfig.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(appCfg.TransactionHistoryClientConfig.IdleConnTimeoutInMillis) * time.Millisecond,
		},
		Timeout: time.Duration(appCfg.TransactionHistoryClientConfig.TimeoutInMillis) * time.Millisecond,
	}
	return httpClient
}

func pairingServiceClient(appCfg *config.AppConfig, logger slog.YallLogger, tracer tracing.Tracer, app *servus.Application) (*pairingService.PairingServiceClient, error) {
	return pairingService.NewPairingServiceClient(
		appCfg.PairingServiceConfig.BaseURL,
		klientOptions("PairingServiceConfig", appCfg, tracer, logger, true, true, app.GetStatsD())...,
	)
}

// nolint:unparam,funlen,gocognit
func klientOptions(client string, appCfg *config.AppConfig, tracer tracing.Tracer, logger slog.YallLogger, withHealthCheck, propagateCommonHeaders bool, statsClient statsd.Client) []klient.Option {
	r := reflect.ValueOf(appCfg)
	f := reflect.Indirect(r).FieldByName(client)

	if !f.IsValid() {
		panic(fmt.Sprintf("invalid client name %s", client))
	}

	conf, ok := f.Interface().(*config.ServiceConfig)

	if !ok {
		panic(fmt.Sprintf("client %s does not have valid client config", client))
	}
	opts := make([]klient.Option, 0)
	if conf.ServiceName != "" {
		opts = append(opts, klient.WithServiceName(conf.ServiceName))
	}

	if statsClient != nil {
		opts = append(opts, klient.WithStatsDClient(statsClient))
	}

	if conf.CircuitBreaker != nil {
		opts = append(opts, klient.WithCircuitConfig(map[string]hystrix.CommandConfig{
			conf.ServiceName: conf.CircuitBreaker.CommandConfig,
		}))

		if len(conf.CircuitBreaker.IgnoredHTTPCode) >= 0 {
			ignoredHTTPCodeMap := make(map[int]bool)
			for _, code := range conf.CircuitBreaker.IgnoredHTTPCode {
				ignoredHTTPCodeMap[code] = true
			}
			opts = append(opts, klient.WithCircuitOptions([]circuitbreaker.Option{
				circuitbreaker.WithUserErrorHandler(func(err error) (nonThreat bool, errOut error) {
					if e, isAPIError := err.(servus.APIError); isAPIError {
						httpCode, _ := e.Info()
						if ignoredHTTPCodeMap[httpCode] {
							return true, err
						}
					}
					return false, err
				}),
			}))
		}
	}

	if tracer != nil {
		opts = append(opts, klient.WithTracing(tracer))
	}

	if logger != nil {
		opts = append(opts, klient.WithLogger(logger))
	}

	if !withHealthCheck {
		opts = append(opts, klient.WithoutHealthCheck())
	}

	if propagateCommonHeaders {
		opts = append(opts, klient.PropagateCommonHeaders())
	}

	return opts
}

// CustomerExperienceClient ...
func CustomerExperienceClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*customerExperience.CustomerExperienceClient, error) {
	return customerExperience.NewCustomerExperienceClient(
		appCfg.CustomerExperienceConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.PropagateCommonHeaders(),
	)
}

// ExternalActiveProfileClient ...
func ExternalActiveProfileClient(appConfig *config.AppConfig, app *servus.Application) (activeprofile.ExternalActiveProfile, error) {
	opts := klientopts.CreateKlientOptions(string(servicename.CustomerMaster), nil, app.GetTracer(), app.GetLogger(), true, true, app.GetStatsD())
	return activeprofile.NewClient(&extActiveProfile.ServiceConfig{
		BaseURL:   appConfig.CustomerExperienceConfig.BaseURL,
		GroupName: "dbmy",
	}, opts...)
}
