// Package errors contains HTTP error codes and error messages
package errors

import (
	"net/http"

	"gitlab.myteksi.net/dakota/servus/v2"
)

// ErrorCode ...
type ErrorCode string

// Error codes for application service
const (
	// 400
	BadRequest ErrorCode = "BAD_REQUEST"

	// 401
	Unauthorized ErrorCode = "UNAUTHORIZED"

	// 404
	ResourceNotFound ErrorCode = "RESOURCE_NOT_FOUND"

	// 500
	InternalServerError ErrorCode = "INTERNAL_SERVER_ERROR"
)

var errorCodeToStatusCode = map[ErrorCode]int{
	// 400
	BadRequest: http.StatusBadRequest,

	// 401
	Unauthorized: http.StatusUnauthorized,

	// 404
	ResourceNotFound: http.StatusNotFound,

	// 500
	InternalServerError: http.StatusInternalServerError,
}

var errorCodeToErrorMessage = map[ErrorCode]string{
	// 500
	InternalServerError: "There is a problem on our end. Please try again later",
}

// DefaultInternalServerError returns default error message for unexpected error.
var DefaultInternalServerError = servus.ServiceError{
	Code:     string(InternalServerError),
	Message:  InternalServerError.ErrorMessage(),
	HTTPCode: InternalServerError.HTTPStatusCode(),
}

// HTTPStatusCode returns HTTP code based on code.
func (code ErrorCode) HTTPStatusCode() int {
	if statusCode, ok := errorCodeToStatusCode[code]; ok {
		return statusCode
	}
	return http.StatusBadRequest
}

// ErrorMessage returns error message based on code.
func (code ErrorCode) ErrorMessage() string {
	if errorMessage, ok := errorCodeToErrorMessage[code]; ok {
		return errorMessage
	}
	return errorCodeToErrorMessage[InternalServerError]
}

// BuildErrorResponse creates servus error
func BuildErrorResponse(code ErrorCode, message string) servus.ServiceError {
	return servus.ServiceError{
		Code:     string(code),
		Message:  message,
		HTTPCode: code.HTTPStatusCode(),
	}
}

// BuildCustomErrorResponse creates servus error
func BuildCustomErrorResponse(httpStatus int, code string, msg string) servus.ServiceError {
	return servus.ServiceError{
		HTTPCode: httpStatus,
		Code:     code,
		Message:  msg,
	}
}
