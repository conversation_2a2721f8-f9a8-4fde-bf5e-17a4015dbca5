package errors

import "strconv"

// CustomError ...
type CustomError struct {
	Code    int
	Message string
}

// CodeString returns custom error code as string
func (e CustomError) CodeString() string {
	return strconv.FormatInt(int64(e.Code), 10)
}

var (
	// ErrCodeUnknown ...
	ErrCodeUnknown = CustomError{
		Code:    3000,
		Message: "Unknown Error",
	}

	// ErrMissingIdentifier ...
	ErrMissingIdentifier = CustomError{
		Code:    3001,
		Message: "Missing customer identifier or txn identifier",
	}

	// ErrInvalidIdentifier ...
	ErrInvalidIdentifier = CustomError{
		Code:    3002,
		Message: "Invalid identifiers",
	}

	// ErrInvalidFilter ...
	ErrInvalidFilter = CustomError{
		Code:    3003,
		Message: "Invalid filters",
	}

	// ErrMissingTimestampFilter ...
	ErrMissingTimestampFilter = CustomError{
		Code:    3004,
		Message: "Missing timestamp filters",
	}

	// ErrInvalidTxnStatus ...
	ErrInvalidTxnStatus = CustomError{
		Code:    3005,
		Message: "Invalid transaction status",
	}

	// ErrInvalidTxnType ...
	ErrInvalidTxnType = CustomError{
		Code:    3006,
		Message: "Invalid transaction type",
	}

	// ErrInvalidTxnSubtype ...
	ErrInvalidTxnSubtype = CustomError{
		Code:    3007,
		Message: "Invalid transaction subtype",
	}

	// ErrInvalidTxnAmountRange ...
	ErrInvalidTxnAmountRange = CustomError{
		Code:    3008,
		Message: "Invalid transaction amount range",
	}

	// ErrInvalidTxnAmount ...
	ErrInvalidTxnAmount = CustomError{
		Code:    3009,
		Message: "Invalid transaction amount",
	}

	// ErrInvalidPageSize ...
	ErrInvalidPageSize = CustomError{
		Code:    3010,
		Message: "Invalid page size",
	}

	// ErrJWTNotFound ...
	ErrJWTNotFound = CustomError{
		Code:    1224,
		Message: "JWT not found",
	}
)
