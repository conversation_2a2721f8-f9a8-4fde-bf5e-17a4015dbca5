// Package utils ...
package utils

import (
	"github.com/samber/lo"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

// CustomerActiveIdentity ...
type CustomerActiveIdentity struct {
	SafeID string
	BIF    string
	CIF    string
}

// IsBizScope ...
func (c *CustomerActiveIdentity) IsBizScope() bool {
	return c.BIF != ""
}

// GetCustomerID ...
func (c *CustomerActiveIdentity) GetCustomerID() string {
	if c.IsBizScope() {
		return c.BIF
	}
	return c.CIF
}

// FindLatestBizProfile ...
func FindLatestBizProfile(profiles []customerMasterDBMY.BusinessRelationship) (customerMasterDBMY.BusinessRelationship, bool) {
	res := lo.MaxBy(profiles, func(nextProfile, currentProfile customerMasterDBMY.BusinessRelationship) bool {
		return nextProfile.CreatedAt.After(*currentProfile.CreatedAt)
	})
	if lo.IsEmpty(res.Bif) {
		return customerMasterDBMY.BusinessRelationship{}, false
	}
	return res, true
}
