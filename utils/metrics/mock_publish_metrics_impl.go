// Code generated by mockery v2.39.1. DO NOT EDIT.

package metrics

import (
	deposits_balance_event "gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"
	digicard_transaction_tx "gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_transaction_tx"

	mock "github.com/stretchr/testify/mock"

	payment_engine_tx "gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"

	statsd "gitlab.myteksi.net/dakota/servus/v2/statsd"

	storage "gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// MockPublishMetricsImpl is an autogenerated mock type for the PublishMetricsImpl type
type MockPublishMetricsImpl struct {
	mock.Mock
}

// PublishDepositsCoreBalanceMetrics provides a mock function with given fields: data, stats, err
func (_m *MockPublishMetricsImpl) PublishDepositsCoreBalanceMetrics(data *deposits_balance_event.DepositsBalanceEvent, stats statsd.Client, err error) {
	_m.Called(data, stats, err)
}

// PublishDepositsCoreMetrics provides a mock function with given fields: data, stats
func (_m *MockPublishMetricsImpl) PublishDepositsCoreMetrics(data *storage.TransactionsData, stats statsd.Client) {
	_m.Called(data, stats)
}

// PublishDigicardTxnMetrics provides a mock function with given fields: data, stats
func (_m *MockPublishMetricsImpl) PublishDigicardTxnMetrics(data *digicard_transaction_tx.DigicardTransactionTx, stats statsd.Client) {
	_m.Called(data, stats)
}

// PublishLoanCoreTxMetrics provides a mock function with given fields: data, stats
func (_m *MockPublishMetricsImpl) PublishLoanCoreTxMetrics(data *storage.TransactionsData, stats statsd.Client) {
	_m.Called(data, stats)
}

// PublishPaymentEngineMetrics provides a mock function with given fields: data, stats, err
func (_m *MockPublishMetricsImpl) PublishPaymentEngineMetrics(data *payment_engine_tx.PaymentEngineTx, stats statsd.Client, err error) {
	_m.Called(data, stats, err)
}

func (_m *MockPublishMetricsImpl) PublishDepositsCoreForInterestAggMetrics(txnType string, stats statsd.Client, isSuccess bool) {
	_m.Called(txnType, stats, isSuccess)
}

// NewMockPublishMetricsImpl creates a new instance of MockPublishMetricsImpl. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPublishMetricsImpl(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPublishMetricsImpl {
	mock := &MockPublishMetricsImpl{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
