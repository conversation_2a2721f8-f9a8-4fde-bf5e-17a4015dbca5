package utils

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	accountService "gitlab.myteksi.net/dakota/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceDBMY "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetAssociatedAccounts ...
func GetAssociatedAccounts(ctx context.Context, accountServiceClient accountService.AccountService, associatedAccountsReq accountService.ListAllAssociatedAccountsRequest, logTag string) (dto.ListAssociatedAccountsResponseDTO, error) {
	associatedAccountsResp, err := accountServiceClient.ListAllAssociatedAccounts(ctx, &associatedAccountsReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error while fetching associated account details with account id %s: %s", associatedAccountsReq.AccountID, err.Error()))
		return dto.ListAssociatedAccountsResponseDTO{}, err
	}

	var associatedAccountsList []dto.AccountDetailDTO
	for _, associatedAccount := range associatedAccountsResp.AssociatedAccounts {
		associatedAccountsList = append(associatedAccountsList, dto.AccountDetailDTO{
			AccountID:          associatedAccount.AccountID,
			Status:             associatedAccount.Status,
			AccountType:        associatedAccount.AccountType,
			ProductVariantCode: associatedAccount.ProductVariantCode,
			SubStatus:          associatedAccount.SubStatus,
		})
	}

	return dto.ListAssociatedAccountsResponseDTO{
		CifNumber:          associatedAccountsResp.CifNumber,
		AssociatedAccounts: associatedAccountsList,
	}, nil
}

// IsChildAccount returns true if the account's ParentAccountID field is not empty, false otherwise.
func IsChildAccount(accountDetail *accountServiceDBMY.GetAccountResponse) bool {
	if accountDetail == nil || accountDetail.Account == nil {
		return false
	}
	return accountDetail.Account.ParentAccountID != ""
}
