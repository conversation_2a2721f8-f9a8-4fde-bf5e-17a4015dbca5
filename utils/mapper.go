package utils

import (
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
)

// TxnStatusMap ...
var TxnStatusMap = map[string]string{
	constants.PaymentStatusCompleted:  constants.PaymentStatusCompleted,
	constants.PaymentStatusFailed:     constants.PaymentStatusFailed,
	constants.PaymentStatusProcessing: constants.PaymentStatusProcessing,
	constants.CardStatusCanceled:      constants.CardStatusCanceled,
}

// TxnTypeMap ...
var TxnTypeMap = map[string]string{
	constants.TransferMoneyTxType:                        constants.TransferMoneyTxType,
	constants.FundInTxType:                               constants.FundIn,
	constants.FundInRevTxType:                            constants.FundInRevTxType,
	constants.SendMoneyTxType:                            constants.SendMoneyTxType,
	constants.SendMoneyRevTxType:                         constants.SendMoneyRevTxType,
	constants.ReceiveMoneyTxType:                         constants.ReceiveMoneyTxType,
	constants.ReceiveMoneyRevTxType:                      constants.ReceiveMoneyRevTxType,
	constants.InterestPayoutTransactionType:              constants.InterestPayoutTransactionType,
	constants.BonusInterestPayoutTransactionType:         constants.BonusInterestPayoutTransactionType,
	constants.BonusInterestPayoutReversalTransactionType: constants.BonusInterestPayoutReversalTransactionType,
	constants.OPSTransactionType:                         constants.OPSTransactionType,
	// cards txn
	constants.SpendCardPresentTransactionType:        constants.SpendCardPresentTransactionType,
	constants.SpendCardNotPresentTransactionType:     constants.SpendCardNotPresentTransactionType,
	constants.SpendRefundTransactionType:             constants.SpendRefundTransactionType,
	constants.SpendCardForceTransactionType:          constants.SpendCardForceTransactionType,
	constants.SpendCardAtmTransactionType:            constants.SpendCardAtmTransactionType,
	constants.ATMCashWithdrawalRefundTransactionType: constants.ATMCashWithdrawalRefundTransactionType,
	constants.DomesticAtmFeeTransactionType:          constants.DomesticAtmFeeTransactionType,
	constants.DomesticAtmFeeWaiverTransactionType:    constants.DomesticAtmFeeWaiverTransactionType,
	// cards ops txn
	constants.ManualDebitReconTransactionType:          constants.ManualDebitReconTransactionType,
	constants.ManualCreditReconTransactionType:         constants.ManualCreditReconTransactionType,
	constants.DisputeChargeBackTransactionType:         constants.DisputeChargeBackTransactionType,
	constants.DisputeBankRefundTransactionType:         constants.DisputeBankRefundTransactionType,
	constants.DisputeBankFraudRefundTransactionType:    constants.DisputeBankFraudRefundTransactionType,
	constants.ProvisionalCreditTransactionType:         constants.ProvisionalCreditTransactionType,
	constants.ProvisionalCreditReversalTransactionType: constants.ProvisionalCreditReversalTransactionType,
	constants.OperationalLossTransactionType:           constants.OperationalLossTransactionType,
	// grab txn
	constants.SpendMoneyTxType:    constants.SpendMoneyTxType,
	constants.SpendMoneyRevTxType: constants.SpendMoneyRevTxType,
	// earmark txn
	constants.ApplyEarmarkTransactionType:   constants.ApplyEarmarkTransactionType,
	constants.ReleaseEarmarkTransactionType: constants.ReleaseEarmarkTransactionType,
	// rewards txn
	constants.RewardsCashback:         constants.RewardsCashback,
	constants.RewardsCashbackReversal: constants.RewardsCashbackReversal,
	// qr txn
	constants.QrPaymentTxType:         constants.QrPaymentTxType,
	constants.QrPaymentReversalTxType: constants.QrPaymentReversalTxType,
	// lending txn
	constants.DrawdownTransactionType:     constants.DrawdownTransactionType,
	constants.RepaymentTransactionType:    constants.RepaymentTransactionType,
	constants.WriteOffTransactionType:     constants.WriteOffTransactionType,
	constants.LineOfCreditTransactionType: constants.LineOfCreditTransactionType,
	// insurance txn
	constants.InsurPremiumTxType:         constants.InsurPremiumTxType,
	constants.InsurPremiumReversalTxType: constants.InsurPremiumReversalTxType,
	// MOOMOO
	constants.CashOutTxType: constants.CashOutTxType,
	// Spend card ATM reversal
	constants.SpendCardATMReversalTransactionType: constants.SpendCardATMReversalTransactionType,
	// Spend card present reversal
	constants.SpendCardPresentReversalTransactionType: constants.SpendCardPresentReversalTransactionType,
	// spend card not present reversal
	constants.SpendCardNotPresentReversalTransactionType: constants.SpendCardNotPresentReversalTransactionType,
	// unclaimed monies
	constants.UnclaimedMoniesTransactionType: constants.UnclaimedMoniesTransactionType,
	// card maintenance fee
	constants.NewCardIssuanceFeeTransactionType:       constants.NewCardIssuanceFeeTransactionType,
	constants.NewCardIssuanceFeeWaiverTransactionType: constants.NewCardIssuanceFeeWaiverTransactionType,
}

// TxnSubtypeMap ...
var TxnSubtypeMap = map[string]string{
	constants.IntrabankTransactionSubtype:        constants.IntrabankTransactionSubtype,
	constants.PocketFundingTransactionSubType:    constants.PocketFundingTransactionSubType,
	constants.PocketWithdrawalTransactionSubType: constants.PocketWithdrawalTransactionSubType,
	constants.RppChannelType:                     constants.RppChannelType,
	constants.RENTAS:                             constants.RENTAS,
	constants.InterestPayoutTransactionSubType:   constants.InterestPayoutTransactionSubType,
	constants.OPSTransactionSubType:              constants.OPSTransactionSubType,
	// cards txn
	constants.MastercardTransactionSubtype:    constants.MastercardTransactionSubtype,
	constants.PaynetMydebitTransactionSubtype: constants.PaynetMydebitTransactionSubtype,
	constants.PaynetSanTransactionSubtype:     constants.PaynetSanTransactionSubtype,
	// grab txn
	constants.Grab: constants.Grab,
	// lending txn
	constants.BadDebtRecoveryIntrabankSubType:            constants.BadDebtRecoveryIntrabankSubType,
	constants.OpsLossRecoveryIntrabankTransactionSubType: constants.OpsLossRecoveryIntrabankTransactionSubType,
	constants.UtilizeLimitTransactionSubType:             constants.UtilizeLimitTransactionSubType,
	constants.LoanConversionSubType:                      constants.LoanConversionSubType,
	constants.AkpkFundReallocationSubtype:                constants.AkpkFundReallocationSubtype,
	// insurance txn
	constants.CollectCustSubType: constants.CollectCustSubType,
	// MOOMOO
	constants.MooMoo: constants.MooMoo,
}
