// Code generated by mockery v2.15.0. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockIInterestAggregateV2DAO is an autogenerated mock type for the IInterestAggregateDAO type
type MockIInterestAggregateV2DAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIInterestAggregateV2DAO) Find(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*InterestAggregateV2
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*InterestAggregateV2); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*InterestAggregateV2)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIInterestAggregateV2DAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*InterestAggregateV2
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*InterestAggregateV2); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*InterestAggregateV2)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIInterestAggregateV2DAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *InterestAggregateV2
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *InterestAggregateV2); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*InterestAggregateV2)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIInterestAggregateV2DAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *InterestAggregateV2
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *InterestAggregateV2); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*InterestAggregateV2)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIInterestAggregateV2DAO) Save(ctx context.Context, newData *InterestAggregateV2) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InterestAggregateV2) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIInterestAggregateV2DAO) SaveBatch(ctx context.Context, newData []*InterestAggregateV2) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*InterestAggregateV2) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIInterestAggregateV2DAO) Update(ctx context.Context, newData *InterestAggregateV2) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InterestAggregateV2) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIInterestAggregateV2DAO) UpdateEntity(ctx context.Context, preData *InterestAggregateV2, newData *InterestAggregateV2) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InterestAggregateV2, *InterestAggregateV2) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIInterestAggregateV2DAO) Upsert(ctx context.Context, newData *InterestAggregateV2) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InterestAggregateV2) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockIInterestAggregateV2DAO interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIInterestAggregateV2DAO creates a new instance of NewMockIInterestAggregateV2DAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIInterestAggregateV2DAO(t mockConstructorTestingTNewMockIInterestAggregateV2DAO) *MockIInterestAggregateV2DAO {
	mock := &MockIInterestAggregateV2DAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
