package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var interestAggregateDao = "interest_aggregate_dao"

// GetID implements the GetID function for Entity Interface
func (impl *InterestAggregate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *InterestAggregate) SetID(ID string) {
	// replace the logic to populate unique ID for InterestAggregate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *InterestAggregate) NewEntity() data.Entity {
	return &InterestAggregate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *InterestAggregate) GetTableName() string {
	return "interest_aggregate"
}

// IInterestAggregateDAO is the dao interface for InterestAggregate
//
//go:generate mockery --name IInterestAggregateDAO --inpackage --case=underscore
type IInterestAggregateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*InterestAggregate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InterestAggregate, error)

	// Save ...
	Save(ctx context.Context, newData *InterestAggregate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*InterestAggregate) error

	// Update ...
	Update(ctx context.Context, newData *InterestAggregate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *InterestAggregate, newData *InterestAggregate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *InterestAggregate) error
}

// InterestAggregateDAO ...
type InterestAggregateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewInterestAggregateDAO creates a data access object for InterestAggregate
func NewInterestAggregateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *InterestAggregateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &InterestAggregateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &InterestAggregate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *InterestAggregateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*InterestAggregate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InterestAggregate), err
}

// LoadByIDOnSlave ...
func (dao *InterestAggregateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InterestAggregate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InterestAggregate), err
}

// Save ...
func (dao *InterestAggregateDAO) Save(ctx context.Context, entity *InterestAggregate) error {
	methodName := "save"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *InterestAggregateDAO) SaveBatch(ctx context.Context, entities []*InterestAggregate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *InterestAggregateDAO) Update(ctx context.Context, data *InterestAggregate) error {
	methodName := "update"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *InterestAggregateDAO) UpdateEntity(ctx context.Context, preData *InterestAggregate, newData *InterestAggregate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *InterestAggregateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InterestAggregate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InterestAggregate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *InterestAggregateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InterestAggregate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InterestAggregate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *InterestAggregateDAO) Upsert(ctx context.Context, data *InterestAggregate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(interestAggregateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(interestAggregateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
