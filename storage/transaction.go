package storage

import (
	"context"
	"database/sql"
)

// Transaction ...
//
//go:generate mockery --name Transaction --inpackage --case=underscore
type Transaction interface {
	Exec(query string, args ...interface{}) (sql.Result, error)
	ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
	Prepare(query string) (*sql.Stmt, error)
	Query(query string, args ...interface{}) (*sql.Rows, error)
	QueryRow(query string, args ...interface{}) *sql.Row
}

// Result ...
//
//go:generate mockery --name Result --inpackage --case=underscore
type Result interface {
	LastInsertId() (int64, error)
	RowsAffected() (int64, error)
}

// TxFn ...
type TxFn func(Transaction) error

// WithTransaction ...
var WithTransaction = func(ctx context.Context, db *sql.DB, fn TxFn) (err error) {
	tx, err := db.BeginTx(ctx, nil)

	// We return the error from this defer func if there's any.
	// This may not be ideal as the origin cause of error might lost but at least we let the caller knows something went wrong
	defer func() {
		var err2 error
		if p := recover(); p != nil {
			err2 = tx.Rollback()
			if err2 != nil {
				err = err2
			}
			panic(p)
		} else if err != nil {
			err2 = tx.Rollback()
			if err2 != nil {
				err = err2
			}
		} else {
			err2 = tx.Commit()
			if err2 != nil {
				err = err2
			}
		}
	}()

	err = fn(tx)
	return err
}
