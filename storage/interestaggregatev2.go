package storage

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// InterestAggregateV2 ...
type InterestAggregateV2 struct {
	ID                  uint64                `sql-col:"id" sql-key:"id" sql-insert:"false"`
	AccountID           string                `sql-col:"account_id"`
	AccountAddress      string                `sql-col:"account_address"`
	TotalInterestEarned InterestEarned        `sql-col:"total_interest_earned" sql-where:"false"`
	RecentDay           RecentDataCollection  `sql-col:"recent_day" sql-where:"false"`
	MonthlyHistory      MonthlyDataCollection `sql-col:"monthly_history" sql-where:"false"`
	Currency            string                `sql-col:"currency"`
	CreatedAt           time.Time             `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedAt           time.Time             `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

// InterestEarned ...
type InterestEarned map[string]int64

// Value implements the sql.Valuer interface.
func (data InterestEarned) Value() (driver.Value, error) {
	if data == nil {
		return nil, nil
	}
	return json.Marshal(data)
}

// <PERSON>an implements the sql.Scanner interface.
func (data *InterestEarned) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	if b, ok := value.([]byte); ok {
		return json.Unmarshal(b, data)
	}

	return errors.New("type assertion to []byte failed")
}

// RecentDayData ...
type RecentDayData struct {
	Value int64 `json:"value"`
	Time  int64 `json:"time"`
}

// RecentDataCollection ...
type RecentDataCollection map[string]map[string]RecentDayData

// Value implements the sql.Valuer interface.
func (data RecentDataCollection) Value() (driver.Value, error) {
	if data == nil {
		return nil, nil
	}
	return json.Marshal(data)
}

// Scan implements the sql.Scanner interface.
func (data *RecentDataCollection) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	if b, ok := value.([]byte); ok {
		return json.Unmarshal(b, data)
	}

	return errors.New("type assertion to []byte failed")
}

// MonthlyDataCollection ...
type MonthlyDataCollection map[string]map[string]int64

// Value implements the sql.Valuer interface.
func (data MonthlyDataCollection) Value() (driver.Value, error) {
	if data == nil {
		return nil, nil
	}
	return json.Marshal(data)
}

// Scan implements the sql.Scanner interface.
func (data *MonthlyDataCollection) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	if b, ok := value.([]byte); ok {
		return json.Unmarshal(b, data)
	}

	return errors.New("type assertion to []byte failed")
}
