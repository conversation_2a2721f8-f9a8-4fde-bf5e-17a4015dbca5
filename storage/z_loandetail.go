package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_detailDao = "loan_detail_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanDetail) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanDetail) SetID(ID string) {
	// replace the logic to populate unique ID for LoanDetail
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanDetail) NewEntity() data.Entity {
	return &LoanDetail{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanDetail) GetTableName() string {
	return "loan_detail"
}

// ILoanDetailDAO is the dao interface for LoanDetail
//
//go:generate mockery --name ILoanDetailDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.myteksi.net/dakota/servus/v2/data
type ILoanDetailDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error)

	// Save ...
	Save(ctx context.Context, newData *LoanDetail) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanDetail) error

	// Update ...
	Update(ctx context.Context, newData *LoanDetail) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanDetail, newData *LoanDetail) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanDetail) error
}

// LoanDetailDAO ...
type LoanDetailDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanDetailDAO creates a data access object for LoanDetail
func NewLoanDetailDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanDetailDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanDetailDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanDetail{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanDetailDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanDetail), err
}

// LoadByIDOnSlave ...
func (dao *LoanDetailDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanDetail), err
}

// Save ...
func (dao *LoanDetailDAO) Save(ctx context.Context, entity *LoanDetail) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanDetailDAO) SaveBatch(ctx context.Context, entities []*LoanDetail) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanDetailDAO) Update(ctx context.Context, data *LoanDetail) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanDetailDAO) UpdateEntity(ctx context.Context, preData *LoanDetail, newData *LoanDetail) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanDetailDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDetail))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanDetailDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDetail))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanDetailDAO) Upsert(ctx context.Context, data *LoanDetail) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_detailDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
