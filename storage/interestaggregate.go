package storage

import "time"

// InterestAggregate ...
type InterestAggregate struct {
	ID                  uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	AccountID           string    `sql-col:"account_id"`
	AccountAddress      string    `sql-col:"account_address"`
	TotalInterestEarned int64     `sql-col:"total_interest_earned"`
	Currency            string    `sql-col:"currency"`
	CreatedAt           time.Time `sql-col:"created_at"`
	UpdatedAt           time.Time `sql-col:"updated_at"`
}
