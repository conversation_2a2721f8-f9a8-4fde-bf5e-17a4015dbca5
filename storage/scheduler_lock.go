package storage

import "time"

// SchedulerLock ...
type SchedulerLock struct {
	ID          uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	SchedulerID string    `sql-col:"scheduler_id"`
	LastRunAt   time.Time `sql-col:"last_run_at"`
	CreatedAt   time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedAt   time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}
