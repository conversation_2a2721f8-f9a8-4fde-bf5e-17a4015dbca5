package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var schedulerLockDao = "scheduler_lock_dao"

// GetID implements the GetID function for Entity Interface
func (impl *SchedulerLock) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *SchedulerLock) SetID(ID string) {
	// replace the logic to populate unique ID for SchedulerLock
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *SchedulerLock) NewEntity() data.Entity {
	return &SchedulerLock{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *SchedulerLock) GetTableName() string {
	return "scheduler_lock"
}

// ISchedulerLockDAO is the dao interface for SchedulerLock
//
//go:generate mockery --name ISchedulerLockDAO --inpackage --case=underscore
type ISchedulerLockDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*SchedulerLock, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*SchedulerLock, error)

	// Save ...
	Save(ctx context.Context, newData *SchedulerLock) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*SchedulerLock) error

	// Update ...
	Update(ctx context.Context, newData *SchedulerLock) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *SchedulerLock, newData *SchedulerLock) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*SchedulerLock, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*SchedulerLock, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *SchedulerLock) error
}

// SchedulerLockDAO ...
type SchedulerLockDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewSchedulerLockDAO creates a data access object for SchedulerLock
func NewSchedulerLockDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *SchedulerLockDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &SchedulerLockDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &SchedulerLock{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *SchedulerLockDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*SchedulerLock, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*SchedulerLock), err
}

// LoadByIDOnSlave ...
func (dao *SchedulerLockDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*SchedulerLock, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*SchedulerLock), err
}

// Save ...
func (dao *SchedulerLockDAO) Save(ctx context.Context, entity *SchedulerLock) error {
	methodName := "save"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *SchedulerLockDAO) SaveBatch(ctx context.Context, entities []*SchedulerLock) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *SchedulerLockDAO) Update(ctx context.Context, data *SchedulerLock) error {
	methodName := "update"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *SchedulerLockDAO) UpdateEntity(ctx context.Context, preData *SchedulerLock, newData *SchedulerLock) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *SchedulerLockDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*SchedulerLock, error) {
	methodName := "find"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*SchedulerLock, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*SchedulerLock))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *SchedulerLockDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*SchedulerLock, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*SchedulerLock, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*SchedulerLock))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *SchedulerLockDAO) Upsert(ctx context.Context, data *SchedulerLock) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(schedulerLockDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(schedulerLockDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
