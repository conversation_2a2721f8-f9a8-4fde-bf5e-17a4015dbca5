package storage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"github.com/Rhymond/go-money"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.myteksi.net/dakota/common/tenants"
)

const (
	opsPortalServiceID = "payment-ops-trf"
	gxRewards          = "GX reward"
)

// TransactionsData ...
type TransactionsData struct {
	ID uint64 `sql-col:"id" sql-key:"id" sql-insert:"false"`
	// Client fields
	ClientTransactionID string `sql-col:"client_transaction_id"`
	ClientBatchID       string `sql-col:"client_batch_id"`

	// ThoughtMachine Fields
	TmPostingInstructionBatchID string `sql-col:"tm_posting_instruction_batch_id"`
	TmTransactionID             string `sql-col:"tm_transaction_id"`
	TmTransactionType           string `sql-col:"tm_transaction_type"`

	// Batch Level Fields
	BatchRemarks            string          `sql-col:"batch_remarks"`
	BatchStatus             string          `sql-col:"batch_status"`
	BatchErrorType          string          `sql-col:"batch_error_type"`
	BatchDetails            json.RawMessage `sql-col:"batch_details"  data-type:"json"`
	BatchErrorMessage       string          `sql-col:"batch_error_message"`
	BatchInsertionTimestamp time.Time       `sql-col:"batch_insertion_timestamp"`
	BatchValueTimestamp     time.Time       `sql-col:"batch_value_timestamp"`

	// Transfer Level
	TransactionDomain       string          `sql-col:"transaction_domain"`
	TransactionType         string          `sql-col:"transaction_type"`
	TransactionSubtype      string          `sql-col:"transaction_subtype"`
	TransactionDetails      json.RawMessage `sql-col:"transaction_details" data-type:"json"`
	TransactionViolations   json.RawMessage `sql-col:"transaction_violations" data-type:"json"`
	AccountID               string          `sql-col:"account_id"`
	AccountAddress          string          `sql-col:"account_address"`
	AccountAsset            string          `sql-col:"account_asset"`
	AccountPhase            string          `sql-col:"account_phase"`
	DebitOrCredit           string          `sql-col:"debit_or_credit"`
	TransactionAmount       string          `sql-col:"transaction_amount"`
	TransactionCurrency     string          `sql-col:"transaction_currency"`
	BalanceAfterTransaction string          `sql-col:"balance_after_transaction"`

	Metadata  json.RawMessage `sql-col:"metadata" data-type:"json"`
	CreatedAt time.Time       `sql-col:"created_at"`
	UpdatedAt time.Time       `sql-col:"updated_at"`
}

// SortByBatchValueTS ...
type SortByBatchValueTS []*TransactionsData

func (a SortByBatchValueTS) Len() int {
	return len(a)
}

func (a SortByBatchValueTS) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

func (a SortByBatchValueTS) Less(i, j int) bool {
	return a[i].BatchValueTimestamp.Before(a[j].BatchValueTimestamp)
}

// FormattedAmount ...
func (d *TransactionsData) FormattedAmount(ctx context.Context) int64 {
	amountInCents := money.New(utils.GetAmountInCents(ctx, d.TransactionAmount), d.TransactionCurrency).Amount()
	// if d.DebitOrCredit == constants.DEBIT && utils.SearchStringArray(constants.ReverseTransactionType, d.TransactionType) {
	//	amountInCents = 1 * amountInCents // implies amount is credited
	// } else if d.DebitOrCredit == constants.CREDIT && utils.SearchStringArray(constants.ReverseTransactionType, d.TransactionType) {
	//	amountInCents = -1 * amountInCents // implies amount is debited
	// }
	// above logic not applicable to DBMY
	if d.DebitOrCredit == constants.DEBIT && d.TransactionType == constants.SpendMoneyTxType {
		amountInCents = -1 * amountInCents // implies amount is debited
	} else if d.TransactionType == constants.ReleaseEarmarkTransactionType {
		amountInCents = -1 * amountInCents // implies amount is debited
	} else if d.DebitOrCredit == constants.DEBIT && d.TransactionType == constants.ReceiveMoneyTxType {
		amountInCents = 1 * amountInCents // implies amount is debited, directed credit
	} else if d.DebitOrCredit == constants.DEBIT {
		amountInCents = -1 * amountInCents // implies amount is debited
	} else {
		amountInCents = 1 * amountInCents // implies amount is credited
	}
	return amountInCents
}

// FormattedSettlementAmount formats the settlement amount with transaction direction (debit or credit)
func (d *TransactionsData) FormattedSettlementAmount(amountInCents int64) int64 {
	var finalAmount int64
	if d.DebitOrCredit == constants.DEBIT {
		finalAmount = -1 * amountInCents
	} else {
		finalAmount = 1 * amountInCents
	}
	return finalAmount
}

// GetTxnScenarioKey ...
// nolint: gocognit, funlen
// Scenario key should cater for internal account posting leg as it might be returned in ops api
func (d *TransactionsData) GetTxnScenarioKey() string {
	if d.IsCardDomain() {
		return d.getTxnScenarioKeyByCardDomain()
	}
	if d.IsLendingDomain() {
		return d.getTxnScenarioKeyByLendingDomain()
	}
	if d.IsBizLendingDomain() {
		return d.getTxnScenarioKeyByBizLendingDomain()
	}
	// if d.IsBizDepositDomain() {
	// 	return d.getTxnScenarioKeyByBizDepositDomain()
	// }
	var key string
	switch {
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.IntrabankTransactionSubtype && d.DebitOrCredit == constants.DEBIT:
		key = constants.TransferIntrabankDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.IntrabankTransactionSubtype && d.DebitOrCredit == constants.CREDIT:
		key = constants.TransferIntrabankCreditScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.PocketFundingTransactionSubType && d.DebitOrCredit == constants.DEBIT:
		key = constants.TransferMainToPocketDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.PocketFundingTransactionSubType && d.DebitOrCredit == constants.CREDIT:
		key = constants.TransferMainToPocketCreditScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && d.DebitOrCredit == constants.DEBIT:
		key = constants.TransferPocketToMainDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && d.DebitOrCredit == constants.CREDIT:
		key = constants.TransferPocketToMainCreditScenarioKey
	case d.TransactionType == constants.TransferMoneyRevTxType && d.TransactionSubtype == constants.IntrabankTransactionSubtype && d.DebitOrCredit == constants.DEBIT:
		key = constants.TransferMoneyReversalIntrabankDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyRevTxType && d.TransactionSubtype == constants.IntrabankTransactionSubtype && d.DebitOrCredit == constants.CREDIT:
		key = constants.TransferMoneyReversalIntrabankCreditScenarioKey
	case d.TransactionType == constants.FundInTxType:
		key = constants.FundInScenarioKey
	case d.TransactionType == constants.FundInRevTxType:
		key = constants.FundInReversalScenarioKey
	case d.TransactionType == constants.SendMoneyTxType && d.DebitOrCredit == constants.DEBIT:
		key = constants.SendMoneyDebitScenarioKey
	case d.TransactionType == constants.SendMoneyRevTxType && d.DebitOrCredit == constants.CREDIT:
		key = constants.SendMoneyReversalCreditScenarioKey
	case d.TransactionType == constants.ReceiveMoneyTxType && d.DebitOrCredit == constants.CREDIT:
		key = constants.ReceiveMoneyCreditScenarioKey
	case d.TransactionType == constants.ReceiveMoneyRevTxType && d.DebitOrCredit == constants.DEBIT:
		key = constants.ReceiveMoneyReversalDebitScenarioKey
	case d.TransactionType == constants.SpendMoneyTxType:
		if d.TransactionSubtype == constants.MooMoo {
			key = constants.MooMooSpendMoneyScenarioKey
		} else {
			key = constants.SpendMoneyScenarioKey
		}
	case d.TransactionType == constants.SpendMoneyRevTxType:
		if d.TransactionSubtype == constants.MooMoo {
			key = constants.MooMooSpendMoneyReversalScenarioKey
		} else {
			key = constants.SpendMoneyReversalScenarioKey
		}
	case d.TransactionType == constants.CashOutTxType:
		if d.TransactionSubtype == constants.MooMoo {
			key = constants.MooMooCashOutScenarioKey
		}
	case d.TransactionType == constants.InterestPayoutTransactionType:
		key = constants.InterestPayoutScenarioKey
	case d.TransactionType == constants.BonusInterestPayoutTransactionType:
		key = constants.BonusInterestPayoutScenarioKey
	case d.TransactionType == constants.BonusInterestPayoutReversalTransactionType:
		key = constants.BonusInterestPayoutReversalScenarioKey
	case d.TransactionType == constants.Adjustment && d.DebitOrCredit == constants.DEBIT:
		key = constants.AdjustmentBankInitiatedDebitScenarioKey
	case d.TransactionType == constants.Adjustment && d.DebitOrCredit == constants.CREDIT:
		key = constants.AdjustmentBankInitiatedCreditScenarioKey
	case d.TransactionType == constants.RewardsCashback:
		key = constants.RewardsCashbackScenarioKey
	case d.TransactionType == constants.RewardsCashbackReversal:
		key = constants.RewardsCashbackReversalScenarioKey
	case d.TransactionType == constants.ApplyEarmarkTransactionType:
		key = constants.ApplyEarmarkScenarioKey
	case d.TransactionType == constants.ReleaseEarmarkTransactionType:
		key = constants.ReleaseEarmarkScenarioKey
	case d.TransactionType == constants.QrPaymentTxType:
		key = constants.PaymentTransactionTypeDebitScenarioKey
	case d.TransactionType == constants.QrPaymentReversalTxType:
		key = constants.PaymentReversalTransactionTypeCreditScenarioKey
	case d.TransactionType == constants.InsurPremiumTxType:
		key = constants.InsurPremiumTransactionTypeScenarioKey
	case d.TransactionType == constants.InsurPremiumReversalTxType:
		key = constants.InsurPremiumReversalTransactionTypeScenarioKey
	case d.TransactionType == constants.UnclaimedMoniesTransactionType:
		key = constants.UnclaimedMoniesSuspenseScenarioKey
	default:
		key = constants.Default
	}
	return key
}

// nolint: funlen
func (d *TransactionsData) getTxnScenarioKeyByCardDomain() string {
	key := constants.Default
	if d.isDebit() {
		switch d.TransactionType {
		case constants.SpendCardPresentTransactionType:
			key = constants.SpendCardPresentTransactionTypeDebitScenarioKey
		case constants.SpendCardNotPresentTransactionType:
			key = constants.SpendCardNotPresentTransactionTypeDebitScenarioKey
		case constants.SpendCardForceTransactionType:
			key = constants.SpendCardForceTransactionTypeDebitScenarioKey
		case constants.SpendCardAtmTransactionType:
			key = constants.SpendCardAtmTransactionTypeDebitScenarioKey
		case constants.DomesticAtmFeeTransactionType:
			key = constants.DomesticAtmFeeTransactionTypeDebitScenarioKey
		// not used in prd yet, can revisit later
		case constants.CardAnnualFeeTransactionType:
			key = constants.CardAnnualFeeTransactionTypeDebitScenarioKey
		// not used in prd yet, can revisit later
		case constants.CardReplacementFeeTransactionType:
			key = constants.CardReplacementFeeTransactionTypeDebitScenarioKey
		case constants.NewCardIssuanceFeeWaiverTransactionType:
			key = constants.NewCardIssuanceFeeWaiverTransactionTypeCreditScenarioKey
		}
	}
	if d.isCredit() {
		switch d.TransactionType {
		case constants.SpendCardPresentTransactionType:
			key = constants.SpendCardPresentTransactionTypeCreditScenarioKey
		case constants.SpendCardNotPresentTransactionType:
			key = constants.SpendCardNotPresentTransactionTypeCreditScenarioKey
		case constants.SpendCardForceTransactionType:
			key = constants.SpendCardForceTransactionTypeCreditScenarioKey
		case constants.ATMCashWithdrawalRefundTransactionType:
			key = constants.ATMCashWithdrawalRefundTransactionTypeCreditScenarioKey
		case constants.DomesticAtmFeeWaiverTransactionType:
			key = constants.DomesticAtmFeeWaiverTransactionTypeCreditScenarioKey
		case constants.MoneySendTransactionType:
			key = constants.MoneySendTransactionTypeCreditScenarioKey
		case constants.SpendRefundTransactionType:
			key = constants.SpendRefundTransactionTypeCreditScenarioKey
		case constants.SpendCardATMReversalTransactionType:
			key = constants.SpendCardATMReversalTypeCreditScenarioKey
		case constants.SpendCardNotPresentReversalTransactionType:
			key = constants.SpendCardNotPresentReversalTypeCreditScenarioKey
		case constants.SpendCardPresentReversalTransactionType:
			key = constants.SpendCardPresentReversalTypeCreditScenarioKey
		case constants.NewCardIssuanceFeeWaiverTransactionType:
			key = constants.NewCardIssuanceFeeWaiverTransactionTypeCreditScenarioKey
		case constants.CardAnnualFeeWaiverTransactionType:
			key = constants.CardAnnualFeeWaiverTransactionTypeCreditScenarioKey
		case constants.CardReplacementFeeWaiverTransactionType:
			key = constants.CardReplacementFeeWaiverTransactionTypeCreditScenarioKey
		}
	}
	switch d.TransactionType {
	case constants.ManualDebitReconTransactionType:
		key = constants.ManualDebitReconTransactionTypeScenarioKey
	case constants.ManualCreditReconTransactionType:
		key = constants.ManualCreditReconTransactionTypeScenarioKey
	case constants.DisputeChargeBackTransactionType:
		key = constants.DisputeChargeBackTransactionTypeScenarioKey
	case constants.DisputeBankRefundTransactionType:
		key = constants.DisputeBankRefundTransactionTypeScenarioKey
	case constants.DisputeBankFraudRefundTransactionType:
		key = constants.DisputeBankFraudRefundTransactionTypeScenarioKey
	case constants.ProvisionalCreditTransactionType:
		key = constants.ProvisionalCreditTransactionTypeScenarioKey
	case constants.ProvisionalCreditReversalTransactionType:
		key = constants.ProvisionalCreditReversalTransactionTypeScenarioKey
	case constants.RewardsCashback:
		key = constants.RewardsCashbackTransactionTypeScenarioKey
	case constants.RewardsCashbackReversal:
		key = constants.RewardsCashbackReversalTransactionTypeScenarioKey
	case constants.OperationalLossTransactionType:
		key = constants.OperationalLossTransactionTypeScenarioKey
	case constants.NewCardIssuanceFeeTransactionType:
		key = constants.NewCardIssuanceFeeTransactionTypeDebitScenarioKey
	}
	return key
}

func (d *TransactionsData) getTxnScenarioKeyByLendingDomain() string {
	var key string
	switch d.TransactionType {
	case constants.DrawdownTransactionType:
		key = constants.LendingDrawdownScenarioKey
	case constants.RepaymentTransactionType:
		key = constants.LendingRepaymentScenarioKey
	case constants.WriteOffTransactionType:
		key = constants.LendingWriteOffScenarioKey
	default:
		key = constants.Default
	}
	return key
}

func (d *TransactionsData) getTxnScenarioKeyByBizLendingDomain() string {
	var key string
	switch d.TransactionType {
	case constants.DrawdownTransactionType:
		key = constants.BizLendingDrawdownScenarioKey
	case constants.RepaymentTransactionType:
		key = constants.BizLendingRepaymentScenarioKey
	case constants.WriteOffTransactionType:
		key = constants.BizLendingWriteOffScenarioKey
	default:
		key = constants.Default
	}
	return key
}

// We will revisit this when there is significant difference between retail and biz
// nolint:unused
func (d *TransactionsData) getTxnScenarioKeyByBizDepositDomain() string {
	var key string
	switch {
	case d.TransactionType == constants.FundInTxType:
		key = constants.BizFundInScenarioKey
	case d.TransactionType == constants.FundInRevTxType:
		key = constants.BizFundInReversalScenarioKey
	case d.TransactionType == constants.ReceiveMoneyTxType:
		key = constants.BizReceiveMoneyScenarioKey
	case d.TransactionType == constants.ReceiveMoneyRevTxType:
		key = constants.BizReceiveMoneyReversalScenarioKey
	case d.TransactionType == constants.SendMoneyTxType:
		key = constants.BizSendMoneyScenarioKey
	case d.TransactionType == constants.SendMoneyRevTxType:
		key = constants.BizSendMoneyReversalScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.DebitOrCredit == constants.DEBIT:
		key = constants.BizTransferMoneyDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyTxType && d.DebitOrCredit == constants.CREDIT:
		key = constants.BizTransferMoneyCreditScenarioKey
	case d.TransactionType == constants.TransferMoneyRevTxType && d.DebitOrCredit == constants.DEBIT:
		key = constants.BizTransferMoneyReversalDebitScenarioKey
	case d.TransactionType == constants.TransferMoneyRevTxType && d.DebitOrCredit == constants.CREDIT:
		key = constants.BizTransferMoneyReversalCreditScenarioKey
	case d.TransactionType == constants.InterestPayoutTransactionType:
		key = constants.BizInterestPayoutScenarioKey
	case d.TransactionType == constants.ApplyEarmarkTransactionType:
		key = constants.BizApplyEarmarkScenarioKey
	case d.TransactionType == constants.ReleaseEarmarkTransactionType:
		key = constants.BizReleaseEarmarkScenarioKey
	default:
		key = constants.Default
	}
	return key
}

// GetTransactionCategoryAndIconURL ...
func (d *TransactionsData) GetTransactionCategoryAndIconURL() (*api.Category, string) {
	switch {
	case d.TransactionType == constants.PocketTransactionTypes[0]:
		return d.GetCustomCategoryAndIconURLForTransferMoney()
	case d.TransactionType == constants.SendMoneyFeeTransactionType:
		return &api.Category{
			Name: "Transfer Fee",
		}, constants.IconURLMap[constants.TransferFee]
	case d.TransactionType == "SEND_MONEY":
		return &api.Category{
			Name: "Transfer Out",
		}, constants.IconURLMap[constants.TransferOut]
	case d.TransactionType == "RECEIVE_MONEY":
		return &api.Category{
			Name: "Transfer In",
		}, constants.IconURLMap[constants.TransferIn]
	case d.TransactionType == constants.InterestPayoutTransactionType,
		d.TransactionType == constants.BonusInterestPayoutTransactionType:
		return &api.Category{}, constants.IconURLMap[constants.InterestPayout]
	case d.TransactionType == "TAX_PAYOUT":
		return &api.Category{}, constants.IconURLMap[constants.TaxOnInterest]
	case d.TransactionType == "ADJUSTMENT":
		return &api.Category{Name: "Adjustment Transaction"}, constants.IconURLMap[constants.Adjustment]
	case lo.Contains([]string{constants.RewardsCashback, constants.RewardsCashbackReversal}, d.TransactionType):
		return &api.Category{}, constants.IconURLMap[constants.Rewards]
	case d.IsCardMaintenanceFeeTxn():
		return &api.Category{}, constants.IconURLMap[constants.DebitCardDomain]
	default:
		return &api.Category{}, constants.IconURLMap["DefaultTransaction"]
	}
}

// GetCustomCategoryAndIconURLForTransferMoney ...
func (d *TransactionsData) GetCustomCategoryAndIconURLForTransferMoney() (*api.Category, string) {
	switch d.TransactionSubtype {
	case constants.PocketFundingTransactionSubType:
		if d.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Withdrawal"}, constants.IconURLMap[constants.TransferOut]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	case constants.PocketWithdrawalTransactionSubType:
		if d.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Withdrawal"}, constants.IconURLMap[constants.PocketWithdrawalTransactionSubType]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	case constants.IntraBank:
		if d.DebitOrCredit == constants.DEBIT {
			return &api.Category{Name: "Transfer Out"}, constants.IconURLMap[constants.TransferOut]
		}
		return &api.Category{Name: "Transfer In"}, constants.IconURLMap[constants.TransferIn]
	default:
		return &api.Category{}, constants.IconURLMap["DefaultTransaction"]
	}
}

// CounterPartyName ...
func (d *TransactionsData) CounterPartyName() string {
	var counterPartyDisplayName string
	tenant := config.GetTenant()
	if utils.SearchStringArray(constants.TaxPayoutTransactionTypes, d.TransactionType) {
		counterPartyDisplayName = localise.Translate(constants.TaxOnInterestDesc)
	}
	if utils.SearchStringArray(constants.SendMoneyFeeTransactionTypes, d.TransactionType) {
		counterPartyDisplayName = localise.Translate(constants.TransactionFee)
	}
	switch tenant {
	case tenants.TenantMY:
		if utils.SearchStringArray(constants.EarmarkTransactionTypes, d.TransactionType) {
			counterPartyDisplayName = localise.Translate(constants.Earmark)
		}
	}
	return counterPartyDisplayName
}

// GetPocketTxnCounterPartyAccountID return the counterparty account ID for pocket txn
func (d *TransactionsData) GetPocketTxnCounterPartyAccountID(ctx context.Context) string {
	var batchDetails map[string]string

	err := json.Unmarshal(d.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
		return ""
	}

	// Boost Pocket
	if batchDetails["sub_account_type"] == constants.BoostPocket {
		if d.AccountID == batchDetails["pocket_id"] {
			// this is not filled in deposits-core, imo it's a P1 / P2 requirements
			// because the counterparty accountID in this scenario is always the parentAccountID of the pocket
			return batchDetails["main_account_id"]
		}
		return batchDetails["pocket_id"]
	}

	// Savings Pocket
	if d.AccountID == batchDetails["source_account_id"] {
		return batchDetails["destination_account_id"]
	}
	return batchDetails["source_account_id"]
}

// GetPocketServiceType return the service type for a pocket
func (d *TransactionsData) GetPocketServiceType(ctx context.Context) string {
	var batchDetails map[string]string

	err := json.Unmarshal(d.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
		return ""
	}

	if batchDetails["sub_account_type"] == constants.BoostPocket {
		return constants.BoostPocket
	}

	return batchDetails["service_type"]
}

// GetBatchDetails assumes batchDetails values are all string, returns the batch details as a map[string]string
func (d *TransactionsData) GetBatchDetails(ctx context.Context, logTag string) (map[string]string, error) {
	if d == nil {
		slog.FromContext(ctx).Warn(logTag, "d is nil")
		return nil, errors.New("d is nil")
	}

	var batchDetails map[string]string

	err := json.Unmarshal(d.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Error parsing batchDetails", slog.Error(err))
		return nil, err
	}

	return batchDetails, nil
}

// IsInterestPayoutTxn checks if the txn is relevant to interest payout
func (d *TransactionsData) IsInterestPayoutTxn() bool {
	return utils.SearchStringArray(constants.InterestPayoutTransactionTypes, d.TransactionType)
}

// IsTaxPayoutTxn checks if the txn is relevant to tax payout
func (d *TransactionsData) IsTaxPayoutTxn() bool {
	return utils.SearchStringArray(constants.TaxPayoutTransactionTypes, d.TransactionType)
}

// IsOpsTxn checks if the txn is an adjustment
func (d *TransactionsData) IsOpsTxn() bool {
	return d.TransactionType == constants.OPSTransactionType
}

// IsRewardsTxn checks if the txn is a reward
func (d *TransactionsData) IsRewardsTxn() bool {
	return utils.SearchStringArray(constants.RewardsTransactions, d.TransactionType)
}

// IsCardMaintenanceFeeTxn checks if the txn is a card maintenance fee txn
func (d *TransactionsData) IsCardMaintenanceFeeTxn() bool {
	return d.TransactionDomain == constants.DebitCardDomain && lo.Contains(constants.CardMaintenanceFeeTypes, d.TransactionType)
}

// IsAtmFeeTxn checks if the txn is a card fee txn
func (d *TransactionsData) IsAtmFeeTxn() bool {
	return utils.SearchStringArray(constants.AtmFeeTransactionTypes, d.TransactionType)
}

// IsCardOpsTxn checks if the txn is a card ops txn
func (d *TransactionsData) IsCardOpsTxn() bool {
	return utils.SearchStringArray(constants.CardOpsTransactionTypes, d.TransactionType)
}

// IsCardTxn checks if the txn is a card txn and involve card network (not card ops)
func (d *TransactionsData) IsCardTxn() bool {
	return d.TransactionDomain == constants.DebitCardDomain && !d.IsCardOpsTxn() && !d.IsCardMaintenanceFeeTxn()
}

// IsInsuranceTxn checks if the txn is a insurance txn
func (d *TransactionsData) IsInsuranceTxn() bool {
	return d.TransactionDomain == constants.InsuranceDomain
}

// IsCardDomain checks if the txn domain is DEBIT_CARD
func (d *TransactionsData) IsCardDomain() bool {
	return d.TransactionDomain == constants.DebitCardDomain
}

// IsLendingDomain checks if the txn domain is LENDING
func (d *TransactionsData) IsLendingDomain() bool {
	return d.TransactionDomain == constants.LendingDomain
}

// IsBizLendingDomain checks if the txn domain is BIZ_LENDING
func (d *TransactionsData) IsBizLendingDomain() bool {
	return d.TransactionDomain == constants.BizLendingDomain
}

// IsBizDepositDomain checks if the txn domain is BIZ_DEPOSITS
func (d *TransactionsData) IsBizDepositDomain() bool {
	return d.TransactionDomain == constants.BizDepositsDomain
}

// GetTransactionDetail ...
func (d *TransactionsData) GetTransactionDetail(ctx context.Context) *dto.TransactionDetail {
	var transactionDetail dto.TransactionDetail
	err := json.Unmarshal(d.TransactionDetails, &transactionDetail)
	if err != nil {
		slog.FromContext(ctx).Warn("transactionsData.GetTransactionDetail", fmt.Sprintf("Error parsing TransactionDetail, err: %s", err.Error()))
	}
	return &transactionDetail
}

// IsOpsRewardCashback checks the current transactions is GX rewards cashback created by Ops
func (d *TransactionsData) IsOpsRewardCashback() bool {
	if d.TransactionType != constants.RewardsCashback {
		return false
	}

	batchDetails := map[string]string{}
	if err := json.Unmarshal(d.BatchDetails, &batchDetails); err == nil {
		// The rewards cashback that created by Ops
		if serviceID, ok := batchDetails["service_id"]; ok && serviceID == opsPortalServiceID {
			return true
		}
	}
	return false
}

// GetRewardsCampaignName ...
func (d *TransactionsData) GetRewardsCampaignName(ctx context.Context) string {
	// Hardcode the Ops rewards display name
	if d.IsOpsRewardCashback() {
		return gxRewards
	}
	transactionData := d.GetTransactionDetail(ctx)
	if transactionData != nil {
		return transactionData.CampaignName
	}
	return ""
}

// GetRewardsCampaignDescription ...
func (d *TransactionsData) GetRewardsCampaignDescription(ctx context.Context) string {
	if d.IsOpsRewardCashback() {
		return d.BatchRemarks
	}
	transactionData := d.GetTransactionDetail(ctx)
	if transactionData != nil {
		return transactionData.CampaignDescription
	}
	return ""
}

// GetInterestCampaignName ...
func (d *TransactionsData) GetInterestCampaignName(ctx context.Context) string {
	transactionData := d.GetTransactionDetail(ctx)
	if transactionData != nil {
		return transactionData.CampaignName
	}
	return ""
}

// GetInterestCampaignDescription ...
func (d *TransactionsData) GetInterestCampaignDescription(ctx context.Context) string {
	transactionData := d.GetTransactionDetail(ctx)
	if transactionData != nil {
		return transactionData.CampaignDescription
	}
	return ""
}

// GetLoanTransactionID ...
func (d *TransactionsData) GetLoanTransactionID(ctx context.Context) string {
	var transactionDetails map[string]string

	err := json.Unmarshal(d.TransactionDetails, &transactionDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing transactionDetails, err: %s", err.Error()))
		return ""
	}

	return transactionDetails["loanTransactionID"]
}

func (d *TransactionsData) isDebit() bool {
	return d.DebitOrCredit == constants.DEBIT
}

func (d *TransactionsData) isCredit() bool {
	return d.DebitOrCredit == constants.CREDIT
}
