package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var card_transaction_detailDao = "card_transaction_detail_dao"

// GetID implements the GetID function for Entity Interface
func (impl *CardTransactionDetail) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *CardTransactionDetail) SetID(ID string) {
	// replace the logic to populate unique ID for CardTransactionDetail
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *CardTransactionDetail) NewEntity() data.Entity {
	return &CardTransactionDetail{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *CardTransactionDetail) GetTableName() string {
	return "card_transaction_detail"
}

// ICardTransactionDetailDAO is the dao interface for CardTransactionDetail
//
//go:generate mockery --name ICardTransactionDetailDAO --inpackage --case=underscore
type ICardTransactionDetailDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error)

	// Save ...
	Save(ctx context.Context, newData *CardTransactionDetail) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*CardTransactionDetail) error

	// Update ...
	Update(ctx context.Context, newData *CardTransactionDetail) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *CardTransactionDetail, newData *CardTransactionDetail) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *CardTransactionDetail) error
}

// CardTransactionDetailDAO ...
type CardTransactionDetailDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewCardTransactionDetailDAO creates a data access object for CardTransactionDetail
func NewCardTransactionDetailDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *CardTransactionDetailDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &CardTransactionDetailDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &CardTransactionDetail{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *CardTransactionDetailDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*CardTransactionDetail), err
}

// LoadByIDOnSlave ...
func (dao *CardTransactionDetailDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*CardTransactionDetail), err
}

// Save ...
func (dao *CardTransactionDetailDAO) Save(ctx context.Context, entity *CardTransactionDetail) error {
	methodName := "save"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *CardTransactionDetailDAO) SaveBatch(ctx context.Context, entities []*CardTransactionDetail) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *CardTransactionDetailDAO) Update(ctx context.Context, data *CardTransactionDetail) error {
	methodName := "update"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *CardTransactionDetailDAO) UpdateEntity(ctx context.Context, preData *CardTransactionDetail, newData *CardTransactionDetail) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *CardTransactionDetailDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error) {
	methodName := "find"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*CardTransactionDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*CardTransactionDetail))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *CardTransactionDetailDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*CardTransactionDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*CardTransactionDetail))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *CardTransactionDetailDAO) Upsert(ctx context.Context, data *CardTransactionDetail) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(card_transaction_detailDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(card_transaction_detailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
