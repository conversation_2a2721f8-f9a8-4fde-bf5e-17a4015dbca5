package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var paymentDetailDao = "payment_detail_dao"

// GetID implements the GetID function for Entity Interface
func (impl *PaymentDetail) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *PaymentDetail) SetID(ID string) {
	// replace the logic to populate unique ID for PaymentDetail
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *PaymentDetail) NewEntity() data.Entity {
	return &PaymentDetail{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *PaymentDetail) GetTableName() string {
	return "payment_detail"
}

// IPaymentDetailDAO is the dao interface for PaymentDetail
//
//go:generate mockery --name IPaymentDetailDAO --inpackage --case=underscore
type IPaymentDetailDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*PaymentDetail, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PaymentDetail, error)

	// Save ...
	Save(ctx context.Context, newData *PaymentDetail) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*PaymentDetail) error

	// Update ...
	Update(ctx context.Context, newData *PaymentDetail) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *PaymentDetail, newData *PaymentDetail) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*PaymentDetail, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PaymentDetail, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *PaymentDetail) error
}

// PaymentDetailDAO ...
type PaymentDetailDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewPaymentDetailDAO creates a data access object for PaymentDetail
func NewPaymentDetailDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *PaymentDetailDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &PaymentDetailDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &PaymentDetail{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *PaymentDetailDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*PaymentDetail, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PaymentDetail), err
}

// LoadByIDOnSlave ...
func (dao *PaymentDetailDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PaymentDetail, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PaymentDetail), err
}

// Save ...
func (dao *PaymentDetailDAO) Save(ctx context.Context, entity *PaymentDetail) error {
	methodName := "save"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *PaymentDetailDAO) SaveBatch(ctx context.Context, entities []*PaymentDetail) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *PaymentDetailDAO) Update(ctx context.Context, data *PaymentDetail) error {
	methodName := "update"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *PaymentDetailDAO) UpdateEntity(ctx context.Context, preData *PaymentDetail, newData *PaymentDetail) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *PaymentDetailDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*PaymentDetail, error) {
	methodName := "find"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PaymentDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PaymentDetail))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *PaymentDetailDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PaymentDetail, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PaymentDetail, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PaymentDetail))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *PaymentDetailDAO) Upsert(ctx context.Context, data *PaymentDetail) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(paymentDetailDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(paymentDetailDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
