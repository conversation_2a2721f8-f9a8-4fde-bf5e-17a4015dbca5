// Code generated by mockery v2.26.1. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockICardTransactionDetailDAO is an autogenerated mock type for the ICardTransactionDetailDAO type
type MockICardTransactionDetailDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockICardTransactionDetailDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*CardTransactionDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*CardTransactionDetail, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*CardTransactionDetail); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CardTransactionDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockICardTransactionDetailDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*CardTransactionDetail, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*CardTransactionDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*CardTransactionDetail, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*CardTransactionDetail); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CardTransactionDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockICardTransactionDetailDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *CardTransactionDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*CardTransactionDetail, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *CardTransactionDetail); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransactionDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockICardTransactionDetailDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*CardTransactionDetail, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *CardTransactionDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*CardTransactionDetail, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *CardTransactionDetail); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransactionDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockICardTransactionDetailDAO) Save(ctx context.Context, newData *CardTransactionDetail) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransactionDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockICardTransactionDetailDAO) SaveBatch(ctx context.Context, newData []*CardTransactionDetail) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*CardTransactionDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockICardTransactionDetailDAO) Update(ctx context.Context, newData *CardTransactionDetail) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransactionDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockICardTransactionDetailDAO) UpdateEntity(ctx context.Context, preData *CardTransactionDetail, newData *CardTransactionDetail) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransactionDetail, *CardTransactionDetail) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockICardTransactionDetailDAO) Upsert(ctx context.Context, newData *CardTransactionDetail) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransactionDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockICardTransactionDetailDAO interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockICardTransactionDetailDAO creates a new instance of MockICardTransactionDetailDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockICardTransactionDetailDAO(t mockConstructorTestingTNewMockICardTransactionDetailDAO) *MockICardTransactionDetailDAO {
	mock := &MockICardTransactionDetailDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
