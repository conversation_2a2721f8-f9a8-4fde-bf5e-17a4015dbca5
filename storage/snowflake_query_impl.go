// Code generated by mockery 2.9.4. DO NOT EDIT.

package storage

import (
	sql "database/sql"

	mock "github.com/stretchr/testify/mock"
)

// MockSnowflakeQueryImpl is an autogenerated mock type for the SnowflakeQueryImpl type
type MockSnowflakeQueryImpl struct {
	mock.Mock
}

// GetTransactionDetail provides a mock function with given fields:
func (_m *MockSnowflakeQueryImpl) GetSTM453TransactionDetail(fastTransactionID string) (*sql.Rows, error) {
	ret := _m.Called()

	var r0 *sql.Rows
	if rf, ok := ret.Get(0).(func(string) *sql.Rows); ok {
		r0 = rf(fastTransactionID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sql.Rows)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(fastTransactionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
