package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var interestAggregateV2Dao = "interest_aggregateV2_dao"

// GetID implements the GetID function for Entity Interface
func (impl *InterestAggregateV2) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *InterestAggregateV2) SetID(ID string) {
	// replace the logic to populate unique ID for InterestAggregate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *InterestAggregateV2) NewEntity() data.Entity {
	return &InterestAggregateV2{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *InterestAggregateV2) GetTableName() string {
	return "interest_aggregate_v2"
}

// IInterestAggregateV2DAO is the dao interface for InterestAggregateV2
//
//go:generate mockery --name IInterestAggregateV2DAO --inpackage --case=underscore
type IInterestAggregateV2DAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error)

	// Save ...
	Save(ctx context.Context, newData *InterestAggregateV2) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*InterestAggregateV2) error

	// Update ...
	Update(ctx context.Context, newData *InterestAggregateV2) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *InterestAggregateV2, newData *InterestAggregateV2) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *InterestAggregateV2) error
}

// InterestAggregateV2DAO ...
type InterestAggregateV2DAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewInterestAggregateV2DAO creates a data access object for InterestAggregate
func NewInterestAggregateV2DAO(cfg *servus.DataConfig, statsDClient statsd.Client) *InterestAggregateV2DAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &InterestAggregateV2DAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &InterestAggregateV2{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *InterestAggregateV2DAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InterestAggregateV2), err
}

// LoadByIDOnSlave ...
func (dao *InterestAggregateV2DAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InterestAggregateV2, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InterestAggregateV2), err
}

// Save ...
func (dao *InterestAggregateV2DAO) Save(ctx context.Context, entity *InterestAggregateV2) error {
	methodName := "save"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *InterestAggregateV2DAO) SaveBatch(ctx context.Context, entities []*InterestAggregateV2) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *InterestAggregateV2DAO) Update(ctx context.Context, data *InterestAggregateV2) error {
	methodName := "update"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *InterestAggregateV2DAO) UpdateEntity(ctx context.Context, preData *InterestAggregateV2, newData *InterestAggregateV2) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *InterestAggregateV2DAO) Find(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error) {
	methodName := "find"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InterestAggregateV2, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InterestAggregateV2))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *InterestAggregateV2DAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InterestAggregateV2, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InterestAggregateV2, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InterestAggregateV2))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *InterestAggregateV2DAO) Upsert(ctx context.Context, data *InterestAggregateV2) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(interestAggregateV2Dao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(interestAggregateV2Dao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
