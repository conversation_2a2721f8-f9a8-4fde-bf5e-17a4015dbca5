// Code generated by mockery v2.30.1. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"

	sql "database/sql"
)

// MockDatabaseStore is an autogenerated mock type for the DatabaseStore type
type MockDatabaseStore struct {
	mock.Mock
}

// GetDatabaseHandle provides a mock function with given fields: ctx, config
func (_m *MockDatabaseStore) GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	ret := _m.Called(ctx, config)

	var r0 *sql.DB
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *data.MysqlConfig) (*sql.DB, error)); ok {
		return rf(ctx, config)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *data.MysqlConfig) *sql.DB); ok {
		r0 = rf(ctx, config)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sql.DB)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *data.MysqlConfig) error); ok {
		r1 = rf(ctx, config)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTxnDataByExternalIDFromDB provides a mock function with given fields: ctx, externalID, dbClient
func (_m *MockDatabaseStore) GetTxnDataByExternalIDFromDB(ctx context.Context, externalID string, dbClient *sql.DB) ([]*TransactionsData, error) {
	ret := _m.Called(ctx, externalID, dbClient)

	var r0 []*TransactionsData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *sql.DB) ([]*TransactionsData, error)); ok {
		return rf(ctx, externalID, dbClient)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *sql.DB) []*TransactionsData); ok {
		r0 = rf(ctx, externalID, dbClient)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*TransactionsData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *sql.DB) error); ok {
		r1 = rf(ctx, externalID, dbClient)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewDatabaseStore creates a new instance of DatabaseStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabaseStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDatabaseStore {
	mock := &MockDatabaseStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
