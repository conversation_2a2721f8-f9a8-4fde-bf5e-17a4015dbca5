package storage

import (
	"database/sql"
	"fmt"
)

// SnowflakeQuery ...
type SnowflakeQuery struct{}

// SnowflakeQueryImpl ...
type SnowflakeQueryImpl interface {
	GetSTM453TransactionDetail(fastTransactionID string) (*sql.Rows, error)
}

// GetSTM453TransactionDetail ...
func (q *SnowflakeQuery) GetSTM453TransactionDetail(fastTransactionID string) (*sql.Rows, error) {
	query := fmt.Sprintf(`select TRANSACTION_TYPE, TRANSACTION_AMOUNT, TRANSACTION_SUBFAMILY, TRANSACTION_TIMESTAMP, FAST_TRANSACTION_ID, SENDER_ACCOUNT_NUMBER, RECIPIENT_ACCOUNT_NUMBER, RECIPIENT_BANK from GOLD.OPS.STM453_REPORT " +
		"where FAST_TRANSACTION_ID = '%s';`,
		fastTransactionID)
	return StatementsDOPSReader.Query(query)
}
