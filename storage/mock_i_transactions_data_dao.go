// Code generated by mockery v2.26.0. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockITransactionsDataDAO is an autogenerated mock type for the ITransactionsDataDAO type
type MockITransactionsDataDAO struct {
	mock.Mock
}

// Delete provides a mock function with given fields: ctx, ID
func (_m *MockITransactionsDataDAO) Delete(ctx context.Context, ID string) error {
	ret := _m.Called(ctx, ID)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, ID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockITransactionsDataDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*TransactionsData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*TransactionsData, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*TransactionsData); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*TransactionsData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockITransactionsDataDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*TransactionsData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*TransactionsData, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*TransactionsData); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*TransactionsData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockITransactionsDataDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *TransactionsData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*TransactionsData, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *TransactionsData); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*TransactionsData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockITransactionsDataDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *TransactionsData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*TransactionsData, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *TransactionsData); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*TransactionsData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NativeQueryOneRow provides a mock function with given fields: ctx, query, fields, args
func (_m *MockITransactionsDataDAO) NativeQueryOneRow(ctx context.Context, query string, fields []interface{}, args ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, ctx, query, fields)
	_ca = append(_ca, args...)
	ret := _m.Called(_ca...)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []interface{}, ...interface{}) error); ok {
		r0 = rf(ctx, query, fields, args...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NativeQueryOneRowOnSlave provides a mock function with given fields: ctx, query, fields, args
func (_m *MockITransactionsDataDAO) NativeQueryOneRowOnSlave(ctx context.Context, query string, fields []interface{}, args ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, ctx, query, fields)
	_ca = append(_ca, args...)
	ret := _m.Called(_ca...)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []interface{}, ...interface{}) error); ok {
		r0 = rf(ctx, query, fields, args...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockITransactionsDataDAO) Save(ctx context.Context, newData *TransactionsData) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *TransactionsData) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockITransactionsDataDAO) SaveBatch(ctx context.Context, newData []*TransactionsData) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*TransactionsData) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockITransactionsDataDAO) Update(ctx context.Context, newData *TransactionsData) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *TransactionsData) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockITransactionsDataDAO) UpdateEntity(ctx context.Context, preData *TransactionsData, newData *TransactionsData) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *TransactionsData, *TransactionsData) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockITransactionsDataDAO) Upsert(ctx context.Context, newData *TransactionsData) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *TransactionsData) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockITransactionsDataDAO interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockITransactionsDataDAO creates a new instance of MockITransactionsDataDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockITransactionsDataDAO(t mockConstructorTestingTNewMockITransactionsDataDAO) *MockITransactionsDataDAO {
	mock := &MockITransactionsDataDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
