// Code generated by mockery v2.39.1. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/dakota/servus/v2/data"
)

// MockILoanDetailDAO is an autogenerated mock type for the ILoanDetailDAO type
type MockILoanDetailDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockILoanDetailDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*LoanDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*LoanDetail, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*LoanDetail); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*LoanDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockILoanDetailDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDetail, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindOnSlave")
	}

	var r0 []*LoanDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*LoanDetail, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*LoanDetail); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*LoanDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockILoanDetailDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for LoadByID")
	}

	var r0 *LoanDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*LoanDetail, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *LoanDetail); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*LoanDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockILoanDetailDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDetail, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for LoadByIDOnSlave")
	}

	var r0 *LoanDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*LoanDetail, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *LoanDetail); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*LoanDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockILoanDetailDAO) Save(ctx context.Context, newData *LoanDetail) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockILoanDetailDAO) SaveBatch(ctx context.Context, newData []*LoanDetail) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for SaveBatch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*LoanDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockILoanDetailDAO) Update(ctx context.Context, newData *LoanDetail) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockILoanDetailDAO) UpdateEntity(ctx context.Context, preData *LoanDetail, newData *LoanDetail) error {
	ret := _m.Called(ctx, preData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEntity")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanDetail, *LoanDetail) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockILoanDetailDAO) Upsert(ctx context.Context, newData *LoanDetail) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Upsert")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanDetail) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockILoanDetailDAO creates a new instance of MockILoanDetailDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockILoanDetailDAO(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockILoanDetailDAO {
	mock := &MockILoanDetailDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
