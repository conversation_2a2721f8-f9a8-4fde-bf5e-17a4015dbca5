package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	data2 "gitlab.myteksi.net/gophers/go/commons/data"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var transactionsDataDao = "transactions_data_dao"

// GetID implements the GetID function for Entity Interface
func (impl *TransactionsData) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *TransactionsData) SetID(ID string) {
	// replace the logic to populate unique ID for TransactionsData
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *TransactionsData) NewEntity() data.Entity {
	return &TransactionsData{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *TransactionsData) GetTableName() string {
	return "transactions_data"
}

// ITransactionsDataDAO is the dao interface for TransactionsData
//
//go:generate mockery --name ITransactionsDataDAO --inpackage --case=underscore
type ITransactionsDataDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error)

	// Save ...
	Save(ctx context.Context, newData *TransactionsData) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*TransactionsData) error

	// Update ...
	Update(ctx context.Context, newData *TransactionsData) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *TransactionsData, newData *TransactionsData) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *TransactionsData) error

	// NativeQueryOneRow ...
	NativeQueryOneRow(ctx context.Context, query string, fields []interface{}, args ...interface{}) error

	// NativeQueryOneRowOnSlave ...
	NativeQueryOneRowOnSlave(ctx context.Context, query string, fields []interface{}, args ...interface{}) error

	// Delete ...
	Delete(ctx context.Context, ID string) error
}

// TransactionsDataDAO ...
type TransactionsDataDAO struct {
	data.DAO
	StatsD          statsd.Client
	slaveDbStrategy data2.DbStrategy
}

// NewTransactionsDataDAO creates a data access object for TransactionsData
func NewTransactionsDataDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *TransactionsDataDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	mysqlDAO := data.NewMysqlDAO(cfg.MySQL, &TransactionsData{})
	return &TransactionsDataDAO{
		DAO:             mysqlDAO,
		StatsD:          statsDClient,
		slaveDbStrategy: mysqlDAO.SlaveStrategy,
	}
}

// LoadByID ...
func (dao *TransactionsDataDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*TransactionsData), err
}

// LoadByIDOnSlave ...
func (dao *TransactionsDataDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*TransactionsData, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*TransactionsData), err
}

// Save ...
func (dao *TransactionsDataDAO) Save(ctx context.Context, entity *TransactionsData) error {
	methodName := "save"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *TransactionsDataDAO) SaveBatch(ctx context.Context, entities []*TransactionsData) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *TransactionsDataDAO) Update(ctx context.Context, data *TransactionsData) error {
	methodName := "update"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *TransactionsDataDAO) UpdateEntity(ctx context.Context, preData *TransactionsData, newData *TransactionsData) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *TransactionsDataDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error) {
	methodName := "find"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*TransactionsData, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*TransactionsData))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *TransactionsDataDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*TransactionsData, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*TransactionsData, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*TransactionsData))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *TransactionsDataDAO) Upsert(ctx context.Context, data *TransactionsData) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(transactionsDataDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(transactionsDataDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// NativeQueryOneRowOnSlave is a custom method to query one row from slave
func (dao *TransactionsDataDAO) NativeQueryOneRowOnSlave(ctx context.Context, query string, fields []interface{}, args ...interface{}) error {
	return dao.slaveDbStrategy.NativeQueryOneRowContext(ctx, &TransactionsData{}, query, fields, args...)
}
