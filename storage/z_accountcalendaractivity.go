package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var accountCalendarActivityDao = "account_calendar_activity_dao"

// GetID implements the GetID function for Entity Interface
func (impl *AccountCalendarActivity) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *AccountCalendarActivity) SetID(ID string) {
	// replace the logic to populate unique ID for AccountCalendarActivity
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *AccountCalendarActivity) NewEntity() data.Entity {
	return &AccountCalendarActivity{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *AccountCalendarActivity) GetTableName() string {
	return "account_calendar_activity"
}

// IAccountCalendarActivityDAO is the dao interface for AccountCalendarActivity
//
//go:generate mockery --name IAccountCalendarActivityDAO --inpackage --case=underscore
type IAccountCalendarActivityDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*AccountCalendarActivity, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*AccountCalendarActivity, error)

	// Save ...
	Save(ctx context.Context, newData *AccountCalendarActivity) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*AccountCalendarActivity) error

	// Update ...
	Update(ctx context.Context, newData *AccountCalendarActivity) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *AccountCalendarActivity, newData *AccountCalendarActivity) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*AccountCalendarActivity, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*AccountCalendarActivity, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *AccountCalendarActivity) error
}

// AccountCalendarActivityDAO ...
type AccountCalendarActivityDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewAccountCalendarActivityDAO creates a data access object for AccountCalendarActivity
func NewAccountCalendarActivityDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *AccountCalendarActivityDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &AccountCalendarActivityDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &AccountCalendarActivity{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *AccountCalendarActivityDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*AccountCalendarActivity, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*AccountCalendarActivity), err
}

// LoadByIDOnSlave ...
func (dao *AccountCalendarActivityDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*AccountCalendarActivity, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*AccountCalendarActivity), err
}

// Save ...
func (dao *AccountCalendarActivityDAO) Save(ctx context.Context, entity *AccountCalendarActivity) error {
	methodName := "save"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *AccountCalendarActivityDAO) SaveBatch(ctx context.Context, entities []*AccountCalendarActivity) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *AccountCalendarActivityDAO) Update(ctx context.Context, data *AccountCalendarActivity) error {
	methodName := "update"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *AccountCalendarActivityDAO) UpdateEntity(ctx context.Context, preData *AccountCalendarActivity, newData *AccountCalendarActivity) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *AccountCalendarActivityDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*AccountCalendarActivity, error) {
	methodName := "find"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*AccountCalendarActivity, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*AccountCalendarActivity))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *AccountCalendarActivityDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*AccountCalendarActivity, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*AccountCalendarActivity, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*AccountCalendarActivity))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *AccountCalendarActivityDAO) Upsert(ctx context.Context, data *AccountCalendarActivity) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(accountCalendarActivityDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(accountCalendarActivityDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
