package storage

import (
	"context"
	"errors"
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

type stratDAO struct{}
type stratDAOWithErr struct{}
type dummyResult struct {
	method string
}

func (d *stratDAO) Find(_ context.Context, _ ...data.Condition) ([]*dummyResult, error) {
	return []*dummyResult{{method: "Find"}}, nil
}

func (d *stratDAO) FindOnSlave(_ context.Context, _ ...data.Condition) ([]*dummyResult, error) {
	return []*dummyResult{{method: "FindOnSlave"}}, nil
}

func (d *stratDAOWithErr) Find(_ context.Context, _ ...data.Condition) ([]*dummyResult, error) {
	return nil, errors.New("error")
}

var replicaTestCtx = func(ctx context.Context, enabled bool) context.Context {
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsReplicaReadEnabled").Return(enabled)
	return featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
}

func TestShouldReadFromReplica(t *testing.T) {
	t.Run("Should return false when feature flag is nil", func(t *testing.T) {
		ctx := context.Background()
		result := ShouldReadFromReplica(ctx)
		assert.Falsef(t, result, "Should return false when feature flag is not enabled")
	})

	t.Run("Should return false when feature flag is false", func(t *testing.T) {
		ctx := replicaTestCtx(context.Background(), false)
		result := ShouldReadFromReplica(ctx)
		assert.Falsef(t, result, "Should return false when feature flag is not enabled")
	})

	t.Run("Should return true when feature flag is enabled", func(t *testing.T) {
		ctx := replicaTestCtx(context.Background(), true)
		result := ShouldReadFromReplica(ctx)
		assert.Truef(t, result, "Should return true when feature flag is enabled")
	})

	t.Run("Should return false when feature flag is enabled but caller identity is not sentry t6", func(t *testing.T) {
		ctx := replicaTestCtx(context.Background(), true)
		result := ShouldReadFromReplica(ctx, ReplicaByCallerIdentity("sentry-t7"))
		assert.Falsef(t, result, "Should return false when feature flag is enabled but caller identity is not sentry t6")
	})
}

func TestFindReplicaConditionally(t *testing.T) {
	t.Run("Should return error when DAO is not interface", func(t *testing.T) {
		type dao struct{}
		ctx := replicaTestCtx(context.Background(), true)
		_, err := FindReplicaConditionally(ctx, dao{}, nil)
		require.Error(t, err)
		assert.Regexp(t, regexp.MustCompile("DAO is not a pointer"), err.Error())
	})

	t.Run("Should return error when method not found", func(t *testing.T) {
		type dao struct{}
		ctx := replicaTestCtx(context.Background(), true)
		_, err := FindReplicaConditionally(ctx, &dao{}, nil)
		require.Error(t, err)
		assert.Regexp(t, regexp.MustCompile("method .+ not found"), err.Error())
	})

	t.Run("Should not return error on Find", func(t *testing.T) {
		ctx := context.Background()
		res, err := FindReplicaConditionally(ctx, &stratDAO{}, nil)
		require.NoError(t, err)
		typedRes, ok := res.([]*dummyResult)
		assert.True(t, ok)
		assert.Equal(t, "Find", typedRes[0].method)
	})

	t.Run("Should not return error on FindOnSlave", func(t *testing.T) {
		ctx := replicaTestCtx(context.Background(), true)
		res, err := FindReplicaConditionally(ctx, &stratDAO{}, nil)
		require.NoError(t, err)
		typedRes, ok := res.([]*dummyResult)
		assert.True(t, ok)
		assert.Equal(t, "FindOnSlave", typedRes[0].method)
	})

	t.Run("Should return error on Find error", func(t *testing.T) {
		ctx := context.Background()
		_, err := FindReplicaConditionally(ctx, &stratDAOWithErr{}, nil)
		assert.Error(t, err)
	})
	t.Run("Should return error on nil dao", func(t *testing.T) {
		ctx := context.Background()
		_, err := FindReplicaConditionally(ctx, nil, nil)
		require.Error(t, err)
		assert.Regexp(t, regexp.MustCompile("DAO is not a pointer"), err.Error())
	})
}
