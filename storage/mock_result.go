// Code generated by mockery v2.14.0. DO NOT EDIT.

package storage

import mock "github.com/stretchr/testify/mock"

// MockResult is an autogenerated mock type for the Result type
type MockResult struct {
	mock.Mock
}

// LastInsertId provides a mock function with given fields:
func (_m *MockResult) LastInsertId() (int64, error) {
	ret := _m.Called()

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RowsAffected provides a mock function with given fields:
func (_m *MockResult) RowsAffected() (int64, error) {
	ret := _m.Called()

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockResult interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockResult creates a new instance of MockResult. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockResult(t mockConstructorTestingTNewMockResult) *MockResult {
	mock := &MockResult{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
