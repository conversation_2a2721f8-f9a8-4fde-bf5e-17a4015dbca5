package storage

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
)

func TestTransactionsData_GetCustomCategoryAndIconURLForTransferMoney(t *testing.T) {
	scenarios := []struct {
		name             string
		txnData          *TransactionsData
		expectedCategory string
	}{
		{
			name: "when debit pocket funding txn it should return withdrawal category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.PocketFundingTransactionSubType,
				DebitOrCredit:      constants.DEBIT,
			},
			expectedCategory: "Withdrawal",
		},
		{
			name: "when credit pocket funding txn it should return withdrawal category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.PocketFundingTransactionSubType,
				DebitOrCredit:      constants.CREDIT,
			},
			expectedCategory: "Transfer In",
		},
		{
			name: "when debit pocket withdrawal txn it should return withdrawal category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.PocketWithdrawalTransactionSubType,
				DebitOrCredit:      constants.DEBIT,
			},
			expectedCategory: "Withdrawal",
		},
		{
			name: "when credit pocket withdrawal txn it should return transfer in category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.PocketWithdrawalTransactionSubType,
				DebitOrCredit:      constants.CREDIT,
			},
			expectedCategory: "Transfer In",
		},
		{
			name: "when debit intrabank txn it should return transfer out category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.IntrabankTransactionSubtype,
				DebitOrCredit:      constants.DEBIT,
			},
			expectedCategory: "Transfer Out",
		},
		{
			name: "when credit intrabank txn it should return transfer in category",
			txnData: &TransactionsData{
				TransactionSubtype: constants.IntrabankTransactionSubtype,
				DebitOrCredit:      constants.CREDIT,
			},
			expectedCategory: "Transfer In",
		},
		{
			name:             "when unknown txn it should return empty category",
			txnData:          &TransactionsData{},
			expectedCategory: "",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			category, _ := scenario.txnData.GetCustomCategoryAndIconURLForTransferMoney()
			assert.Equal(t, scenario.expectedCategory, category.Name)
		})
	}
}

func TestTransactionsData_GetPocketTxnCounterPartyAccountID(t *testing.T) {
	scenarios := []struct {
		name                          string
		txnData                       *TransactionsData
		expectedCounterPartyAccountID string
	}{
		{
			name: "when funding into pocket",
			txnData: &TransactionsData{
				AccountID:    "***********",
				BatchDetails: []byte("{\"source_account_id\":      \"***********\",\n\t\t\t\t\t\"destination_account_id\": \"***********000\"}"),
			},
			expectedCounterPartyAccountID: "***********000",
		},
		{
			name: "when withdraw from pocket",
			txnData: &TransactionsData{
				AccountID:    "***********000",
				BatchDetails: []byte("{\"source_account_id\":      \"***********000\",\n\t\t\t\t\t\"destination_account_id\": \"***********\"}"),
			},
			expectedCounterPartyAccountID: "***********",
		},
		{
			name: "when viewing from Main Account - CASA <-> Boost pocket txn",
			txnData: &TransactionsData{
				AccountID:    "***********",
				BatchDetails: []byte("{\"pocket_id\":      \"*****************\",\n\t\t\t\t\t\"sub_account_type\": \"BOOST_POCKET\"}"),
			},
			expectedCounterPartyAccountID: "*****************",
		},
		{
			name: "when viewing from Boost Pocket - CASA <-> Boost pocket",
			txnData: &TransactionsData{
				AccountID:    "*****************",
				BatchDetails: []byte("{\"pocket_id\":      \"*****************\",\n\t\t\t\t\t\"sub_account_type\": \"BOOST_POCKET\"}"),
			},
			expectedCounterPartyAccountID: "",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			counterPartyAccountID := scenario.txnData.GetPocketTxnCounterPartyAccountID(context.Background())
			assert.Equal(t, scenario.expectedCounterPartyAccountID, counterPartyAccountID)
		})
	}
}

func TestTransactionsData_IsInterestPayoutTxn(t *testing.T) {
	scenarios := []struct {
		name             string
		txnData          *TransactionsData
		expectedResponse bool
	}{
		{
			name: "happy path",
			txnData: &TransactionsData{
				TransactionType: constants.InterestPayout,
			},
			expectedResponse: true,
		},
		{
			name: "sad path",
			txnData: &TransactionsData{
				TransactionType: constants.SendMoneyTxType,
			},
			expectedResponse: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			resp := scenario.txnData.IsInterestPayoutTxn()
			assert.Equal(t, scenario.expectedResponse, resp)
		})
	}
}

func TestTransactionsData_IsTaxPayoutTxn(t *testing.T) {
	scenarios := []struct {
		name             string
		txnData          *TransactionsData
		expectedResponse bool
	}{
		{
			name: "happy path",
			txnData: &TransactionsData{
				TransactionType: "TAX_PAYOUT",
			},
			expectedResponse: true,
		},
		{
			name: "sad path",
			txnData: &TransactionsData{
				TransactionType: constants.InterestPayoutTransactionType,
			},
			expectedResponse: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			resp := scenario.txnData.IsTaxPayoutTxn()
			assert.Equal(t, scenario.expectedResponse, resp)
		})
	}
}

func TestTransactionsData_IsOpsTxn(t *testing.T) {
	scenarios := []struct {
		name             string
		txnData          *TransactionsData
		expectedResponse bool
	}{
		{
			name: "happy path",
			txnData: &TransactionsData{
				TransactionType: constants.OPSTransactionType,
			},
			expectedResponse: true,
		},
		{
			name: "sad path",
			txnData: &TransactionsData{
				TransactionType: constants.InterestPayoutTransactionType,
			},
			expectedResponse: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			resp := scenario.txnData.IsOpsTxn()
			assert.Equal(t, scenario.expectedResponse, resp)
		})
	}
}

// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:351:								IsRewardsTxn							0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:356:								IsAtmFeeTxn							0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:361:								IsCardOpsTxn							0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:366:								IsCardTxn							0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:371:								GetTransactionDetail						0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:381:								GetRewardsCampaignName						0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:390:								GetRewardsCampaignDescription					0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:399:								GetInterestCampaignName						0.0%
// gitlab.com/gx-regional/dbmy/transaction-history/storage/transactions_data.go:408:								GetInterestCampaignDescription					0.0%

func TestTransactionsData_IsRewardsTxn(t *testing.T) {
	t.Run("should return true when transaction type is REWARDS_CASHBACK", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.RewardsCashback,
		}
		assert.True(t, txnData.IsRewardsTxn())
	})

	t.Run("should return true when transaction type is REWARDS_CASHBACK_REVERSAL", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.RewardsCashbackReversal,
		}
		assert.True(t, txnData.IsRewardsTxn())
	})

	t.Run("should return false when transaction type is not REWARDS_CASHBACK or REWARDS_CASHBACK_REVERSAL", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: "SOME_OTHER_TYPE",
		}
		assert.False(t, txnData.IsRewardsTxn())
	})
}

func TestTransactionsData_IsAtmFeeTxn(t *testing.T) {
	t.Run("should return true when transaction type is DOMESTIC_ATM_FEE", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.DomesticAtmFeeTransactionType,
		}
		assert.True(t, txnData.IsAtmFeeTxn())
	})
	t.Run("should return true when transaction type is INTERNATIONAL_ATM_FEE_WAIVER", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.DomesticAtmFeeWaiverTransactionType,
		}
		assert.True(t, txnData.IsAtmFeeTxn())
	})
	t.Run("should return false when transaction type is not DOMESTIC_ATM_FEE or INTERNATIONAL_ATM_FEE_WAIVER", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: "SOME_OTHER_TYPE",
		}
		assert.False(t, txnData.IsAtmFeeTxn())
	})
}

func TestTransactionsData_IsCardOpsTxn(t *testing.T) {
	t.Run("should return true when transaction type is MANUAL_DEBIT_RECON", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.ManualDebitReconTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is MANUAL_CREDIT_RECON", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.ManualCreditReconTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is DISPUTE_CHARGEBACK", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.DisputeChargeBackTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is DISPUTE_BANK_REFUND", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.DisputeBankRefundTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is DISPUTE_BANK_FRAUD_REFUND", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.DisputeBankFraudRefundTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is PROVISIONAL_CREDIT", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.ProvisionalCreditTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is PROVISIONAL_CREDIT_REVERSAL", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.ProvisionalCreditReversalTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return true when transaction type is OPERATIONAL_LOSS", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: constants.OperationalLossTransactionType,
		}
		assert.True(t, txnData.IsCardOpsTxn())
	})
	t.Run("should return false when transaction type is not any of the card ops transaction type", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: "SOME_OTHER_TYPE",
		}
		assert.False(t, txnData.IsCardOpsTxn())
	})
}

func TestTransactionsData_IsBizDepositDomain(t *testing.T) {
	t.Run("should return true when transaction domain is BIZ_DEPOSITS", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionDomain: constants.BizDepositsDomain,
		}
		assert.True(t, txnData.IsBizDepositDomain())
	})
	t.Run("should return false when transaction domain is not BIZ_DEPOSITS", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionDomain: constants.DepositsDomain,
		}
		assert.False(t, txnData.IsBizDepositDomain())
	})
}

func TestTransactionsData_GetTxnScenarioKeyByBizDepositDomain(t *testing.T) {
	scenarios := []struct {
		name     string
		txnData  *TransactionsData
		expected string
	}{
		{
			name: "Fund In",
			txnData: &TransactionsData{
				TransactionType: constants.FundInTxType,
			},
			expected: constants.BizFundInScenarioKey,
		},
		{
			name: "Fund In Reversal",
			txnData: &TransactionsData{
				TransactionType: constants.FundInRevTxType,
			},
			expected: constants.BizFundInReversalScenarioKey,
		},
		{
			name: "Receive Money",
			txnData: &TransactionsData{
				TransactionType: constants.ReceiveMoneyTxType,
			},
			expected: constants.BizReceiveMoneyScenarioKey,
		},
		{
			name: "Receive Money Reversal",
			txnData: &TransactionsData{
				TransactionType: constants.ReceiveMoneyRevTxType,
			},
			expected: constants.BizReceiveMoneyReversalScenarioKey,
		},
		{
			name: "Send Money",
			txnData: &TransactionsData{
				TransactionType: constants.SendMoneyTxType,
			},
			expected: constants.BizSendMoneyScenarioKey,
		},
		{
			name: "Send Money Reversal",
			txnData: &TransactionsData{
				TransactionType: constants.SendMoneyRevTxType,
			},
			expected: constants.BizSendMoneyReversalScenarioKey,
		},
		{
			name: "Transfer Money Debit",
			txnData: &TransactionsData{
				TransactionType: constants.TransferMoneyTxType,
				DebitOrCredit:   constants.DEBIT,
			},
			expected: constants.BizTransferMoneyDebitScenarioKey,
		},
		{
			name: "Transfer Money Credit",
			txnData: &TransactionsData{
				TransactionType: constants.TransferMoneyTxType,
				DebitOrCredit:   constants.CREDIT,
			},
			expected: constants.BizTransferMoneyCreditScenarioKey,
		},
		{
			name: "Transfer Money Reversal Debit",
			txnData: &TransactionsData{
				TransactionType: constants.TransferMoneyRevTxType,
				DebitOrCredit:   constants.DEBIT,
			},
			expected: constants.BizTransferMoneyReversalDebitScenarioKey,
		},
		{
			name: "Transfer Money Reversal Credit",
			txnData: &TransactionsData{
				TransactionType: constants.TransferMoneyRevTxType,
				DebitOrCredit:   constants.CREDIT,
			},
			expected: constants.BizTransferMoneyReversalCreditScenarioKey,
		},
		{
			name: "Interest Payout",
			txnData: &TransactionsData{
				TransactionType: constants.InterestPayoutTransactionType,
			},
			expected: constants.BizInterestPayoutScenarioKey,
		},
		{
			name: "Apply Earmark",
			txnData: &TransactionsData{
				TransactionType: constants.ApplyEarmarkTransactionType,
			},
			expected: constants.BizApplyEarmarkScenarioKey,
		},
		{
			name: "Release Earmark",
			txnData: &TransactionsData{
				TransactionType: constants.ReleaseEarmarkTransactionType,
			},
			expected: constants.BizReleaseEarmarkScenarioKey,
		},
		{
			name: "Others",
			txnData: &TransactionsData{
				TransactionType: "test",
			},
			expected: constants.Default,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			key := scenario.txnData.getTxnScenarioKeyByBizDepositDomain()
			assert.Equal(t, scenario.expected, key)
		})
	}
}

func TestTransactionsData_IsCardMaintenanceFeeTxn(t *testing.T) {
	t.Run("should return true when transaction type is one of card maintenance fee", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionDomain: constants.DebitCardDomain,
			TransactionType:   constants.CardAnnualFeeWaiverTransactionType,
		}
		assert.True(t, txnData.IsCardMaintenanceFeeTxn())
	})
	t.Run("should return false when transaction type is not one of card maintenance fee", func(t *testing.T) {
		txnData := &TransactionsData{
			TransactionType: "SOME_OTHER_TYPE",
		}
		assert.False(t, txnData.IsCardMaintenanceFeeTxn())
	})
}

func TestTransactionsData_GetPocketServiceType(t *testing.T) {
	t.Run("should return service_type when sub_account_type is not specified", func(t *testing.T) {
		jsonBytes, err := json.Marshal(map[string]string{
			"service_type": "asdfasdf",
		})
		txnData := &TransactionsData{
			BatchDetails: jsonBytes,
		}
		assert.Equal(t, txnData.GetPocketServiceType(context.Background()), "asdfasdf")
		assert.NoError(t, err)
	})

	t.Run("should return service_type when sub_account_type is not BOOST_POCKET", func(t *testing.T) {
		jsonBytes, err := json.Marshal(map[string]string{
			"sub_account_type": "NOT_BOOST_POCKET",
			"service_type":     "asdfasdf",
		})
		txnData := &TransactionsData{
			BatchDetails: jsonBytes,
		}
		assert.Equal(t, txnData.GetPocketServiceType(context.Background()), "asdfasdf")
		assert.NoError(t, err)
	})

	t.Run("should return BOOST_POCKET when sub_account_type is BOOST_POCKET", func(t *testing.T) {
		jsonBytes, err := json.Marshal(map[string]string{
			"sub_account_type": "BOOST_POCKET",
			"service_type":     "asdfasdf",
		})
		txnData := &TransactionsData{
			BatchDetails: jsonBytes,
		}
		assert.Equal(t, txnData.GetPocketServiceType(context.Background()), "BOOST_POCKET")
		assert.NoError(t, err)
	})

	t.Run("should return empty string when json.Unmarshal returns error", func(t *testing.T) {
		// This will cause json.Unmarshal into a map[string]string to fail
		jsonBytes, err := json.Marshal(map[string]bool{
			"some_other_key": true,
		})
		txnData := &TransactionsData{
			BatchDetails: jsonBytes,
		}
		assert.Equal(t, txnData.GetPocketServiceType(context.Background()), "")
		assert.NoError(t, err)
	})
}
