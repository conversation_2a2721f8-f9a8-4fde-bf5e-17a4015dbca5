package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CardTransactionDetail ...
type CardTransactionDetail struct {
	ID                            uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	CardTransactionID             string          `sql-col:"card_transaction_id"`
	CardID                        string          `sql-col:"card_id"`
	TransactionDomain             string          `sql-col:"transaction_domain"`
	TransactionType               string          `sql-col:"transaction_type"`
	TransactionSubType            string          `sql-col:"transaction_subtype"`
	TransferType                  string          `sql-col:"transfer_type"`
	Amount                        int64           `sql-col:"amount"`
	Currency                      string          `sql-col:"currency"`
	OriginalAmount                int64           `sql-col:"original_amount"`
	CaptureOriginalAmountTillDate int64           `sql-col:"captured_original_amount_till_date"`
	OriginalCurrency              string          `sql-col:"original_currency"`
	CaptureAmount                 int64           `sql-col:"capture_amount"`
	CaptureAmountTillDate         int64           `sql-col:"captured_amount_till_date"`
	AccountID                     string          `sql-col:"account_id"`
	Account                       json.RawMessage `sql-col:"account"  sql-where:"false" data-type:"json"`
	MerchantDescription           string          `sql-col:"merchant_description"`
	Status                        string          `sql-col:"status"`
	StatusDetails                 json.RawMessage `sql-col:"status_details"  sql-where:"false" data-type:"string"`
	Metadata                      json.RawMessage `sql-col:"metadata" sql-where:"false"  data-type:"json"`
	TailCardNumber                string          `sql-col:"tail_card_number"`
	CreationTimestamp             time.Time       `sql-col:"creation_timestamp"`
	ValueTimestamp                time.Time       `sql-col:"value_timestamp"`
	CreatedAt                     time.Time       `sql-col:"created_at"`
	UpdatedAt                     time.Time       `sql-col:"updated_at"`
}

// CardDataKey ...
type CardDataKey struct {
	CardTxnID string
	AccountID string
}

// GetFormattedTxnStatus ...
// To enable product requirement of displaying status as `PROCESSING` when authorization is completed but
// not settled yet, `AUTHORIZED` status is remapped to `PROCESSING`.
func (d *CardTransactionDetail) GetFormattedTxnStatus() string {
	if d.Status == constants.AuthorizedStatus {
		return constants.ProcessingStatus
	}

	if d.Status == constants.CompletedStatus && d.TransactionType == constants.SpendRefundTransactionType {
		return constants.RefundedStatus
	}

	return d.Status
}

// GetFormattedTxnStatusForTxnDetails to accomodate the logic in card transaction details screen in the App to show
// only below statuses COMPLETED/PROCESSING
func (d *CardTransactionDetail) GetFormattedTxnStatusForTxnDetails() string {
	if d.Status == constants.AuthorizedStatus {
		return constants.ProcessingStatus
	}

	return d.Status
}

// GetMetadata ...
func (d *CardTransactionDetail) GetMetadata(ctx context.Context) *dto.CardDetailMetadata {
	var md dto.CardDetailMetadata
	err := json.Unmarshal(d.Metadata, &md)
	if err != nil {
		slog.FromContext(ctx).Warn("cardTransactionDetail.GetMetadata", fmt.Sprintf("Error parsing Metadata, err: %s", err.Error()))
	}
	return &md
}

// IsForeignTransaction determine is foreign transaction by CurrencyConvType
// https://gxbank.atlassian.net/wiki/spaces/Digicard/pages/*********/Local+vs+Foreign+Transactions
func (d *CardTransactionDetail) IsForeignTransaction(ctx context.Context) bool {
	md := d.GetMetadata(ctx)
	fxTxnTypes := map[constants.CurrencyConvType]struct{}{
		constants.DCC: {},
		constants.FX:  {},
	}
	_, found := fxTxnTypes[constants.CurrencyConvType(md.CurrencyConvType)]
	return found
}

// IsCardMaintenanceFee determine is card maintenance fee transaction
func (d *CardTransactionDetail) IsCardMaintenanceFee() bool {
	return lo.Contains(constants.CardMaintenanceFeeTypes, d.TransactionType)
}

// HasCurrencyConversion determine the transaction should show the currency conversion rate
// even though DCC is also foreign transaction but the conversion rate will be handled by Mastercard
// the conversion rate return from EN will be ******** and the request currency and original currency will be using
// the same currency
func (d *CardTransactionDetail) HasCurrencyConversion(ctx context.Context) bool {
	md := d.GetMetadata(ctx)
	return constants.CurrencyConvType(md.CurrencyConvType) == constants.FX
}

// BuildCardDataKey ...
func BuildCardDataKey(txnID, acctID string) CardDataKey {
	return CardDataKey{
		CardTxnID: txnID,
		AccountID: acctID,
	}
}
