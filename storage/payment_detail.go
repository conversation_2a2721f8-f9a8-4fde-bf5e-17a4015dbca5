package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// PaymentDetail ...
type PaymentDetail struct {
	ID                    uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	UUID                  string          `sql-col:"uuid"`
	TransactionID         string          `sql-col:"transaction_id"`
	TransactionDomain     string          `sql-col:"transaction_domain"`
	TransactionType       string          `sql-col:"transaction_type"`
	TransactionSubType    string          `sql-col:"transaction_subtype"`
	Amount                int64           `sql-col:"amount"`
	Currency              string          `sql-col:"currency"`
	AccountID             string          `sql-col:"account_id"`
	CounterPartyAccountID string          `sql-col:"counter_party_account_id"`
	Account               json.RawMessage `sql-col:"account" sql-where:"false" data-type:"json"`
	CounterPartyAccount   json.RawMessage `sql-col:"counter_party_account" sql-where:"false" data-type:"json"`
	Status                string          `sql-col:"status"`
	StatusDetails         json.RawMessage `sql-col:"status_details" data-type:"json"`
	Metadata              json.RawMessage `sql-col:"metadata" data-type:"json"`
	CreatedAt             time.Time       `sql-col:"created_at"`
	UpdatedAt             time.Time       `sql-col:"updated_at"`
	CreationTimestamp     time.Time       `sql-col:"creation_timestamp"`
	ValueTimestamp        time.Time       `sql-col:"value_timestamp"`
}

// PaymentDataKey ...
type PaymentDataKey struct {
	PaymentBatchID string
	AccountID      string
}

// GetPaymentMetadata ...
func (d *PaymentDetail) GetPaymentMetadata(ctx context.Context) *dto.PaymentMetaData {
	var paymentMetadata dto.PaymentMetaData
	err := json.Unmarshal(d.Metadata, &paymentMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn("paymentDetail.GetPaymentMetadata", fmt.Sprintf("Error parsing Metadata, err: %s", err.Error()))
	}
	return &paymentMetadata
}

// GetCounterPartyAccount ...
func (d *PaymentDetail) GetCounterPartyAccount(ctx context.Context) *dto.AccountDetail {
	var counterPartyAccount dto.AccountDetail
	err := json.Unmarshal(d.CounterPartyAccount, &counterPartyAccount)
	if err != nil {
		slog.FromContext(ctx).Warn("paymentDetail.GetCounterPartyAccount", fmt.Sprintf("Error parsing CounterPartyAccount, err: %s", err.Error()))
	}
	return &counterPartyAccount
}

// GetStatusDetails ...
func (d *PaymentDetail) GetStatusDetails(ctx context.Context) *dto.StatusDetails {
	var statusDetails dto.StatusDetails
	err := json.Unmarshal(d.StatusDetails, &statusDetails)
	if err != nil {
		slog.FromContext(ctx).Warn("paymentDetail.GetStatusDetails", fmt.Sprintf("Error parsing StatusDetails, err: %s", err.Error()))
		return nil
	}
	return &statusDetails
}

// GetRemarksFromMetadata ...
func (d *PaymentDetail) GetRemarksFromMetadata(ctx context.Context) string {
	metadata := d.GetPaymentMetadata(ctx)
	if metadata != nil {
		return metadata.Remarks
	}
	return ""
}

// BuildPaymentDataKey ...
func BuildPaymentDataKey(txnID, acctID string) PaymentDataKey {
	return PaymentDataKey{
		PaymentBatchID: txnID,
		AccountID:      acctID,
	}
}

// GetOriginalTransactionIDFromMetadata ...
func (d *PaymentDetail) GetOriginalTransactionIDFromMetadata(ctx context.Context) string {
	metadata := d.GetPaymentMetadata(ctx)
	if metadata != nil {
		return metadata.OriginalTransactionID
	}
	return ""
}

// GetGrabActivityTypeFromMetadata ...
func (d *PaymentDetail) GetGrabActivityTypeFromMetadata(ctx context.Context) string {
	metadata := d.GetPaymentMetadata(ctx)
	if metadata != nil {
		return metadata.GrabActivityType
	}
	return ""
}

// GetCounterpartyDisplayNameFromMetadata ...
func (d *PaymentDetail) GetCounterpartyDisplayNameFromMetadata(ctx context.Context) string {
	metadata := d.GetPaymentMetadata(ctx)
	if metadata != nil {
		return metadata.CounterPartyDisplayName
	}
	return ""
}

// GetServiceTypeFromMetadata ...
func (d *PaymentDetail) GetServiceTypeFromMetadata(ctx context.Context) string {
	metadata := d.GetPaymentMetadata(ctx)
	if metadata != nil {
		return metadata.ServiceType
	}
	return ""
}

// IsQrTxn determines if the transaction is a qr transaction
func (d *PaymentDetail) IsQrTxn(ctx context.Context) bool {
	serviceType := d.GetServiceTypeFromMetadata(ctx)
	return utils.SearchStringArray(constants.QrTransactionTypes, serviceType)
}

// GetGrabRecipientReferenceFromActivityType ...
func (d *PaymentDetail) GetGrabRecipientReferenceFromActivityType(ctx context.Context) string {
	switch d.GetGrabActivityTypeFromMetadata(ctx) {
	case constants.GrabCancellationFee:
		return "Cancellation fee"
	case constants.GrabOutstandingFee:
		return "Outstanding fee"
	case constants.GrabNoShowFee:
		return "No-show fee"
	}
	return ""
}

// GetGrabRefundRecipientReference ...
func (d *PaymentDetail) GetGrabRefundRecipientReference(ctx context.Context) string {
	originalTxnID := d.GetOriginalTransactionIDFromMetadata(ctx)
	return "Refund to " + originalTxnID
}
