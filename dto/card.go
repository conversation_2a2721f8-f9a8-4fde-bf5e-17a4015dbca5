package dto

// CardDetailMetadata ...
type CardDetailMetadata struct {
	AuthorizationID          string `json:"authorizationID"`
	CurrencyConvType         string `json:"currencyConvType"`
	MaskedCardNumber         string `json:"maskedCardNumber"`
	TransactionType          string `json:"transactionType"`
	TransactionCategory      string `json:"transactionCategory"`
	TransactionSubCategory   string `json:"transactionSubCategory"`
	RetrievalReferenceNumber string `json:"retrievalReferenceNumber"`
	ThreeDsValidation        string `json:"threeDsValidation"`
	DiFraudScore             string `json:"diFraudScore,omitempty"`
	NetworkID                string `json:"networkID"`
	TerminalID               string `json:"terminalID"`
	AuthTimestamp            string `json:"authTimestamp"`
	SettlementTimestamp      string `json:"settlementTimestamp"`
	RefundType               string `json:"refundType"`
	OriginalChargeID         string `json:"OriginalChargeID"` // fix me
	RefundAmount             int64  `json:"refundAmount"`
	RefundDateTime           string `json:"refundDateTime"`
	RefundComments           string `json:"refundComments"`
	ATMTransactionFees       int64  `json:"atmTransactionFees"`
	MCConversionRate         int64  `json:"mcConversionRate"`
	PinValidation            string `json:"pinValidation"`
	CardProxyNumber          string `json:"cardProxyNumber"`
	BankFee                  string `json:"bankFee"`
	ExchangeRate             string `json:"exchangeRate"`
	Mcc                      string `json:"mcc"`
	TransactionCountry       string `json:"transactionCountry"`
	AcceptorCountryCode      string `json:"acceptorCountryCode"`
	RetailerID               string `json:"retailerID"`
	AcquirerReferenceData    string `json:"acquirerReferenceData"`
}
