package dto

import (
	"time"

	"github.com/shopspring/decimal"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
)

// AccountDetail ...
type AccountDetail struct {
	PairingID   string      `json:"pairingID"`
	Number      string      `json:"number"`
	SwiftCode   string      `json:"swiftCode"`
	DisplayName string      `json:"displayName"`
	FullName    string      `json:"fullName"`
	Proxy       ProxyObject `json:"proxy_object,omitempty"`
}

// ProxyObject ...
type ProxyObject struct {
	Channel string `json:"channel,omitempty"`
	Type    string `json:"type,omitempty"`
	Value   string `json:"value,omitempty"`
	IsQR    bool   `json:"isQR,omitempty"`
}

// StatusDetails ...
type StatusDetails struct {
	Reason      string
	Description string
}

// PaginationCursor ...
type PaginationCursor struct {
	Date               string
	ID                 int64
	FirstTransactionID uint64
	TransactionID      string
}

// PaymentMetaData ...
type PaymentMetaData struct {
	Remarks                 string `json:"remarks"`
	RecipientReference      string `json:"recipient_reference"`
	PaymentDescription      string `json:"payment_description"`
	ExternalID              string `json:"external_id"`
	BankName                string `json:"bank_name"`
	OriginalTransactionID   string `json:"original_transaction_id"`
	GrabActivityType        string `json:"grab_activity_type"`
	ActivityType            string `json:"activity_type"`
	CashAccountCode         string `json:"cash_account_code"`
	ServiceType             string `json:"serviceType"`
	CounterPartyDisplayName string `json:"counterPartyDisplayName"`
	ResidentStatus          string `json:"residentStatus"`
	PurposeCode             string `json:"purposeCode"`
	BeneficiaryCountry      string `json:"beneficiaryCountry"`
	RelationshipCode        string `json:"relationshipCode"`
}

// Payment-Engine Kafka Message DTOs. Fields Required in Tx-history are added in struct

// PaymentEngineMetaData Dto for MetaData field of Kafka Message
type PaymentEngineMetaData struct {
	Proxy ProxyObject `json:"proxy_object"`
}

// PaymentEngineProperties Dto for properties field of Kafka Message
type PaymentEngineProperties struct {
	FAST            PaymentEngineFASTProperties
	AuthorisationID string
	VerdictID       uint64
}

// PaymentEngineFASTProperties ...
type PaymentEngineFASTProperties struct {
	PurposeCode string
	LookUpRefID string
}

// PaginationParameters :struct to pass the pagination params to create links
type PaginationParameters struct {
	AccountID      string
	StartingBefore string
	EndingAfter    string
	StartDate      string
	EndDate        string
	PageSize       int64
}

// Money ...
type Money struct {
	Currency string
	Amount   int64
}

// STM453TransactionInfo ...
type STM453TransactionInfo struct {
	TransactionType        string
	TransactionAmount      float64
	TransactionSubtype     string
	TransactionTimestamp   time.Time
	FastTransactionID      string
	SenderAccountNumber    string
	RecipientAccountNumber string
	RecipientBank          string
}

// ClientTypesBatchIDs groups transaction IDs by domain
type ClientTypesBatchIDs struct {
	PaymentBatchIDs []string
	CardBatchIDs    []string
	LoanBatchIDs    []string
}

// AccountIconDetail ...
type AccountIconDetail struct {
	AccountID      string `json:"accountID,omitempty"`
	AccountIconURL string `json:"accountIconURL,omitempty"`
}

// TransactionCode ...
type TransactionCode struct {
	Domain  string `json:"domain,omitempty"`
	Type    string `json:"type,omitempty"`
	SubType string `json:"subType,omitempty"`
}

// TransactionHistorySearchRequest ...
type TransactionHistorySearchRequest struct {
	AccountID          string
	EndDate            string
	StartDate          string
	PageSize           int64
	EndingAfter        string
	StartingBefore     string
	TransactionDomain  string
	TransactionType    string
	TransactionSubtype string
	Status             string
	ProductVariantCode string
}

// TransactionsHistorySearchResponse ...
type TransactionsHistorySearchResponse struct {
	Links map[string]string              `json:"links"`
	Data  []TransactionHistorySearchData `json:"data"`
}

// TransactionHistorySearchData ...
type TransactionHistorySearchData struct {
	TransactionID                  string                   `json:"transactionID,omitempty"`
	BatchID                        string                   `json:"batchID,omitempty"`
	AccountType                    string                   `json:"accountType,omitempty"`
	DisplayName                    string                   `json:"displayName,omitempty"`
	IconURL                        string                   `json:"iconURL,omitempty"`
	AccountIconDetail              *AccountIconDetail       `json:"accountIconDetail,omitempty"`
	Amount                         int64                    `json:"amount,omitempty"`
	Currency                       string                   `json:"currency,omitempty"`
	Status                         string                   `json:"status,omitempty"`
	IsQR                           bool                     `json:"isQR,omitempty"`
	CreationTimestamp              time.Time                `json:"creationTimestamp,omitempty"`
	TransactionCode                *TransactionCode         `json:"transactionCode,omitempty"`
	CardTransactionDetail          *CxCardTransactionDetail `json:"cardTransactionDetail,omitempty"`
	Tags                           []api.TransactionTags    `json:"tags,omitempty"`
	TransactionDescription         string                   `json:"transactionDescription,omitempty"`
	TransactionRemarks             string                   `json:"transactionRemarks,omitempty"`
	CounterParty                   *CounterParty            `json:"counterParty,omitempty"`
	IsPartialSettlement            bool                     `json:"isPartialSettlement,omitempty"`
	CapturedAmountTillDate         int64                    `json:"capturedAmountTillDate,omitempty"`
	CapturedOriginalAmountTillDate int64                    `json:"capturedOriginalAmountTillDate,omitempty"`
	CounterParties                 []CounterParty           `json:"counterParties,omitempty"`
}

// CxCardTransactionDetail ...
type CxCardTransactionDetail struct {
	CardID           string            `json:"cardID,omitempty"`
	TailCardNumber   string            `json:"tailCardNumber,omitempty"`
	ExchangeRate     string            `json:"exchangeRate,omitempty"`
	BankFee          string            `json:"bankFee,omitempty"`
	CardRefundDetail *CardRefundDetail `json:"cardRefundDetail,omitempty"`
	SettlementDate   *time.Time        `json:"settlementDate,omitempty"`
}

// CardRefundDetail ...
type CardRefundDetail struct {
	OriginalChargeID string `json:"originalChargeID,omitempty"`
}

// CounterParty ...
type CounterParty struct {
	DisplayName        string            `json:"displayName,omitempty"`
	IconURL            string            `json:"iconURL,omitempty"`
	TransactionDetails map[string]string `json:"transactionDetails,omitempty"`
	SwiftCode          string            `json:"swiftCode,omitempty"`
	AccountNumber      string            `json:"accountNumber,omitempty"`
}

// TransactionDetail ...
type TransactionDetail struct {
	CampaignName        string `json:"campaignName,omitempty"`
	CampaignDescription string `json:"campaignDescription,omitempty"`
	CardID              string `json:"cardID,omitempty"`
	TailCardNumber      string `json:"tailCardNumber,omitempty"`
}

// RepaymentDetail ...
type RepaymentDetail struct {
	TotalRepaymentAmount        decimal.Decimal
	IsTotalInterestSaved        bool
	TotalInterestSaved          decimal.Decimal
	IsTotalPenalInterestCharged bool
	PenalInterestCharged        decimal.Decimal
	LoanRepaymentDetail         []LoanRepaymentDetailDTO
	Currency                    string
	LoanTransactionID           string
}

// DrawdownDetailsDTO define the structure in which metadata is published from downstream
//
//nolint:dupl
type DrawdownDetailsDTO struct {
	LoanName                string
	LoanTenorInMonths       int
	FirstInstallmentDueDate string
	LastInstallmentDueDate  string
	TotalAmountDue          decimal.Decimal
	Currency                string
	TotalPrincipalDue       decimal.Decimal
	TotalNormalInterestDue  decimal.Decimal
	AnnualPercentageRate    decimal.Decimal
	EffectiveInterestRate   decimal.Decimal
	RepaymentInstallment    map[int64]*api.Money
	ProductVariantCode      string
	ProcessingFeeRate       decimal.Decimal
	ProcessingFeeAmount     decimal.Decimal
	DisbursementAmount      decimal.Decimal
}

// RepaymentDetailsDTO defines the structure in which metadata is published from downstream
//
//nolint:dupl
type RepaymentDetailsDTO struct {
	TotalRepaymentAmount        decimal.Decimal
	IsTotalInterestSaved        bool
	TotalInterestSaved          decimal.Decimal
	IsTotalPenalInterestCharged bool
	PenalInterestCharged        decimal.Decimal
	LoanRepaymentDetail         []LoanRepaymentDetailDTO
	Currency                    string
}

// LoanRepaymentDetailDTO : DTO contains repayment components per loan
//
//nolint:dupl
type LoanRepaymentDetailDTO struct {
	LoanName                string
	AccountID               string
	RepaymentAmount         decimal.Decimal
	Currency                string
	TotalDueBeforeRepayment decimal.Decimal
	TotalDueAfterRepayment  decimal.Decimal
	IsNormalInterestSaved   bool
	NormalInterestSave      decimal.Decimal
	IsPenalInterestCharged  bool
	PenalInterestCharged    decimal.Decimal
	ValueTimestamp          time.Time
	Status                  string
	SubStatus               string
}
