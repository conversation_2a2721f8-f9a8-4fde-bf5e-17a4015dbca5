package worker

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/metrics"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic/workerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// PopulateInterestAggregateOneTimer ...
type PopulateInterestAggregateOneTimer struct {
	Store storage.DatabaseStore `inject:"dBStore"`
}

const (
	// PopulateInterestAggregateTableOneTimerSchedulerID ...
	PopulateInterestAggregateTableOneTimerSchedulerID = "POPULATE_TOTAL_INTEREST_ONE_TIMER"
)

// populateInterestAggregateOneTimer is one timer method to populate interest aggregate table by overriding.
// Can be reused in future to fix the entries as it will compute since beginning and override.
func populateInterestAggregateOneTimer(ctx context.Context, appCfg *config.AppConfig, workerObj PopulateInterestAggregateOneTimer) {
	lockDuration := time.Minute * time.Duration(appCfg.PopulateInterestAggregateOneTimerConf.LockDurationInMinutes)
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobBatchIDTagKey), utils.NewUUID()))
	mysqlConfig := appCfg.Data.MySQL.Master
	if !checkAndUpdateSchedulerLock(ctx, mysqlConfig, workerObj.Store, PopulateInterestAggregateTableOneTimerSchedulerID, lockDuration) {
		return
	}
	currentTime := time.Now()
	featFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featFlags == nil || !featFlags.IsBatchifyOneTimeInterestAggEnabled() {
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, "Run legacy one timer for interest aggregate.")
		workerlogic.LegacyPopulateInterestAggregateOneTimerLogic(ctx, appCfg)
	} else {
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, "Run batchify one timer for interest aggregate.")
		workerlogic.PopulateInterestAggregateOneTimerLogic(ctx, appCfg)
	}

	statsDClient.Duration(
		metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, currentTime,
		fmt.Sprintf("%s%s", metrics.WorkerIDTag, PopulateInterestAggregateTableOneTimerSchedulerID),
	)
}
