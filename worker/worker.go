// Package worker will contain method required for worker initialization
package worker

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"

	"gitlab.myteksi.net/gophers/go/commons/data"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/db/mysql/queries"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var statsDClient servusStatsD.Client

const (
	dailyInterestAggJob      = "ENABLE_DAILY_INTEREST_AGG_WORKER"
	oneTimeInterestAggJob    = "ENABLE_ONE_TIME_INTEREST_AGG_WORKER"
	populateInterestAggV2Job = "ENABLE_POPULATE_INTEREST_AGG_V2_WORKER"
)

// StartWorker ...
func StartWorker(app *servus.Application, appCfg *config.AppConfig) {
	statsDClient = app.GetStatsD()
	logger := app.GetLogger()
	if os.Getenv(dailyInterestAggJob) == constants.True {
		logger.Info("StartWorker", "Starting daily interest aggregate job")
		startUpdateInterestAggregateDBForDailyInterestPayoutWorker(app, appCfg)
	}
	if os.Getenv(oneTimeInterestAggJob) == constants.True {
		logger.Info("StartWorker", "Starting one time interest aggregate job")
		startPopulateInterestAggregateOneTimer(app, appCfg)
	}
	if os.Getenv(populateInterestAggV2Job) == constants.True {
		logger.Info("StartWorker", "Starting update interest aggregate V2 job")
		startPopulateInterestAggregateV2(app, appCfg)
	}
}

// startUpdateInterestAggregateDBForDailyInterestPayoutWorker to schedule workerlogic that will update interest_aggregate table on daily basis after payout
func startUpdateInterestAggregateDBForDailyInterestPayoutWorker(app *servus.Application, appCfg *config.AppConfig) {
	updateInterestAggregateTableWorkerObj := &UpdateInterestAggregateTableForDailyInterestPayout{}
	app.MustRegister("updateInterestAggregateTable", updateInterestAggregateTableWorkerObj)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), UpdateInterestAggregateTableForDailyInterestPayoutSchedulerID))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, appCfg.NewFeatureFlagRepoImpl())
	updateInterestAggregateTableJob(ctx, appCfg, *updateInterestAggregateTableWorkerObj)
}

// StartLegacyWorker starts the Go managed cron worker. This is for transitioning from the Go cron worker to the new k8s cron worker
func StartLegacyWorker(app *servus.Application, appCfg *config.AppConfig) {
	statsDClient = app.GetStatsD()
	if appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.Enabled {
		startUpdateInterestAggregateDBForDailyInterestPayoutCronWorker(app, appCfg)
	}
	startPopulateInterestAggregateOneTimer(app, appCfg)
}

// startUpdateInterestAggregateDBForDailyInterestPayoutCronWorker to schedule workerlogic that will update interest_aggregate table on daily basis after payout
func startUpdateInterestAggregateDBForDailyInterestPayoutCronWorker(app *servus.Application, appCfg *config.AppConfig) {
	updateInterestAggregateTableWorkerObj := &UpdateInterestAggregateTableForDailyInterestPayout{}
	app.MustRegister("updateInterestAggregateTable", updateInterestAggregateTableWorkerObj)
	s := gocron.NewScheduler(time.UTC)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), UpdateInterestAggregateTableForDailyInterestPayoutSchedulerID))
	featFlags := appCfg.NewFeatureFlagRepoImpl()
	ctx = featureflag.NewContextWithFeatureFlags(ctx, featFlags)
	scheduleUpdateInterestAggregateTableForDailyInterestPayoutWorker(ctx, appCfg, *updateInterestAggregateTableWorkerObj, s)
	s.StartAsync()
}

func startPopulateInterestAggregateOneTimer(app *servus.Application, appCfg *config.AppConfig) {
	populateInterestAggregateOneTimerObj := &PopulateInterestAggregateOneTimer{}
	app.MustRegister("populateInterestAggregateOneTimer", populateInterestAggregateOneTimerObj)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "UPDATE_TOTAL_INTEREST_EARNED_ONE_TIMER"))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, appCfg.NewFeatureFlagRepoImpl())
	populateInterestAggregateOneTimer(ctx, appCfg, *populateInterestAggregateOneTimerObj)
}

func startPopulateInterestAggregateV2(app *servus.Application, appCfg *config.AppConfig) {
	populateInterestAggregateV2WorkerObj := &PopulateInterestAggregateV2WorkerObj{}
	app.MustRegister("populateInterestAggregateV2", populateInterestAggregateV2WorkerObj)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "UPDATE_INTEREST_EARNED_V2"))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, appCfg.NewFeatureFlagRepoImpl())
	scheduleInterestAggregateTableV2Worker(ctx, appCfg, populateInterestAggregateV2WorkerObj)
}

func checkAndUpdateSchedulerLock(ctx context.Context, mysqlConfig *data.MysqlConfig, store storage.DatabaseStore, schedulerID string, lockDuration time.Duration) bool {
	dbs, err := store.GetDatabaseHandle(ctx, mysqlConfig)
	if err != nil {
		return false
	}

	tx, err := dbs.BeginTx(ctx, nil)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SchedulerLockLogTag, fmt.Sprintf("Failed to initiate a transaction: %s", err.Error()))
		return false
	}

	defer func() {
		if p := recover(); p != nil {
			err = tx.Rollback()
			panic(p)
		} else if err != nil {
			err = tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	now := time.Now().UTC()
	if !checkSchedulerLockDurationExpired(ctx, tx, now, schedulerID, lockDuration) {
		return false
	}
	_, err = tx.ExecContext(ctx, queries.UpdateSchedulerLockQuery, now, schedulerID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SchedulerLockLogTag, fmt.Sprintf("Failed to update the scheduler_lock with updated run_time: %s", err.Error()))
		return false
	}
	return true
}

func checkSchedulerLockDurationExpired(ctx context.Context, tx *sql.Tx, now time.Time, schedulerID string, lockDuration time.Duration) bool {
	var schedulerLockObj storage.SchedulerLock

	queryRow := tx.QueryRow(queries.SelectSchedulerLockQuery, schedulerID)
	if err := queryRow.Scan(&schedulerLockObj.ID, &schedulerLockObj.SchedulerID, &schedulerLockObj.LastRunAt, &schedulerLockObj.CreatedAt,
		&schedulerLockObj.UpdatedAt); err != nil {
		slog.FromContext(ctx).Warn(constants.SchedulerLockLogTag, fmt.Sprintf("Failed to map scheduler map object from db: %s", err.Error()))
		return false
	}

	if now.Sub(schedulerLockObj.LastRunAt.UTC()) < lockDuration {
		slog.FromContext(ctx).Info(constants.SchedulerLockLogTag, "Not running the job, scheduler_lock_duration has not expired")
		return false
	}

	return true
}
