package worker

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/helper"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	//dataWorkerCheckpoint        = "INTEREST_AGGREGATE_WORKER_CHECKPOINT"
	populateInterestAggV2LogTag = "populateInterestAggV2Log" // Log tag for this worker
)

// PopulateInterestAggregateV2WorkerObj represents the worker responsible for populating the InterestAggregateV2 table.
type PopulateInterestAggregateV2WorkerObj struct {
	LockDuration  time.Duration         // Duration for which the Redis lock is held
	RetentionDay  int                   // Number of days to retain recent transaction data
	BatchSize     int                   // Number of records to process in a single batch
	RedisClient   redis.Client          `inject:"client.redis"` // Redis client for locking and checkpointing
	checkpoint    CheckPoint            // Current checkpoint state of the worker
	ctx           context.Context       // Context for operations
	lock          redis.Lock            // Redis distributed lock
	Location      *time.Location        // Time zone location for time calculations (Asia/Singapore)
	day           time.Time             // Current day when the worker starts
	CheckPointKey string                // Key used to store the checkpoint in Redis
	Store         storage.DatabaseStore `inject:"dBStore"` // Database store interface
}

// CheckPoint represents the progress of the worker.
type CheckPoint struct {
	LastProcessedID       int       // The ID of the last InterestAggregate record processed
	LastProcessedTime     time.Time // The timestamp when the last record was processed (not actively used for processing logic in current implementation, but present)
	CurrentProcessingTime time.Time // The timestamp when the current worker run started
	Completed             bool      // Indicates if the entire data processing is complete
}

// MarshalBinary serializes the CheckPoint struct to JSON bytes.
func (c *CheckPoint) MarshalBinary() ([]byte, error) {
	return json.Marshal(c)
}

// UnmarshalBinary deserializes JSON bytes into a CheckPoint struct.
func (c *CheckPoint) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, c)
}

// scheduleInterestAggregateTableV2Worker initializes and schedules the worker to populate the interest aggregate table V2.
// It sets up worker configurations and then starts the processing job.
func scheduleInterestAggregateTableV2Worker(ctx context.Context, appCfg *config.AppConfig, workerObj *PopulateInterestAggregateV2WorkerObj) {
	workerConfig := appCfg.PopulateInterestAggregateV2OneTimerConfig
	workerObj.LockDuration = time.Duration(workerConfig.LockDurationInMinutes) * time.Minute
	workerObj.BatchSize = workerConfig.BatchSize
	workerObj.RetentionDay = appCfg.InterestAggregationConfig.RetentionDay
	workerObj.CheckPointKey = workerConfig.CheckPoint
	slog.FromContext(ctx).Info(populateInterestAggV2LogTag, fmt.Sprintf("starting scheduleInterestAggregateTableV2Worker job %+v", workerConfig))
	populateInterestAggregateTableV2Job(ctx, workerObj)
}

// populateInterestAggregateTableV2Job is the main function that executes the worker's logic.
// It handles locking, checkpointing, data fetching, processing, and writing.
var populateInterestAggregateTableV2Job = func(ctx context.Context, w *PopulateInterestAggregateV2WorkerObj) {

	// Load the Asia/Singapore location for consistent time handling.
	location, err := time.LoadLocation("Asia/Singapore")
	if err != nil {
		slog.FromContext(ctx).Warn(populateInterestAggV2LogTag, fmt.Sprintf("Failed to set location: %s", err.Error()))
		return
	}
	w.day = time.Now().In(location) // Set the current day in the specified location
	w.Location = location           // Store the location in the worker object
	w.ctx = ctx                     // Store the context in the worker object

	// Attempt to acquire a Redis distributed lock. If unsuccessful, exit.
	lockAcquired := startCond(w)
	if !lockAcquired {
		return
	}
	slog.FromContext(w.ctx).Info(populateInterestAggV2LogTag, "start processing")
	// Ensure the lock is released when the function exits.
	defer func() {
		cleanup(w)
	}()

	// Get the worker's checkpoint from Redis. If an error occurs (other than no data), exit.
	err = getCheckpoint(w)
	if err != nil {
		slog.FromContext(ctx).Warn(populateInterestAggV2LogTag, fmt.Sprintf("Get Checkpoint fail: %s", err.Error()))
		return
	}

	// Calculate the cutoff day for retaining recent daily data.
	saveDayUpTo := helper.GetDay(time.Now().AddDate(0, 0, -w.RetentionDay), w.Location)
	// Define the end time for current month's transaction data (last second of today).
	lastSecOfToday := time.Date(w.day.Year(), w.day.Month(), w.day.Day(), 23, 59, 59, 59, w.Location)
	// Define the start time for current month's transaction data (first second of this month).
	fistSecOfThisMonth := time.Date(w.day.Year(), w.day.Month(), 1, 0, 0, 0, 0, w.Location)

	// Set exclusive end and start times for transaction data queries.
	endTime := lastSecOfToday.Add(time.Nanosecond)
	startTime := fistSecOfThisMonth
	startTime = startTime.Add(time.Nanosecond)

	// Main processing loop: continues until the entire process is completed (as per checkpoint).
	for !w.checkpoint.Completed {
		// Fetch a batch of InterestAggregate records from the database.
		records, fetchErr := fetchRecords(w)
		if fetchErr != nil && !errors.Is(fetchErr, data.ErrNoData) {
			slog.FromContext(w.ctx).Error(populateInterestAggV2LogTag, "error fetching customers", slog.Error(fetchErr))
			return
		}

		// If records are fetched, proceed with processing.
		if len(records) > 0 {
			txnStartTime := time.Now()
			// Get relevant interest payout transaction data for the current month for the fetched records.
			txnData, txnErr := getInterestPayoutDataForCurrentMonth(w, records, startTime, endTime)
			if txnErr != nil && !errors.Is(txnErr, data.ErrNoData) {
				slog.FromContext(ctx).Error(populateInterestAggV2LogTag, fmt.Sprintf("error getInterestPayoutDataForCurrentMonth %v", time.Since(txnStartTime)), slog.Error(txnErr))
				return
			}
			slog.FromContext(ctx).Info(populateInterestAggV2LogTag, fmt.Sprintf("pass getInterestPayoutDataForCurrentMonth %v", time.Since(txnStartTime)))

			// Write/save the processed data to the InterestAggregateV2 table.
			writeErr := writeRecords(w, records, txnData, saveDayUpTo)
			if writeErr != nil {
				slog.FromContext(ctx).Error(populateInterestAggV2LogTag, "error writeRecords", slog.Error(writeErr))
				return
			}
		}
		// Update the checkpoint in Redis with the progress.
		updateCheckPtErr := updateCheckpoint(w, records)
		if updateCheckPtErr != nil {
			slog.FromContext(ctx).Error(populateInterestAggV2LogTag, "error updating checkpoint", slog.Error(updateCheckPtErr))
			return
		}
	}
}

// startCond attempts to acquire a Redis distributed lock.
// It returns true if the lock is successfully acquired, false otherwise.
func startCond(w *PopulateInterestAggregateV2WorkerObj) bool {
	var err error
	w.lock, err = w.RedisClient.TryLock(w.ctx, w.CheckPointKey, w.LockDuration)
	if err != nil {
		// Log error if it's not due to the lock being occupied.
		if err != redis.ErrLockOccupied {
			slog.FromContext(w.ctx).Error(populateInterestAggV2LogTag, "error trying to obtain redis lock", slog.Error(err))
		}
		return false
	}
	return true
}

// cleanup releases the Redis distributed lock.
func cleanup(w *PopulateInterestAggregateV2WorkerObj) {
	err := w.lock.Unlock(w.ctx)
	if err != nil {
		slog.FromContext(w.ctx).Error(populateInterestAggV2LogTag, "failed to release lock", slog.Error(err))
	}
}

// getCheckpoint retrieves the worker's progress checkpoint from Redis.
// If no checkpoint exists (first run), it initializes a new one.
func getCheckpoint(w *PopulateInterestAggregateV2WorkerObj) error {
	checkpoint := CheckPoint{}
	err := w.RedisClient.GetObject(w.ctx, w.CheckPointKey, &checkpoint)
	if err != nil {
		// If no data exists, initialize a new checkpoint with the current time.
		if errors.Is(err, redis.ErrNoData) {
			w.checkpoint = CheckPoint{
				CurrentProcessingTime: time.Now(),
			}
			return nil
		}
		// Log and return error if unmarshalling fails.
		slog.FromContext(w.ctx).Error(populateInterestAggV2LogTag, "error unmarshalling checkpoint", slog.Error(err))
		return err
	}

	w.checkpoint = checkpoint // Set the worker's checkpoint to the retrieved one.
	return nil
}

// fetchRecords fetches a batch of InterestAggregate records from the database.
// It queries records with IDs greater than the last processed ID from the checkpoint, ordered ascending.
func fetchRecords(w *PopulateInterestAggregateV2WorkerObj) ([]*storage.InterestAggregate, error) {
	conditions := []data.Condition{
		data.GreaterThan("ID", w.checkpoint.LastProcessedID), // Start from the last processed ID
		data.AscendingOrder("ID"),                            // Order by ID ascending
		data.Limit(w.BatchSize),                              // Limit to the configured batch size
	}
	return storage.InterestAggregateD.Find(w.ctx, conditions...)
}

// writeRecords transforms and saves the processed data into the InterestAggregateV2 table.
// It takes original InterestAggregate records and associated TransactionsData.
func writeRecords(w *PopulateInterestAggregateV2WorkerObj, records []*storage.InterestAggregate, txnData []*storage.TransactionsData, saveDayUpTo string) error {
	recordsV2 := make([]*storage.InterestAggregateV2, len(records)) // Slice to hold new InterestAggregateV2 records
	// Create a map to easily access transaction data by AccountID and AccountAddress.
	txnMap := make(map[string]map[string][]*storage.TransactionsData)
	currentMth := helper.GetMonth(w.day, w.Location) // Get the current month key

	// Populate the txnMap for quick lookup.
	for _, txn := range txnData {
		if _, ok := txnMap[txn.AccountID]; !ok {
			txnMap[txn.AccountID] = make(map[string][]*storage.TransactionsData)
		}
		if _, ok := txnMap[txn.AccountID][txn.AccountAddress]; !ok {
			txnMap[txn.AccountID][txn.AccountAddress] = make([]*storage.TransactionsData, 0)
		}
		txnMap[txn.AccountID][txn.AccountAddress] = append(txnMap[txn.AccountID][txn.AccountAddress], txn)
	}

	// Transform each InterestAggregate record into an InterestAggregateV2 record.
	for i, r := range records {
		// Construct recent day data and current month aggregate data from transactions.
		recentDayData, currentMonthData := constructData(w.ctx, txnMap[r.AccountID][r.AccountAddress], saveDayUpTo, w.Location)

		// Create monthly history map for the current month.
		monthlyHistory := make(map[string]map[string]int64)
		for txType, amt := range currentMonthData {
			monthlyHistory[txType] = map[string]int64{currentMth: amt}
		}

		// Populate the InterestAggregateV2 object.
		recordsV2[i] = &storage.InterestAggregateV2{
			AccountID:      r.AccountID,
			AccountAddress: r.AccountAddress,
			// TotalInterestEarned is migrated, potentially changing from a single value to a map.
			TotalInterestEarned: storage.InterestEarned{constants.InterestPayoutTransactionType: r.TotalInterestEarned},
			MonthlyHistory:      monthlyHistory, // Aggregated monthly data
			RecentDay:           recentDayData,  // Aggregated recent day data
			Currency:            r.Currency,
		}
	}
	// Save the batch of new InterestAggregateV2 records to the database.
	return storage.InterestAggregateV2D.SaveBatch(w.ctx, recordsV2)
}

// constructData processes a list of transaction data to create recent daily aggregates and current month aggregates.
// It returns a collection of recent daily data and a map of current month data aggregated by transaction type.
func constructData(ctx context.Context, lst []*storage.TransactionsData, saveDayUpTo string, location *time.Location) (storage.RecentDataCollection, map[string]int64) {
	recentData := make(map[string]map[string]storage.RecentDayData) // Map for recent daily data (TransactionType -> DayKey -> RecentDayData)
	currentMonthData := make(map[string]int64)                      // Map for current month total by transaction type

	for _, data := range lst {
		amt := utils.GetAmountInCents(ctx, data.TransactionAmount) // Get amount in cents
		currentMonthData[data.TransactionType] += amt              // Aggregate for current month total

		dayKey := helper.GetDay(data.BatchValueTimestamp, location) // Get the day key for the transaction
		// If the transaction's day is within the retention period, add it to recentData.
		if dayKey >= saveDayUpTo {
			if _, ok := recentData[data.TransactionType]; !ok {
				recentData[data.TransactionType] = make(map[string]storage.RecentDayData)
			}
			// Store the amount and timestamp for the specific day and transaction type.
			recentData[data.TransactionType][dayKey] = storage.RecentDayData{Value: amt, Time: data.BatchValueTimestamp.UnixNano()}
		}
	}
	return recentData, currentMonthData
}

// updateCheckpoint updates the worker's progress checkpoint in Redis.
// It marks completion if no more records are found or updates the last processed ID.
func updateCheckpoint(w *PopulateInterestAggregateV2WorkerObj, interestData []*storage.InterestAggregate) error {
	if len(interestData) == 0 {
		// If no records were processed in this batch, it means all data is processed.
		w.checkpoint.Completed = true
		slog.FromContext(w.ctx).Info(populateInterestAggV2LogTag, "completed processing")
	} else {
		// Otherwise, update the last processed ID and mark as not completed.
		w.checkpoint.Completed = false
		lastRecordID := interestData[len(interestData)-1].ID

		// Check for integer overflow before converting uint64 to int.
		if lastRecordID > uint64(math.MaxInt) {
			slog.FromContext(w.ctx).Error(populateInterestAggV2LogTag, "integer overflow detected during conversion")
			return fmt.Errorf("integer overflow: lastRecordID %d exceeds MaxInt %d", lastRecordID, math.MaxInt)
		}

		w.checkpoint.LastProcessedID = int(lastRecordID) // Update the last processed ID
		slog.FromContext(w.ctx).Info(populateInterestAggV2LogTag, fmt.Sprintf("processed last id %d", lastRecordID))
	}
	// Save the updated checkpoint object to Redis with a 168-hour (7-day) expiry.
	_, err := w.RedisClient.SetObject(w.ctx, w.CheckPointKey, &w.checkpoint, 168*time.Hour)
	return err
}

// getInterestPayoutDataForCurrentMonth fetches relevant transaction data for the current month
// for a given set of InterestAggregate records (specifically for AccountID and AccountAddress).
func getInterestPayoutDataForCurrentMonth(w *PopulateInterestAggregateV2WorkerObj, records []*storage.InterestAggregate, startTime, endTime time.Time) ([]*storage.TransactionsData, error) {
	// Build a list of OR conditions for AccountID and AccountAddress pairs from the current batch of records.
	acctConditions := make([]data.Condition, len(records))
	for i, record := range records {
		acctConditions[i] = data.And(
			data.EqualTo("AccountID", record.AccountID),
			data.EqualTo("AccountAddress", record.AccountAddress),
		)
	}

	// Combine all conditions for fetching transaction data.
	conds := []data.Condition{
		data.Or(acctConditions[0], acctConditions[1:]...),  // OR conditions for all account pairs
		data.GreaterThan("BatchValueTimestamp", startTime), // Transactions after startTime
		data.LessThan("BatchValueTimestamp", endTime),      // Transactions before endTime
		data.Or( // Only specific transaction types
			data.EqualTo("TransactionType", constants.InterestPayoutTransactionType),
			data.EqualTo("TransactionType", constants.InterestPayoutReversalTransactionType),
			data.EqualTo("TransactionType", constants.BonusInterestPayoutTransactionType),
			data.EqualTo("TransactionType", constants.BonusInterestPayoutReversalTransactionType),
		),
		data.EqualTo("BatchStatus", constants.BatchStatusAccepted), // Only accepted transactions
	}
	// Fetch transaction data from the slave database.
	return storage.TransactionsDataD.FindOnSlave(w.ctx, conds...)
}
