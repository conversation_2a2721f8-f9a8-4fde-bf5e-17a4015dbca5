package constants

// nolint: dupl
const (

	// GetAccountCalendarActivityLogTag handler log tag ...
	GetAccountCalendarActivityLogTag = "GetAccountCalendarActivityLog"
	// GetAccountTransactionsSearchLogTag handler log tag...
	GetAccountTransactionsSearchLogTag = "GetAccountTransactionsSearchLog"
	// GetTransactionDetailHandlerLogTag handler log tag...
	GetTransactionDetailHandlerLogTag = "getTransactionDetailHandlerLog"
	// GetCASATransactionsSummaryHandlerLogTag handler log tag...
	GetCASATransactionsSummaryHandlerLogTag = "getCASATransactionsSummaryHandlerLog"
	// GetAllTransactionsLogicLogTag logic log tag
	GetAllTransactionsLogicLogTag = "getAllTransactionsLog"
	// GetAccountCalendarActivitiesLogicLogTag logic log tag
	GetAccountCalendarActivitiesLogicLogTag = "getAccountCalendarActivitiesLog"
	// GetTransactionDetailLogTag ...
	GetTransactionDetailLogTag = "getTransactionDetailLog"
	// GetPocketInterestEarnedHandlerLogTag handler log tag...
	GetPocketInterestEarnedHandlerLogTag = "getPocketInterestEarnedHandlerLog"
	// GetPocketActivitiesHandlerLogTag handler log tag...
	GetPocketActivitiesHandlerLogTag = "getPocketActivitiesHandlerLog"
	// GetPocketActivityDetailHandlerLogTag handler log tag...
	GetPocketActivityDetailHandlerLogTag = "getPocketActivityDetailHandlerLog"
	// GetCASAInterestEarnedHandlerLogTag handler log tag...
	GetCASAInterestEarnedHandlerLogTag = "getCASAInterestEarnedHandlerLog"
	// GetSTM453TransactionInfoLogTag handler log tag...
	GetSTM453TransactionInfoLogTag = "getSTM453TransactionInfoLogTag"
	// GetInternalTransactionDetailLogTag handler log tag...
	GetInternalTransactionDetailLogTag = "getInternalTransactionDetailLogTag"
	// GetOpsSearchLogTag handler log tag...
	GetOpsSearchLogTag = "getOpsSearchLogTag"
	// GetOpsSearchTransactionDataFromDBLogTag handler log tag...
	GetOpsSearchTransactionDataFromDBLogTag = "getOpsSearchTransactionDataFromDBLogTag"
	// RequestValidationFailureEvent request validation log tag...
	RequestValidationFailureEvent = "requestValidationFailureLogTag"
	// GetAccountTransactionsSearchForCXLogTag handler log tag...
	GetAccountTransactionsSearchForCXLogTag = "GetAccountTransactionsSearchForCXLogTag"

	// DepositsBalanceStreamLogTag deposits balance kafka stream log tag
	DepositsBalanceStreamLogTag = "depositsBalanceStream"
	// DepositsCoreStreamLogTag deposits core kafka stream log tag
	DepositsCoreStreamLogTag = "depositsCoreStream"

	// ReceiverTxEntryLogTag payment engine kafka stream related log tag
	ReceiverTxEntryLogTag = "receiverTxEntryLog"
	// SenderTxEntryLogTag payment engine kafka stream related log tag
	SenderTxEntryLogTag = "senderTxEntryLog"
	// PaymentsInterbankTxTag payment engine kafka stream related log tag
	PaymentsInterbankTxTag = "HandlePaymentsInterbankTxTag"
	// PaymentMooMooTxTag ...
	PaymentMooMooTxTag = "HandlePaymentMooMooTxTag"
	// PaymentRentasTxTag ...
	PaymentRentasTxTag = "HandlePaymentRentasTxTag"
	// PaymentsIntrabankTxTag payment engine kafka stream related log tag
	PaymentsIntrabankTxTag = "HandlePaymentsIntrabankTx"
	// PaymentsGrabTxTag payment engine kafka stream related log tag
	PaymentsGrabTxTag = "HandlePaymentsGrabTx"

	// DepositsBalanceTag consumer log tag
	DepositsBalanceTag = "deposits-balance-streams"
	// DepositsCoreTag consumer log tag
	DepositsCoreTag = "deposits-core-streams"
	// PaymentEngineTag consumer log tag
	PaymentEngineTag = "payment-engine-streams"

	// CheckAuthorizationLogTag ...
	CheckAuthorizationLogTag = "CheckAuthorizationLog"
	// LookUpCifTag ...
	LookUpCifTag = "LookUpCifFromCustomerMasterLogTag"
	// LookUpCustomerIdentityLogTag ...
	LookUpCustomerIdentityLogTag = "LookUpCustomerIdentityLogTag"
	// CheckPermissionTag ...
	CheckPermissionTag = "CheckPermissionForAccountServiceLogTag"
	// CheckPermissionAndGetParentTag ...
	CheckPermissionAndGetParentTag = "CheckPermissionAndGetParentLogTag"
	// GetAccountFromAccountServiceTag ...
	GetAccountFromAccountServiceTag = "GetAccountFromAccountServiceLogTag"

	// CalendarActivityLogTag ...
	CalendarActivityLogTag = "calendarActivityLogTag"
	// BulkUpsertLogTag ...
	BulkUpsertLogTag = "BulkUpsertEvent"
	// InterestAggregateLogTag ...
	InterestAggregateLogTag = "InterestAggregateLog"
	// AmountConversionLogTag ...
	AmountConversionLogTag = "AmountConversionLog"

	// TransactionConnectionLogTag ...
	TransactionConnectionLogTag = "db.transactionConnection"
	// SchedulerLockLogTag ...
	SchedulerLockLogTag = "db.schedulerLock"

	// UpdateInterestAggregateDBForDailyInterestPayout ...
	UpdateInterestAggregateDBForDailyInterestPayout = "worker.updateInterestAggregateDBForDailyInterestPayoutWorker"

	// UpdateInterestAggregateDBLogTag ...
	UpdateInterestAggregateDBLogTag = "updateInterestAggregateDBLogTag"

	//PopulateInterestAggregateOneTimerLogTag ...
	PopulateInterestAggregateOneTimerLogTag = "populateInterestAggregateOneTimerLogTag"

	// InterestAggStreamLogTag deposits core kafka stream for interest aggregation log tag
	InterestAggStreamLogTag = "InterestAggStreamLogTag"

	// GetAccountImageDetailsLogTag ...
	GetAccountImageDetailsLogTag = "getAccountImageDetailsLog"

	//SnowflakeTransactionHistoryLogTag ...
	SnowflakeTransactionHistoryLogTag = "SnowflakeTransactionHistoryLogTag"

	// DigicardTxStreamLogTag ...
	DigicardTxStreamLogTag = "DigicardTxStreamLogTag"

	// InitLogTag ...
	InitLogTag = "InitLogTag"

	// LoanCoreTxStreamLogTag is loan core transaction kafka stream log tag
	LoanCoreTxStreamLogTag = "LoanCoreTxStreamLogTag"

	// LoanExpTxStreamLogTag is loan exp transaction kafka stream log tag
	LoanExpTxStreamLogTag = "LoanExpTxStreamLogTag"

	// GetLendingTransactionDetailLogTag handler log tag
	GetLendingTransactionDetailLogTag = "getLendingTransactionDetailLogTag"

	// GetLendingTransactionDetailDurationLogTag
	GetLendingTransactionDetailDurationLogTag = "getLendingTransactionDetailDurationLogTag"

	//GetLendingTransactionSearchLogTag handler log tag
	GetLendingTransactionSearchLogTag = "getLendingTransactionSearchLog"

	// GetProxyDetailLogTag handler log tag...
	GetProxyDetailLogTag = "getProxyDetailLogTag"

	//GetActiveProfileIDLogTag
	GetActiveProfileIDLogTag = "getActiveProfileIDLog"
)

type tagKey string

const (
	// WorkerIDTagKey ...
	WorkerIDTagKey tagKey = "worker_id"

	// JobBatchIDTagKey ...
	JobBatchIDTagKey tagKey = "job_batch_id"

	// JobIDTagKey ...
	JobIDTagKey tagKey = "job_id"
)
