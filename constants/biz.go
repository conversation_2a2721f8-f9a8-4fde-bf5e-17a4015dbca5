package constants

const (
	// BizDepositsDomain ...
	BizDepositsDomain = "BIZ_DEPOSITS"
	// BizDepositAccount ...
	BizDepositAccount = "BIZ_DEPOSIT_ACCOUNT"
	// BizDepositsAccountIDPrefix ...
	BizDepositsAccountIDPrefix = "8188"
)

const (
	// BizFundInScenarioKey ...
	BizFundInScenarioKey = BizDepositsDomain + "." + FundIn
	// BizFundInReversalScenarioKey ...
	BizFundInReversalScenarioKey = BizDepositsDomain + "." + FundInRevTxType
	// BizReceiveMoneyScenarioKey ...
	BizReceiveMoneyScenarioKey = BizDepositsDomain + "." + ReceiveMoneyTxType
	// BizReceiveMoneyReversalScenarioKey ...
	BizReceiveMoneyReversalScenarioKey = BizDepositsDomain + "." + ReceiveMoneyRevTxType
	// BizSendMoneyScenarioKey ...
	BizSendMoneyScenarioKey = BizDepositsDomain + "." + SendMoneyTxType
	// BizSendMoneyReversalScenarioKey ...
	BizSendMoneyReversalScenarioKey = BizDepositsDomain + "." + SendMoneyRevTxType
	// BizTransferMoneyDebitScenarioKey ...
	BizTransferMoneyDebitScenarioKey = BizDepositsDomain + "." + TransferMoneyTxType + "." + DEBIT
	// BizTransferMoneyCreditScenarioKey ...
	BizTransferMoneyCreditScenarioKey = BizDepositsDomain + "." + TransferMoneyTxType + "." + CREDIT
	// BizTransferMoneyReversalDebitScenarioKey ...
	BizTransferMoneyReversalDebitScenarioKey = BizDepositsDomain + "." + TransferMoneyRevTxType + "." + DEBIT
	// BizTransferMoneyReversalCreditScenarioKey ...
	BizTransferMoneyReversalCreditScenarioKey = BizDepositsDomain + "." + TransferMoneyRevTxType + "." + CREDIT
	// BizInterestPayoutScenarioKey ...
	BizInterestPayoutScenarioKey = BizDepositsDomain + "." + InterestPayoutTransactionType
	// BizApplyEarmarkScenarioKey ...
	BizApplyEarmarkScenarioKey = BizDepositsDomain + "." + ApplyEarmarkTransactionType
	// BizReleaseEarmarkScenarioKey ...
	BizReleaseEarmarkScenarioKey = BizDepositsDomain + "." + ReleaseEarmarkTransactionType
	// BizLendingDrawdownScenarioKey ...
	BizLendingDrawdownScenarioKey = BizLendingDomain + "." + DrawdownTransactionType
	// BizLendingRepaymentScenarioKey ...
	BizLendingRepaymentScenarioKey = BizLendingDomain + "." + RepaymentTransactionType
	// BizLendingWriteOffScenarioKey ...
	BizLendingWriteOffScenarioKey = BizLendingDomain + "." + WriteOffTransactionType
)
