package constants

const (
	// DebitCard is a transaction domain for all the card transactions
	DebitCard = "DEBIT_CARD"
)

const (
	// MCN mastercard scheme network id
	MCN = "MCN"
	// MDT Paynet mydebit scheme network id
	MDT = "MDT"
	// SAN Paynet SAN scheme network id
	SAN = "SAN"
)

// CurrencyConvType currency conversion type
type CurrencyConvType string

const (
	// LOCAL type
	LOCAL CurrencyConvType = "LOCAL"
	// FX type
	FX CurrencyConvType = "FX"
	// DCC type
	DCC CurrencyConvType = "DCC"
)

// const for country code
const (
	// MYS country code for Malaysia
	MYS = "MYS"
)

const (
	// CardStatusCanceled ...
	CardStatusCanceled = "CANCELED"
)

const (
	// CardTransferTypeRefund ...
	CardTransferTypeRefund = "REFUND"
)

const (
	// SpendRefundTransactionType ...
	SpendRefundTransactionType = "SPEND_CARD_REFUND"

	// ATMCashWithdrawalRefundTransactionType ...
	ATMCashWithdrawalRefundTransactionType = "SPEND_CARD_ATM_REFUND"

	// MoneySendTransactionType ...
	MoneySendTransactionType = "RECEIVE_MONEY"

	// SpendCardPresentTransactionType ...
	SpendCardPresentTransactionType = "SPEND_CARD_PRESENT"

	// SpendCardNotPresentTransactionType ...
	SpendCardNotPresentTransactionType = "SPEND_CARD_NOT_PRESENT"

	// SpendCardForceTransactionType ...
	SpendCardForceTransactionType = "SPEND_CARD_FORCE"

	// SpendCardAtmTransactionType ...
	SpendCardAtmTransactionType = "SPEND_CARD_ATM"

	// DomesticAtmFeeWaiverTransactionType DOMESTIC_ATM_FEE_WAIVER
	DomesticAtmFeeWaiverTransactionType = "DOMESTIC_ATM_FEE_WAIVER"

	// DomesticAtmFeeTransactionType ...
	DomesticAtmFeeTransactionType = "DOMESTIC_ATM_FEE"
	// NewCardIssuanceFeeTransactionType ...
	NewCardIssuanceFeeTransactionType = "NEW_CARD_ISSUANCE_FEE"
	// NewCardIssuanceFeeWaiverTransactionType ...
	NewCardIssuanceFeeWaiverTransactionType = "NEW_CARD_ISSUANCE_FEE_WAIVER"
	// CardAnnualFeeTransactionType ...
	CardAnnualFeeTransactionType = "CARD_ANNUAL_FEE"
	// CardAnnualFeeWaiverTransactionType ...
	CardAnnualFeeWaiverTransactionType = "CARD_ANNUAL_FEE_WAIVER"
	// CardReplacementFeeTransactionType ...
	CardReplacementFeeTransactionType = "CARD_REPLACEMENT_FEE"
	// CardReplacementFeeWaiverTransactionType ...
	CardReplacementFeeWaiverTransactionType = "CARD_REPLACEMENT_FEE_WAIVER"

	// MastercardTransactionSubtype ...
	MastercardTransactionSubtype = "MASTERCARD"

	// PaynetMydebitTransactionSubtype ...
	PaynetMydebitTransactionSubtype = "PAYNET_MYDEBIT"

	// PaynetSanTransactionSubtype ...
	PaynetSanTransactionSubtype = "PAYNET_SAN"

	// DebitCardDomain ...
	DebitCardDomain = "DEBIT_CARD"

	// TransferTypeCharge ... used in fetching card transaction history
	TransferTypeCharge = "CHARGE"

	// SpendCardATMReversalTransactionType ...
	SpendCardATMReversalTransactionType = "SPEND_CARD_ATM_REVERSAL"

	// SpendCardPresentReversalTransactionType ...
	SpendCardPresentReversalTransactionType = "SPEND_CARD_PRESENT_REVERSAL"

	// SpendCardNotPresentReversalTransactionType ...
	SpendCardNotPresentReversalTransactionType = "SPEND_CARD_NOT_PRESENT_REVERSAL"
	// CardBankInitiatedTransactionSubtype ...
	CardBankInitiatedTransactionSubtype = "BANK_INITIATED"
)

// CardOps txn
const (
	// ManualDebitReconTransactionType ...
	ManualDebitReconTransactionType = "MANUAL_DEBIT_RECON"
	// ManualCreditReconTransactionType ...
	ManualCreditReconTransactionType = "MANUAL_CREDIT_RECON"
	// DisputeChargeBackTransactionType ...
	DisputeChargeBackTransactionType = "DISPUTE_CHARGEBACK"
	// DisputeBankRefundTransactionType ...
	DisputeBankRefundTransactionType = "DISPUTE_BANK_REFUND"
	// DisputeBankFraudRefundTransactionType ...
	DisputeBankFraudRefundTransactionType = "DISPUTE_BANK_FRAUD_REFUND"
	// ProvisionalCreditTransactionType ...
	ProvisionalCreditTransactionType = "PROVISIONAL_CREDIT"
	// ProvisionalCreditReversalTransactionType ...
	ProvisionalCreditReversalTransactionType = "PROVISIONAL_CREDIT_REVERSAL"
	// OperationalLossTransactionType ...
	OperationalLossTransactionType = "OPERATIONAL_LOSS"
)

const (
	// SpendCardATMReversalTypeCreditScenarioKey ...
	SpendCardATMReversalTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardATMReversalTransactionType + "." + CREDIT
	// SpendCardPresentReversalTypeCreditScenarioKey ...
	SpendCardPresentReversalTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardPresentReversalTransactionType + "." + CREDIT
	// SpendCardNotPresentReversalTypeCreditScenarioKey ...
	SpendCardNotPresentReversalTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardNotPresentReversalTransactionType + "." + CREDIT
	// SpendCardPresentTransactionTypeDebitScenarioKey ...
	SpendCardPresentTransactionTypeDebitScenarioKey = DebitCardDomain + "." + SpendCardPresentTransactionType + "." + DEBIT
	// SpendCardNotPresentTransactionTypeDebitScenarioKey ...
	SpendCardNotPresentTransactionTypeDebitScenarioKey = DebitCardDomain + "." + SpendCardNotPresentTransactionType + "." + DEBIT
	// SpendCardForceTransactionTypeDebitScenarioKey ...
	SpendCardForceTransactionTypeDebitScenarioKey = DebitCardDomain + "." + SpendCardForceTransactionType + "." + DEBIT
	// SpendCardAtmTransactionTypeDebitScenarioKey ...
	SpendCardAtmTransactionTypeDebitScenarioKey = DebitCardDomain + "." + SpendCardAtmTransactionType + "." + DEBIT
	// DomesticAtmFeeTransactionTypeDebitScenarioKey ...
	DomesticAtmFeeTransactionTypeDebitScenarioKey = DebitCardDomain + "." + DomesticAtmFeeTransactionType + "." + DEBIT

	// SpendCardPresentTransactionTypeCreditScenarioKey ...
	SpendCardPresentTransactionTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardPresentTransactionType + "." + CREDIT
	// SpendCardNotPresentTransactionTypeCreditScenarioKey ...
	SpendCardNotPresentTransactionTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardNotPresentTransactionType + "." + CREDIT
	// SpendCardForceTransactionTypeCreditScenarioKey ...
	SpendCardForceTransactionTypeCreditScenarioKey = DebitCardDomain + "." + SpendCardForceTransactionType + "." + CREDIT
	// ATMCashWithdrawalRefundTransactionTypeCreditScenarioKey ...
	ATMCashWithdrawalRefundTransactionTypeCreditScenarioKey = DebitCardDomain + "." + ATMCashWithdrawalRefundTransactionType + "." + CREDIT
	// DomesticAtmFeeWaiverTransactionTypeCreditScenarioKey ...
	DomesticAtmFeeWaiverTransactionTypeCreditScenarioKey = DebitCardDomain + "." + DomesticAtmFeeWaiverTransactionType + "." + CREDIT
	// NewCardIssuanceFeeTransactionTypeDebitScenarioKey ...
	NewCardIssuanceFeeTransactionTypeDebitScenarioKey = DebitCardDomain + "." + NewCardIssuanceFeeTransactionType + "." + DEBIT
	// NewCardIssuanceFeeWaiverTransactionTypeCreditScenarioKey ...
	NewCardIssuanceFeeWaiverTransactionTypeCreditScenarioKey = DebitCardDomain + "." + NewCardIssuanceFeeWaiverTransactionType + "." + CREDIT
	// CardAnnualFeeTransactionTypeDebitScenarioKey ...
	CardAnnualFeeTransactionTypeDebitScenarioKey = DebitCardDomain + "." + CardAnnualFeeTransactionType + "." + DEBIT
	// CardAnnualFeeWaiverTransactionTypeCreditScenarioKey ...
	CardAnnualFeeWaiverTransactionTypeCreditScenarioKey = DebitCardDomain + "." + CardAnnualFeeWaiverTransactionType + "." + CREDIT
	// CardReplacementFeeTransactionTypeDebitScenarioKey ...
	CardReplacementFeeTransactionTypeDebitScenarioKey = DebitCardDomain + "." + CardReplacementFeeTransactionType + "." + DEBIT
	// CardReplacementFeeWaiverTransactionTypeCreditScenarioKey ...
	CardReplacementFeeWaiverTransactionTypeCreditScenarioKey = DebitCardDomain + "." + CardReplacementFeeWaiverTransactionType + "." + CREDIT
	// MoneySendTransactionTypeCreditScenarioKey ...
	MoneySendTransactionTypeCreditScenarioKey = DebitCardDomain + "." + MoneySendTransactionType + "." + CREDIT
	// SpendRefundTransactionTypeCreditScenarioKey ...
	SpendRefundTransactionTypeCreditScenarioKey = DebitCardDomain + "." + SpendRefundTransactionType + "." + CREDIT

	// ManualDebitReconTransactionTypeScenarioKey ...
	ManualDebitReconTransactionTypeScenarioKey = DebitCardDomain + "." + ManualDebitReconTransactionType
	// ManualCreditReconTransactionTypeScenarioKey ...
	ManualCreditReconTransactionTypeScenarioKey = DebitCardDomain + "." + ManualCreditReconTransactionType
	// DisputeChargeBackTransactionTypeScenarioKey ...
	DisputeChargeBackTransactionTypeScenarioKey = DebitCardDomain + "." + DisputeChargeBackTransactionType
	// DisputeBankRefundTransactionTypeScenarioKey ...
	DisputeBankRefundTransactionTypeScenarioKey = DebitCardDomain + "." + DisputeBankRefundTransactionType
	// DisputeBankFraudRefundTransactionTypeScenarioKey ...
	DisputeBankFraudRefundTransactionTypeScenarioKey = DebitCardDomain + "." + DisputeBankFraudRefundTransactionType
	// ProvisionalCreditTransactionTypeScenarioKey ...
	ProvisionalCreditTransactionTypeScenarioKey = DebitCardDomain + "." + ProvisionalCreditTransactionType
	// ProvisionalCreditReversalTransactionTypeScenarioKey ...
	ProvisionalCreditReversalTransactionTypeScenarioKey = DebitCardDomain + "." + ProvisionalCreditReversalTransactionType
	// OperationalLossTransactionTypeScenarioKey ...
	OperationalLossTransactionTypeScenarioKey = DebitCardDomain + "." + OperationalLossTransactionType
	// CardBankInitiatedNotCompletedScenarioKey means the payment is yet to or unable complete the happy flow.
	CardBankInitiatedNotCompletedScenarioKey = "BANK_INITIATED_NOT_COMPLETED"
)

var (
	// CreditFundToCardTransactionTypes is the list of card transaction types where the fund will be sent to user account
	CreditFundToCardTransactionTypes = []string{SpendRefundTransactionType, ATMCashWithdrawalRefundTransactionType, MoneySendTransactionType, DomesticAtmFeeWaiverTransactionType}
	// ATMFeeTransactionTypes ...
	ATMFeeTransactionTypes = map[string]struct{}{
		DomesticAtmFeeTransactionType:          {},
		DomesticAtmFeeWaiverTransactionType:    {},
		ATMCashWithdrawalRefundTransactionType: {},
	}
	// AtmFeeTransactionTypes is the list of atm fee
	AtmFeeTransactionTypes = []string{DomesticAtmFeeTransactionType, DomesticAtmFeeWaiverTransactionType}

	// CardOpsTransactionTypes is the list of card transaction types where triggered by ops team
	CardOpsTransactionTypes = []string{ManualDebitReconTransactionType, ManualCreditReconTransactionType, DisputeChargeBackTransactionType, DisputeBankRefundTransactionType, DisputeBankFraudRefundTransactionType, ProvisionalCreditTransactionType, ProvisionalCreditReversalTransactionType, OperationalLossTransactionType}

	// CardMaintenanceFeeTypes is the list of card maintenance fee transaction types
	CardMaintenanceFeeTypes = []string{NewCardIssuanceFeeTransactionType, NewCardIssuanceFeeWaiverTransactionType, CardAnnualFeeTransactionType, CardAnnualFeeWaiverTransactionType, CardReplacementFeeTransactionType, CardReplacementFeeWaiverTransactionType}
)
