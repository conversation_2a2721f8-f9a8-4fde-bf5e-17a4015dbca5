package constants

const (
	// DomainDeposits ...
	DomainDeposits = "DEPOSITS"
)

// constants denoting Transaction-Type
const (
	// RewardPayout ...
	RewardPayout = "REWARD_PAYOUT"

	// RewardPayoutReversal ...
	RewardPayoutReversal = "REWARD_PAYOUT_REVERSAL"

	// RewardsCashback ...
	RewardsCashback = "REWARDS_CASHBACK"

	// RewardsCashbackReversal ...
	RewardsCashbackReversal = "REWARDS_CASHBACK_REVERSAL"
)

// constants denoting Transaction Type Scenario Key
const (
	// RewardsCashbackTransactionTypeScenarioKey ...
	RewardsCashbackTransactionTypeScenarioKey = DebitCardDomain + "." + RewardsCashback
	// RewardsCashbackReversalTransactionTypeScenarioKey ...
	RewardsCashbackReversalTransactionTypeScenarioKey = DebitCardDomain + "." + RewardsCashbackReversal
)

// constants denoting Transaction-Subtype
const (
	// DebitCardCashbackSubType Transaction Subtype of Debit card cashback
	DebitCardCashbackSubType = "DEBIT_CARD_CASHBACK"

	// InterestRewardBonusSubType Transaction Subtype of Bonus Interest
	InterestRewardBonusSubType = "INTEREST_REWARD_BONUS"
)

const (
	// DebitCardCashbackDisplayName display name for cashback
	DebitCardCashbackDisplayName = "Debit Card Rewards"

	// DebitCardCashbackReversalDisplayName display name for cashback reversal
	DebitCardCashbackReversalDisplayName = "Debit Card Rewards Reversal"

	// InterestRewardBonusDisplayName display name for bonus interest
	InterestRewardBonusDisplayName = "Pocket+ Beta Bonus Interest"

	// InterestRewardBonusReversalDisplayName display name for bonus interest reversal
	InterestRewardBonusReversalDisplayName = "Pocket+ Beta Bonus Interest Reversal"
)

var (
	// RewardsDisplayNameMap maps transaction code to display name
	RewardsDisplayNameMap = map[string]map[string]map[string]string{
		DepositsDomain: {
			RewardPayout: {
				DebitCardCashbackSubType:   DebitCardCashbackDisplayName,
				InterestRewardBonusSubType: InterestRewardBonusDisplayName,
			},
			RewardPayoutReversal: {
				DebitCardCashbackSubType:   DebitCardCashbackReversalDisplayName,
				InterestRewardBonusSubType: InterestRewardBonusReversalDisplayName,
			},
		},
	}
	// RewardsTransactions shows all rewards relevant transactions
	RewardsTransactions = []string{RewardsCashback, RewardsCashbackReversal}
)
