package constants

import transactionCode "gitlab.myteksi.net/dakota/common/transaction-code/deposits/dbmy"

// constants denoting Transaction-Type (not the exhaustive list)
const (
	// SendMoneyTxType transaction type of GXS received from Payment-Engine
	SendMoneyTxType = "SEND_MONEY"

	// ReceiveMoneyTxType  transaction type of GXS received from Payment-Engine
	ReceiveMoneyTxType = "RECEIVE_MONEY"

	// SendMoneyRevTxType  transaction type of GXS received from Payment-Engine
	SendMoneyRevTxType = "SEND_MONEY_REVERSAL"

	// ReceiveMoneyRevTxType  transaction type of Grab/GXS received from Payment-Engine
	ReceiveMoneyRevTxType = "RECEIVE_MONEY_REVERSAL"

	// SpendMoneyTxType transaction type of Grab received from Payment-Engine
	SpendMoneyTxType = "SPEND_MONEY"

	// CashOutTxType transaction type of MooMoo
	CashOutTxType = "CASH_OUT"

	// SpendMoneyRevTxType transaction type of Grab received from Payment-Engine
	SpendMoneyRevTxType = "SPEND_MONEY_REVERSAL"

	// SettlementTxType transaction type of Grab received from Payment-Engine
	SettlementTxType = "SETTLEMENT"

	// SettlementRevTxType transaction type of Grab received from Payment-Engine
	SettlementRevTxType = "SETTLEMENT_REVERSAL"

	//	FundInTxType transaction type of DBMY received from Payment-Engine
	FundInTxType = "FUND_IN"

	//	FundInRevTxType transaction type of DBMY received from Payment-Engine
	FundInRevTxType = "FUND_IN_REVERSAL"

	//	TransferMoneyTxType transaction type of DBMY received from Payment-Engine
	TransferMoneyTxType = "TRANSFER_MONEY"

	//	TransferMoneyRevTxType transaction type of DBMY received from Payment-Engine
	TransferMoneyRevTxType = "TRANSFER_MONEY_REVERSAL"

	// QrPaymentTxType transaction type of DBMY received from Payment-Engine for Qr payment
	QrPaymentTxType = transactionCode.TxTypePayment

	// QrPaymentReversalTxType transaction type of DBMY received from Payment-Engine for Qr payment reversal
	QrPaymentReversalTxType = transactionCode.TxTypePaymentReversal
)

// constants denoting Transaction-Subtype (not the exhaustive list)
const (
	// IntraBank Transaction Subtype of GXS received from Payment-Engine
	IntraBank = "INTRABANK"
	// FastNetwork Transaction Subtype of GXS received from Payment-Engine
	FastNetwork = "FAST_NETWORK"
	// Grab Transaction Subtype of Grab received from Payment-Engine
	Grab = "GRAB"
	//RtolAJ Transaction Subtype of GXS received from Payment-Engine
	RtolAJ = "RTOL_AJ"
	//RtolAlto Transaction Subtype of GXS received from Payment-Engine
	RtolAlto = "RTOL_ALTO"
	//RPP Transaction Subtype of DBMY received from Payment-Engine
	RPP = "RPP_NETWORK"
	// Dash Transaction Subtype of Dash received from Payment-Engine
	Dash = "DASH"
	// MooMoo Transaction Subtype of MooMoo received from Payment-Engine
	MooMoo = "MOOMOO"
	//RENTAS Transaction Subtype of DBMY received from Payment-Engine
	RENTAS = "RENTAS"
)

// constant denoting channel of transactions
const (
	// RppChannelType Transaction ChannelType
	RppChannelType = "RPP_NETWORK"
)

// constant denoting service type
const (
	// QrPaymentServiceType ...
	QrPaymentServiceType = "QR_PAYMENT"
	// QrTransferServiceType ...
	QrTransferServiceType = "QR_TRANSFER"
)

// constants denoting status received from payments (not the exhaustive list)
const (
	//PaymentStatusFailed ...
	PaymentStatusFailed = "FAILED"
	//PaymentStatusCompleted ...
	PaymentStatusCompleted = "COMPLETED"
	//PaymentStatusProcessing
	PaymentStatusProcessing = "PROCESSING"
)

// Rail used for different types of payments transactions
const (
	// FASTRail ...
	FASTRail = "FAST"
	// PayNowRail ...
	PayNowRail = "PayNow"
	// MobileNumberRail defines external rail proxy using mobile number.
	MobileNumberRail = "MSISDN"
	// NRICRail defines external rail proxy using national identity card.
	NRICRail = "NRIC"
	// BusinessRegistrationNumberRail defines external rail proxy using business registration number.
	BusinessRegistrationNumberRail = "BREG"
	// PassportNumberRail defines external rail proxy using passport number.
	PassportNumberRail = "PSPT"
	// ArmyIDRail defines external rail proxy using army id.
	ArmyIDRail = "ARMN"
)

// constants denoting different grab activity type
const (
	GrabDefault             = "DEFAULT"
	GrabCancellationFee     = "CANCELLATIONFEE"
	GrabOutstandingFee      = "OUTSTANDINGFEE"
	GrabNoShowFee           = "NOSHOWFEE"
	GrabTransport           = "TRANSPORT"
	GrabExpress             = "EXPRESS"
	GrabFood                = "FOOD"
	GrabTopup               = "TOPUP"
	GrabTipping             = "TIPPING"
	GrabRefund              = "REFUND"
	GrabTopUp               = "TOPUP"
	GrabRide                = "TRANSPORT"
	GrabGift                = "GRABGIFT"
	GrabPayLater            = "PAYLATER"
	GrabRewardsSubscription = "REWARDSUBSCRIPTION"
	GrabInstallment         = "INSTALLMENT"
	GrabPayOnline           = "GPONLINE"
	GrabCard                = "GPMCARREAR"

	GrabTopUpDisplayName               = "GrabPay Wallet"
	GrabRideDisplayName                = "Grab Ride"
	GrabFoodDisplayName                = "Grab Food"
	GrabExpDisplayName                 = "Grab Express"
	GrabGiftDisplayName                = "Grab Gift"
	GrabPaylaterDisplayName            = "Grab PayLater"
	GrabRewardsSubscriptionDisplayName = "Grab Subscription"
	GrabInstallmentDisplayName         = "Grab Instalment"
	GrabPayOnlineDisplayName           = "GrabPay Online"
	GrabTippingDisplayName             = "Grab Tips"
	GrabCardDisplayName                = "Grab Card"
	GrabCancellationFeeDisplayName     = "Grab Cancellation Fee"
	GrabOutstandingFeeDisplayName      = "Grab Outstanding Fee"
	GrabNoShowFeeDisplayName           = "Grab No Show Fee"
	GrabDefaultActivity                = "Grab"
)

var (
	//GrabDisplayNameMap ...
	GrabDisplayNameMap = map[string]string{
		GrabTopUp:               GrabTopUpDisplayName,
		GrabFood:                GrabFoodDisplayName,
		GrabRide:                GrabRideDisplayName,
		GrabExpress:             GrabExpDisplayName,
		GrabGift:                GrabGiftDisplayName,
		GrabPayLater:            GrabPaylaterDisplayName,
		GrabRewardsSubscription: GrabRewardsSubscriptionDisplayName,
		GrabInstallment:         GrabInstallmentDisplayName,
		GrabPayOnline:           GrabDefaultActivity,
		GrabTipping:             GrabTippingDisplayName,
		GrabCard:                GrabDefaultActivity,
		GrabCancellationFee:     GrabDefaultActivity,
		GrabOutstandingFee:      GrabDefaultActivity,
		GrabNoShowFee:           GrabDefaultActivity,
		GrabDefault:             GrabDefaultActivity,
	}
)

const (
	// PaymentTransactionTypeDebitScenarioKey ...
	PaymentTransactionTypeDebitScenarioKey = DepositsDomain + "." + QrPaymentTxType + "." + DEBIT
	// PaymentReversalTransactionTypeCreditScenarioKey ...
	PaymentReversalTransactionTypeCreditScenarioKey = DepositsDomain + "." + QrPaymentReversalTxType + "." + CREDIT
)

var (
	// QrTransactionTypes ...
	QrTransactionTypes = []string{QrPaymentServiceType, QrTransferServiceType}
)

const (
	// CASAMainAccount ...
	CASAMainAccount = "Main account"
	// Default ...
	Default = "DEFAULT"
)
