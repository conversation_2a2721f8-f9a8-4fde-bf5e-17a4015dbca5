package constants

// Transaction direction
const (
	OUT = "to"
	IN  = "from"
)

// Terms used in forming the transaction-details description
// nolint: dupl
const (
	TRANSFER                        = "Transfer"
	TransactionAdjustment           = "transactionAdjustment"
	BankTransfer                    = "bankTransfer"
	OnlineTransfer                  = "onlineTransfer"
	InterestEarned                  = "interestEarned"
	BonusInterestEarned             = "bonusInterestEarned"
	BonusInterestEarnedReversal     = "bonusInterestEarnedReversal"
	TaxOnInterestDesc               = "taxOnInterest"
	FundMoved                       = "fundMoved"
	WithdrawalDesc                  = "withdrawal"
	TransactionFee                  = "transactionFee"
	Completed                       = "completed"
	Failed                          = "failed"
	Payment                         = "payment"
	Refund                          = "refund"
	DBMYInterestEarned              = "DBMYInterestEarned"
	DBMYBonusInterestEarned         = "DBMYBonusInterestEarned"
	DBMYBonusInterestEarnedReversal = "DBMYBonusInterestEarnedReversal"
	MoneyAdded                      = "moneyAdded"
	MoneyWithdrawn                  = "moneyWithdrawn"
	// deprecated in favour of RPPNormalTransfer
	RPPOnlineBanking = "duitnowOnlineBanking"
	// deprecated in favour of RPPNormalTransferRev
	RPPOnlineBankingRev          = "duitnowOnlineBankingReversal"
	RPPNormalTransfer            = "duitnowTransfer"
	RPPNormalTransferRev         = "duitnowReversal"
	QrPayment                    = "qrPayment"
	QrPaymentReversal            = "qrPaymentReversal"
	QrTransfer                   = "qrTransfer"
	QrTransferReversal           = "qrTransferReversal"
	DBMYTransfer                 = "transfer"
	DBMYTransferRev              = "transferReversal"
	DBMYBank                     = "gxbank"
	DBMYBankPocket               = "gxSavingsAccount"
	BankAdjustment               = "bankAdjustment"
	DebitCardPaymentTo           = "debitCardPaymentTo"
	DebitCardRefundPaymentFrom   = "debitCardRefundPaymentFrom"
	DebitCardAtmWithdrawal       = "debitCardAtmWithdrawal"
	DebitCardAtmFeeCharged       = "debitCardAtmFeeCharged"
	DebitCardAtmFeeWaived        = "debitCardAtmFeeWaived"
	DebitCardMoneySendReceive    = "debitCardMoneySendReceive"
	DebitCardAtmWithdrawalRefund = "debitCardAtmWithdrawalRefund"
	ATMWithdraw                  = "atmWithdraw"
	ATM                          = "atm"
	ATMWithdrawFee               = "atmWithdrawFee"
	ATMWithdrawal                = "atmWithdrawal"
	ATMWithdrawalReversal        = "atmWithdrawalReversal"
	ATMWithdrawalFee             = "atmWithdrawalFee"
	CardAnnualFee                = "cardAnnualFee"
	CardAnnualFeeWaived          = "cardAnnualFeeWaived"
	NewCardIssuanceFee           = "newCardIssuanceFee"
	NewCardIssuanceFeeWaived     = "newCardIssuanceFeeWaived"
	CardReplacementFee           = "cardReplacementFee"
	CardReplacementFeeWaived     = "cardReplacementFeeWaived"
	MEPSATMWithdrawalFee         = "mepsAtmWithdrawalFee"
	MEPSATMWithdrawalFeeWaived   = "mepsAtmWithdrawalFeeWaived"
	FeeWaived                    = "feeWaived"
	BankRefund                   = "bankRefund"
	ProvisionalCredit            = "provisionalCredit"
	ProvisionalCreditReversal    = "provisionalCreditReversal"
	Earmark                      = "earmark"
	LendingDrawdown              = "drawdown"
	LendingRepayment             = "repayment"
	UnclaimedMonies              = "unclaimedMonies"
	To                           = "to"
)

// Counterparty names for pocket activities
const (
	InterestPayoutDisplayName = "Interest Earned"
	TaxPayoutDisplayName      = "Tax on Interest"
	FundsAddedDisplayName     = "Funds added"
	FundsWithdrawnDisplayName = "Funds withdrawn"
)

// Counterparty names for loan in CASA
const (
	FlexiCreditDisplayName          = "FlexiCredit"
	FlexiCreditRepaymentDisplayName = "FlexiCredit repayment"
)

// Counterparty names for Biz FlexiLoan
const (
	BizFlexiLoanDisplayName          = "Biz FlexiLoan"
	BizFlexiLoanRepaymentDisplayName = "Biz FlexiLoan repayment"
)
