package testapi

import (
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("AccountCalendarActivity", func() {
	var (
		xfccHeader                     hcl.RequestModifier
		xserviceIDHeader               hcl.RequestModifier
		xuserIDHeader                  hcl.RequestModifier
		cifNumber                      string
		accountCalendarActivityMockDao *storage.MockIAccountCalendarActivityDAO
		mockCustomerMasterDBMYClient   customerMasterDBMYMock.CustomerMaster
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")
		cifNumber = "SG148087844878"

		accountCalendarActivityMockDao = &storage.MockIAccountCalendarActivityDAO{}
		storage.AccountCalendarActivityD = accountCalendarActivityMockDao

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.CustomerMasterClient = mockCustomerMasterClient
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: cifNumber,
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cifNumber,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	Context("Happy path", func() {

		When("calling with correct parameters but no resource present", func() {
			It("returns 200", func() {
				// Mocking Methods of External Services
				lookupCifRequest := &customerMasterApi.LookupCIFNumberRequest{
					ID: "test-grab-id",
					Target: &customerMasterApi.TargetGroup{
						ServiceID: "DIGIBANK",
					},
				}
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).
					Return(&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)
				listCASAAccountForCifRequest := &accountServiceApi.ListCASAAccountsForCustomerDetailRequest{CifNumber: cifNumber}
				listCASAAccountForCifResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
					Accounts: []accountServiceApi.CASAAccountDetail{
						{
							Id:               "12345",
							ProductVariantID: "deposit_product",
							AccountType:      "CASA",
							Status:           "ACTIVE",
							ProductSpecificParameters: map[string]string{
								"applicableHoldcodes": "",
							},
							Features: &accountServiceApi.Feature{
								Credit: true,
								Debit:  true,
							},
							OpeningTimestamp: time.Now(), //"2022-10-13T13:32:12Z"
						},
						{
							Id:               "********",
							ParentAccountID:  "12345",
							ProductVariantID: "deposit_product",
							AccountType:      "CASA",
							AccountName:      "sub account",
							Status:           "ACTIVE",
							ProductSpecificParameters: map[string]string{
								"applicableHoldcodes": "",
							},
							Features: &accountServiceApi.Feature{
								Credit: true,
								Debit:  true,
							},
							OpeningTimestamp: time.Now(), //"2022-10-13T13:34:50Z"
						}},
					MaxChildAccountLimit: 8,
				}
				mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
					Return(listCASAAccountForCifResponse, nil)
				// mocking db
				accountCalendarActivityMockDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.AccountCalendarActivity{}, nil).Once()
				expectedResponse := &api.AccountCalendarActivityResponse{
					Dates: []api.Date{},
				}

				// POST
				resp, err := client.Post("/v2/account-calendar-activity", xfccHeader, xserviceIDHeader, xuserIDHeader)
				responseObject := &api.AccountCalendarActivityResponse{}
				json.Unmarshal(resp.Body.Bytes, responseObject)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(responseObject).Should(Equal(expectedResponse))
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling with correct parameters and resource exist", func() {
			It("returns 200", func() {
				// Mocking Methods of External Services
				lookupCifRequest := &customerMasterApi.LookupCIFNumberRequest{
					ID: "test-grab-id",
					Target: &customerMasterApi.TargetGroup{
						ServiceID: "DIGIBANK",
					},
				}
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).
					Return(&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)
				listCASAAccountForCifRequest := &accountServiceApi.ListCASAAccountsForCustomerDetailRequest{CifNumber: cifNumber}
				listCASAAccountForCifResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
					Accounts: []accountServiceApi.CASAAccountDetail{
						{
							Id:               "12345",
							ProductVariantID: "deposit_product",
							AccountType:      "CASA",
							Status:           "ACTIVE",
							ProductSpecificParameters: map[string]string{
								"applicableHoldcodes": "",
							},
							Features: &accountServiceApi.Feature{
								Credit: true,
								Debit:  true,
							},
							OpeningTimestamp: time.Now(), //"2022-10-13T13:32:12Z"
						},
						{
							Id:               "********",
							ParentAccountID:  "12345",
							ProductVariantID: "deposit_product",
							AccountType:      "CASA",
							AccountName:      "sub account",
							Status:           "ACTIVE",
							ProductSpecificParameters: map[string]string{
								"applicableHoldcodes": "",
							},
							Features: &accountServiceApi.Feature{
								Credit: true,
								Debit:  true,
							},
							OpeningTimestamp: time.Now(), //"2022-10-13T13:34:50Z"
						}},
					MaxChildAccountLimit: 8,
				}
				mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).Return(
					listCASAAccountForCifResponse, nil)
				// mocking db
				prevMonthDate := utils.ToPreviousMonth(time.Now())
				accountCalendarActivityMockDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.AccountCalendarActivity{{
						ID:        10,
						AccountID: "12345",
						Year:      int64(prevMonthDate.Year()),
						Months:    prevMonthDate.Format(utils.TimestampMonthFormat),
						UpdatedAt: time.Now(),
					}}, nil).Once()
				expectedResponse := &api.AccountCalendarActivityResponse{Dates: []api.Date{{
					Year:   strconv.Itoa(prevMonthDate.Year()),
					Months: []string{prevMonthDate.Format(utils.TimestampMonthFormat)},
				}}}

				// POST
				resp, err := client.Post("/v2/account-calendar-activity", xfccHeader, xserviceIDHeader, xuserIDHeader)
				responseObject := &api.AccountCalendarActivityResponse{}
				json.Unmarshal(resp.Body.Bytes, responseObject)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				Expect(responseObject).Should(Equal(expectedResponse))

			})
		})

	})

	Context("Error Path ", func() {
		When("calling without grab id header", func() {
			It("returns 404 error", func() {
				emptyLookupCifRequest := &customerMasterApi.LookupCIFNumberRequest{
					ID: "",
					Target: &customerMasterApi.TargetGroup{
						ServiceID: "",
					},
				}
				cifNotFoundErr := errors.New("CIF_MAPPING_NOT_FOUND:cif number not found")
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, emptyLookupCifRequest).
					Return(nil, cifNotFoundErr).Once()

				resp, err := client.Post("/v2/account-calendar-activity", xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

			})
		})

	})
})
