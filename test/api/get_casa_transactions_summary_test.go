package testapi

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("GetCASATransactionsSummary", func() {
	var (
		xfccHeader                   hcl.RequestModifier
		xClientIDHeader              hcl.RequestModifier
		mockTransactionData          *storage.MockITransactionsDataDAO
		mockInterestAggregate        *storage.MockIInterestAggregateDAO
		xprofileIDHeader             hcl.RequestModifier
		xserviceIDHeader             hcl.RequestModifier
		xuserIDHeader                hcl.RequestModifier
		mockCustomerMasterDBMYClient customerMasterDBMYMock.CustomerMaster
	)
	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xprofileIDHeader = hcl.Header("X-grab-Profile-ID", "test-cif")
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")
		xClientIDHeader = hcl.Header("X-Grab-Id-Clientid", "sentry-t6")
		mockTransactionData = &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate = &storage.MockIInterestAggregateDAO{}
		storage.InterestAggregateD = mockInterestAggregate
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})
	Context("Happy-path", func() {
		When("request params are valid and DB contains data", func() {
			It("returns 200 OK and the response", func() {
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil).Once()
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)

				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xuserIDHeader, xprofileIDHeader, xserviceIDHeader, xClientIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				expectedResponse := responses.GetCASATransactionsSummaryValidResponse()
				respObj := &api.GetCASATransactionsSummaryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("DB returns different types of transactions", func() {
			It("returns 200 OK and the response", func() {
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PendingTransactionsDBMockRows(), nil).Once()
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xuserIDHeader, xprofileIDHeader, xserviceIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				expectedResponse := responses.GetCASATransactionsSummaryValidResponse2()
				respObj := &api.GetCASATransactionsSummaryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
	Context("service request validation", func() {
		When("account id is missing in the request ", func() {
			It("returns 400 Bad Request", func() {
				body := hcl.JSON(`{
						"accountID":""
				}`)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				expectedResponse := MissingAccountIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Database errors", func() {
		When("no data is present in the both dbs", func() {
			It("returns 200 OK with response with Zero values", func() {
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xserviceIDHeader, xuserIDHeader, xprofileIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				expectedResponse := responses.GetCASATransactionsSummaryEmptyResponses()
				respObj := &api.GetCASATransactionsSummaryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("no data is present in the transaction_data db", func() {
			It("returns 200 OK with response with Zero values", func() {
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xserviceIDHeader, xuserIDHeader, xprofileIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				expectedResponse := responses.GetCASATransactionsSummaryNoTransferResponse()
				respObj := &api.GetCASATransactionsSummaryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("no data is present in the interest_aggregate db", func() {
			It("returns 200 OK with response with Zero values", func() {
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xserviceIDHeader, xuserIDHeader, xprofileIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				expectedResponse := responses.GetCASATransactionsSummaryNoInterestResponse()
				respObj := &api.GetCASATransactionsSummaryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("transaction_data db fails", func() {
			It("returns 500 Server Error", func() {
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, nil)
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error while getting the transactions from database"))
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xserviceIDHeader, xuserIDHeader, xprofileIDHeader, body)
				expectedResponse := InternalServerErrorResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("interest_aggregate db fails", func() {
			It("returns 500 Server Error", func() {
				body := hcl.JSON(`{
						"accountID":"**********"
				}`)
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error while getting the interest_aggregate from database"))
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
					Account: &accountServiceApi.Account{
						CifNumber: "test-cif",
					},
				}, nil)
				resp, err := client.Post(GetCASATransactionsSummaryURL, xfccHeader, xserviceIDHeader, xuserIDHeader, xprofileIDHeader, body)
				expectedResponse := InternalServerErrorResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
