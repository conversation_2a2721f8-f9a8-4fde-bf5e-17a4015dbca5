package testapi

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	pairingServiceMock "gitlab.myteksi.net/dakota/payment/pairing-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("LendingTransactionDetailCX", func() {
	var (
		xfccHeader                   hcl.RequestModifier
		mockPaymentDao               *storage.MockIPaymentDetailDAO
		mockTransactionData          *storage.MockITransactionsDataDAO
		mockLoanDetailDao            *storage.MockILoanDetailDAO
		xserviceIDHeader             hcl.RequestModifier
		xuserIDHeader                hcl.RequestModifier
		mockCustomerMasterDBMYClient customerMasterDBMYMock.CustomerMaster
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")

		mockPaymentDao = &storage.MockIPaymentDetailDAO{}
		mockTransactionData = &storage.MockITransactionsDataDAO{}
		mockLoanDetailDao = &storage.MockILoanDetailDAO{}

		storage.PaymentDetailD = mockPaymentDao
		storage.TransactionsDataD = mockTransactionData
		storage.LoanDetailD = mockLoanDetailDao

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		mockPairingServiceClient = &pairingServiceMock.PairingService{}
		service.CustomerMasterClient = mockCustomerMasterClient
		service.PairingServiceClient = mockPairingServiceClient
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	DescribeTable(
		"InvalidParams",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
			Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
		},
		Entry("AccountID missing",
			hcl.JSON(`{
					"safeID":"abcd-123-fgh", 
					"transactionID":"1580b776-6d88-4b43-93b1-18ce779b11ff"
			}`), MissingAccountIDResponse,
		),
		Entry("TransactionID missing",
			hcl.JSON(`{
					"safeID":"abcd-123-fgh", 
					"accountID":"**********"
			}`), MissingTxnIDResponse,
		),
	)
	Context("empty-response", func() {
		When("no data is present in the database ", func() {
			It("returns 404 OK and empty response", func() {
				//Mocking the methods
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, data.ErrNoData).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID":"**********",
    					"transactionID":"1580b776-6d88-4b43-93b1-18ce779b11ff"
				}`)
				expectedResponse := &api.GetTransactionDetailResponse{}
				resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))
				respObj := &api.GetTransactionDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
	Context("error-response", func() {
		When("error in reading from the database", func() {
			It("returns 500 Internal server error", func() {
				//Mocking the methods
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.TransactionsData{}, errors.New("random error message")).Once()

				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID":"**********",
						"transactionID":"1580b776-6d88-4b43-93b1-18ce779b11ff"
				}`)
				resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(InternalServerErrorResponse))
			})
		})
	})
	Context("Happy-path", func() {
		When("Pay now drawdown transaction", func() {
			It("returns 200 OK", func() {
				//Mocking the methods
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.TransactionsDataMockDBRowsForLending()[1]}, nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction("COMPLETED"), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[0]}, nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID":"12345",
						"transactionID":"abc123efg"
				}`)
				expectedResp := responses.GetLendingTransactionDetailResponse()[0]
				resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetLendingTransactionDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("Pay now repayment transaction", func() {
			It("returns 200 OK", func() {
				//Mocking the methods
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.TransactionsDataMockDBRowsForLending()[2]}, nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction("COMPLETED"), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[2]}, nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID":"12345",
						"transactionID":"abc123efg"
				}`)
				expectedResp := responses.GetLendingTransactionDetailResponse()[1]
				resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetLendingTransactionDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
	})
	Context("Unauthorized accountID request", func() {
		When("Account does not have permission", func() {
			It("returns 401 Unauthorized", func() {
				//Mocking the methods
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil).Once()

				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID":"**********",
						"transactionID":"58e3ac0a-f888-44e6-a1e1-ed36sdgs22"
				}`)
				resp, err := client.Post(GetLendingTransactionDetailCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(401))
				Expect(resp.Body.String).Should(MatchJSON(UnauthorizedAccountIDResponse))
			})
		})
	})
})
