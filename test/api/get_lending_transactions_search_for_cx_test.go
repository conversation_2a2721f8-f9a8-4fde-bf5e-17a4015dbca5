package testapi

import (
	"encoding/json"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("LendingTransactionSearchCX", func() {
	var (
		xfccHeader                    hcl.RequestModifier
		mockPaymentDetailStorageDAO   *storage.MockIPaymentDetailDAO
		mockTransactionDataStorageDAO *storage.MockITransactionsDataDAO
		mockLoanDetailsStorageDAO     *storage.MockILoanDetailDAO
		xserviceIDHeader              hcl.RequestModifier
		xuserIDHeader                 hcl.RequestModifier
		mockCustomerMasterDBMYClient  customerMasterDBMYMock.CustomerMaster
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")

		mockPaymentDetailStorageDAO = &storage.MockIPaymentDetailDAO{}
		mockTransactionDataStorageDAO = &storage.MockITransactionsDataDAO{}
		mockLoanDetailsStorageDAO = &storage.MockILoanDetailDAO{}
		storage.PaymentDetailD = mockPaymentDetailStorageDAO
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		storage.LoanDetailD = mockLoanDetailsStorageDAO

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.CustomerMasterClient = mockCustomerMasterClient
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: "test-cif",
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	DescribeTable("body has missing/incorrect field",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
			Expect(resp.Body.String).Should(Equal(expectedResponse))
		},
		Entry("empty accountID",
			hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": ""}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid page size",
			hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12343", "pageSize": -1}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid page size",
			hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12343", "pageSize": 255}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid date format",
			hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12343", "pageSize": 10, "startDate":"2023-01-31"}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`))

	Context("get account transaction search happy cases", func() {
		When("calling with correct parameters but no resource present", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)
				body := hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12345", "pageSize": 2, "startDate": "2021-08-01T00:00:00Z", "endDate": "2021-08-31T15:00:00Z"}`)

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// POST
				resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling with correct parameters but no payment resource present", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.GetAllLendingTransactionsFirstPageMockDBResponse(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)
				body := hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12345", "pageSize": 2, "startDate": "2021-08-01T00:00:00Z", "endDate": "2021-08-31T15:00:00Z"}`)

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// POST
				resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling with correct parameters but no loan resource present", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.GetAllLendingTransactionsFirstPageMockDBResponse(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRowsForLendingTransaction("COMPLETED"), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)
				body := hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "12345", "pageSize": 2, "startDate": "2021-08-01T00:00:00Z", "endDate": "2021-08-31T15:00:00Z"}`)

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// POST
				resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling with correct parameters and resource exist", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.TransactionsDataMockDBRowsForLending(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailsMockDBRowsForTxnSearch(), nil)
				body := hcl.JSON(`{"safeID":"abcd-123-fgh", "accountID": "**********", "pageSize": 1, "startDate": "2022-10-01T00:00:00Z", "endDate": "2022-10-12T15:00:00Z"}`)
				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// POST
				resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetLendingTransactionSearchResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				var transactionsData []api.LendingTransactionSearchData
				for _, data := range respObj.Data {
					transactionsData = append(transactionsData, api.LendingTransactionSearchData{
						TransactionID:        data.TransactionID,
						Description:          data.Description,
						Amount:               data.Amount,
						LoanNames:            data.LoanNames,
						TransactionTimestamp: data.TransactionTimestamp,
						IconURL:              data.IconURL,
						Status:               data.Status,
						TransactionType:      data.TransactionType,
						TransactionSubtype:   data.TransactionSubtype,
					})
				}
				parsedExpectedResponse := &api.GetLendingTransactionSearchResponse{
					Links: respObj.Links,
					Data:  transactionsData,
				}
				Expect(parsedExpectedResponse).Should(Equal(responses.GetAllTransactionsFirstPageResponseForLending()))
			})
		})
	})

	Context("get account transaction search error cases", func() {
		When("calling with correct parameters but unauthorized request", func() {
			It("returns 401", func() {
				body := hcl.JSON(`{
						"safeID":"abcd-123-fgh", 
						"accountID": "1234895",
						"pageSize": 2,
						"startDate": "2022-10-01T00:00:00Z",
						"endDate": "2022-10-12T00:00:00Z"}`)

				//mocking the Customer-Master Call
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil).Once()

				// POST
				resp, err := client.Post(GetLendingTransactionSearchCXURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(401))
			})
		})
	})
})
