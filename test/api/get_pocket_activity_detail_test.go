package testapi

import (
	"encoding/json"
	"errors"

	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
)

var _ = Describe("AccountTransactionSearch", func() {
	var (
		xfccHeader                    hcl.RequestModifier
		mockTransactionDataStorageDAO *storage.MockITransactionsDataDAO
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockTransactionDataStorageDAO = &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.AccountServiceClient = mockAccountServiceClient
		service.CustomerMasterClient = mockCustomerMasterClient
	})
	Context("Service request field validation errors", func() {
		When("pocketID is missing", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id1"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := MissingPocketIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("pocketType is missing", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"test-id",
									"transactionID":"test-client-batch-id1"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := MissingPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("pocketType is invalid", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"test-id",
									"pocketType":"SPEND",
									"transactionID":"test-client-batch-id1"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := InvalidPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("transactionID is missing", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"test-id",
									"pocketType":"SAVINGS"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := MissingTxnIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
	})
	Context("Happy-path", func() {
		When("Request is valid and no transactions present in the db", func() {
			It("returns 200 OK and empty response", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.TransactionsData{}, nil)

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********000",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id1"
								}`))
				expectedResponse := &api.GetPocketActivityDetailResponse{}
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketActivityDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("Pocket funds transfer transaction", func() {
			It("returns 200 OK", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.PocketTransactions()[0]}, nil)

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********000",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id1"
								}`))
				expectedResponse := responses.GetPocketActivityDetailResponseBahasa()[0]
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketActivityDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("Pocket funds withdrawal transaction", func() {
			It("returns 200 OK", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.PocketTransactions()[2]}, nil)

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********000",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id4"
								}`))
				expectedResponse := responses.GetPocketActivityDetailResponseBahasa()[1]
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketActivityDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("Pocket interest payout transaction", func() {
			It("returns 200 OK", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.PocketInterestPayoutTransactionInput()[1]}, nil)

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********000",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id-interest-payout"
								}`))
				expectedResponse := responses.GetPocketActivityDetailResponseForBahasa()
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketActivityDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
	Context("Error-path", func() {
		When("Given pocket ID is not a pocket", func() {
			It("returns 400 BadRequest", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "",
							CifNumber:       "test-cif",
						},
					}, nil)

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id1"
								}`))
				expectedError := InvalidPocketResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("TransactionDB returns unknown DB error", func() {
			It("returns 500 ServerError", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceApi.GetAccountResponse{
						Account: &accountServiceApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("unknown DB fetch error"))

				// POST
				resp, err := client.Post(GetPocketActivityDetailURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"**********",
									"pocketType":"SAVINGS",
									"transactionID":"test-client-batch-id1"
								}`))
				expectedError := InternalServerErrorResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
	})
})
