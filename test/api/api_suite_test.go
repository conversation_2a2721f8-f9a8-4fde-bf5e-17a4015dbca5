package testapi

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/handlers"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/common/testauto/servustest"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	pairingServiceMock "gitlab.myteksi.net/dakota/payment/pairing-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	mockExternalLib "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/activeprofile/mocks"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func TestApi(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Api Suite")
}

var (
	service                         *handlers.TxHistoryService
	server                          servustest.ServerDescriptor
	setupErr                        error
	client                          *hcl.Client
	mockAccountServiceClient        *accountServiceMock.AccountService
	mockCustomerMasterClient        *customerMasterMock.CustomerMaster
	mockPairingServiceClient        *pairingServiceMock.PairingService
	mockExternalActiveProfileClient *mockExternalLib.MockExternalActiveProfile
)

const (
	GetTransactionDetailURL       = "v2/transactions/get"
	GetCASATransactionsSummaryURL = "/v1/casa-transactions-summary"
	GetPocketActivitiesURL        = "/v1/pocket-activities"
	GetPocketInterestEarnedURL    = "/v1/pocket-total-interest-earned"
	GetPocketActivityDetailURL    = "/v1/pocket-activity-detail"
	GetAccountInterestEarnedURL   = "/v1/casa-total-interest-earned"
	EmptyRequestBodyResponse      = `{
    								"code": "BAD_REQUEST",
    								"message": "The request has got invalid parameters",
    								"errors": [
        										{
            										"message": "'accountID' is a mandatory parameter."
        										},
        										{
            										"message": "'transactionID' is a mandatory parameter."
        										}
											 ]
								}`
	MissingAccountIDResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'accountID' is a mandatory parameter."}]
								}`
	MissingPocketIDResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'pocketID' is a mandatory parameter."}]
								}`
	MissingPocketTypeResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'pocketType' is a mandatory parameter."}]
								}`
	InvalidPocketTypeResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'pocketType' is invalid."}]
								}`
	MinPageSizeErrResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'pageSize' less than minPageSize."}]
								}`
	MaxPageSizeErrResponse = `{
									"code": "BAD_REQUEST",
									"message": "The request has got invalid parameters",
									"errors": [{"message": "'pageSize' greater than maxPageSize."}]
								}`
	InvalidPocketResponse = `{
										"code":"BAD_REQUEST",
										"message":"The pocket in the request is invalid"
									 }`
	MissingTxnIDResponse = `{
								"code": "BAD_REQUEST",
								"message": "The request has got invalid parameters",
								"errors": [{"message": "'transactionID' is a mandatory parameter."}]
							}`
	InternalServerErrorResponse = `{
										"code":"INTERNAL_SERVER_ERROR",
										"message":"There is a problem on our end. Please try again later"
									 }`
	UnauthorizedAccountIDResponse = `{
										"code":"UNAUTHORIZED",
										"message":"account permission forbidden"
									 }`
	InvalidCIFResponse = `{
								"code":"INTERNAL_SERVER_ERROR",
								"message":"Failed to call customer-master"
						  }`
	GetLendingTransactionSearchURL   = "/v1/lending/transactions-search"
	GetLendingTransactionSearchCXURL = "/v1/lending/transactions-search-cx"
	GetLendingTransactionDetailURL   = "/v1/lending/transaction-detail"
	GetLendingTransactionDetailCXURL = "/v1/lending/transaction-detail-cx"
)

var _ = BeforeSuite(func() {
	locale := utils.GetLocale()
	mockAccountServiceClient = &accountServiceMock.AccountService{}
	mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency:                        locale.Currency,
			PastMonthsThresholdForCalendarActivity: 12,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.dev.bankfama.net/dev/transfer_money.png",
			LendingRepayment:        "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			LendingDrawdown:         "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
		},
		Locale: utils.GetLocale(),
	}
	mockExternalActiveProfileClient = &mockExternalLib.MockExternalActiveProfile{}
	var mockMiddleware = servus.Middleware{ID: "mocking Active Profile", Func: func(h servus.HandlerFunc) servus.HandlerFunc { return h }}
	app := &servus.Application{}
	mockMiddleware.ApplyApp(app)
	mockExternalActiveProfileClient.On("WithActiveProfileV2", mock.Anything, mock.Anything, mock.Anything).Return(mockMiddleware)

	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	service = &handlers.TxHistoryService{
		AppConfig:                   mockAppConfig,
		CustomerMasterClient:        mockCustomerMasterClient,
		AccountServiceClient:        mockAccountServiceClient,
		ExternalActiveProfileClient: mockExternalActiveProfileClient,
	}
	server = servustest.StartServer(service)
	time.Sleep(time.Second)
	client, setupErr = hcl.NewClient(server.URL())
	Expect(setupErr).ShouldNot(HaveOccurred())
})

var _ = AfterSuite(func() {
	server.Shutdown()
})
