package testapi

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	pairingServiceMock "gitlab.myteksi.net/dakota/payment/pairing-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("LendingTransactionDetail", func() {
	var (
		xfccHeader, body             hcl.RequestModifier
		mockPaymentDao               *storage.MockIPaymentDetailDAO
		mockTransactionData          *storage.MockITransactionsDataDAO
		mockLoanDetailDao            *storage.MockILoanDetailDAO
		xserviceIDHeader             hcl.RequestModifier
		xuserIDHeader                hcl.RequestModifier
		mockCustomerMasterDBMYClient customerMasterDBMYMock.CustomerMaster
	)

	const (
		DrawdownTransactionType  = "DRAWDOWN"
		RepaymentTransactionType = "REPAYMENT"

		ProcessingStatus = "PROCESSING"
		FailedStatus     = "FAILED"
		CompletedStatus  = "COMPLETED"
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")
		body = hcl.JSON(`{"accountID":"12345","transactionID":"abc123efg"}`)

		mockPaymentDao = &storage.MockIPaymentDetailDAO{}
		mockTransactionData = &storage.MockITransactionsDataDAO{}
		mockLoanDetailDao = &storage.MockILoanDetailDAO{}

		storage.PaymentDetailD = mockPaymentDao
		storage.TransactionsDataD = mockTransactionData
		storage.LoanDetailD = mockLoanDetailDao

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		mockPairingServiceClient = &pairingServiceMock.PairingService{}
		service.CustomerMasterClient = mockCustomerMasterClient
		service.PairingServiceClient = mockPairingServiceClient
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	DescribeTable(
		"InvalidParams",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
			Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
		},
		Entry("Empty Request Body",
			hcl.JSON(`{}`), EmptyRequestBodyResponse,
		),
		Entry("AccountID missing",
			hcl.JSON(`{
					"transactionID":"1580b776-6d88-4b43-93b1-18ce779b11ff"
			}`), MissingAccountIDResponse,
		),
		Entry("TransactionID missing",
			hcl.JSON(`{
					"accountID":"**********"
			}`), MissingTxnIDResponse,
		),
	)
	Context("empty-response", func() {
		When("no data is present in the database ", func() {
			It("returns 404 OK and empty response", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, data.ErrNoData).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				expectedResponse := &api.GetTransactionDetailResponse{}
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))
				respObj := &api.GetTransactionDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
	Context("get account transaction detail happy cases", func() {
		BeforeEach(func() {
			mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
				&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
			mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
				&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()

		})

		When("Pay now recovery transaction", func() {
			It("returns 200 OK", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(nil, data.ErrNoData).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[2]}, nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetLendingTransactionDetailResponse()[3]
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent disbursement pending, loan details processing and payment missing", func() {
			It("should return processing transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.UtilizeLimitSuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.ProcessingLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetDrawdownTransactionDetailWithoutPaymentResponseForLending(ProcessingStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent settlement pending/successful and loan details processing and payment successful", func() {
			It("should return processing transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.SuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.ProcessingLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetDrawdownTransactionDetailResponseForLending(ProcessingStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent settlement pending and loan details processing and payment successful", func() {
			It("should return processing transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.SuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.ProcessingLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetDrawdownTransactionDetailResponseForLending(ProcessingStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent auth failed and loan details failed and payment failed", func() {
			It("should return failed transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.AuthFailedDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(FailedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.FailedLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetDrawdownTransactionDetailResponseForLending(FailedStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent settlement failed and loan details failed and payment failed", func() {
			It("should return failed transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.SuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(FailedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.FailedLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetDrawdownTransactionDetailResponseForLending(FailedStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with loan details failed and payment missing because of expiry", func() {
			It("should return failed transaction and status 200", func() {
				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.SuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.FailedLoanDetailsMockDBRowsForLending(DrawdownTransactionType), nil)
				expectedResp := responses.GetDrawdownTransactionDetailWithoutPaymentResponseForLending(FailedStatus)
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("drawdown for account with execute intent settlement successful, loan details completed and payment completed", func() {
			It("should return completed transaction and status 200", func() {

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return([]*storage.TransactionsData{resources.TransactionsDataMockDBRowsForLending()[1]}, nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[0]}, nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetLendingTransactionDetailResponse()[0]
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("repayment for account with transaction successful, loan details processing and payment completed", func() {
			It("should return completed transaction and status 200", func() {

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.RepaymentTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.ProcessingLoanDetailsMockDBRowsForLending(RepaymentTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetProcessingRepaymentTransactionDetailResponseForLending()
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("repayment for account with transaction rejected by TM, loan details failed and payment failed", func() {
			It("should return failed transaction and status 200", func() {

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.TransactionsDataMockDBRowsForFailed(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(FailedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.FailedLoanDetailsMockDBRowsForLending(RepaymentTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetFailedRepaymentTransactionDetailResponseForLending()
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
		When("repayment for account with transaction successful, loan details completed and payment completed", func() {
			It("should return completed transaction and status 200", func() {

				mockTransactionData.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.RepaymentTransactionsForLending(), nil).Once()
				mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PaymentDataMockDBRowsForLendingTransaction(CompletedStatus), nil).Once()
				mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
					Return(resources.CompletedLoanDetailsMockDBRowsForLending(RepaymentTransactionType), nil)
				mockPairingServiceClient.On("LookUpInfo", mock.Anything, mock.Anything).Return(resources.PairingServiceResponseMock(), nil)

				expectedResp := responses.GetRepaymentTransactionDetailResponseForLending()
				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := parseDetailResponse(resp)
				Expect(respObj).Should(Equal(expectedResp))
			})
		})
	})
	Context("get account transaction detail error cases", func() {
		When("called with correct parameters but account does not have permission", func() {
			It("returns 401 Unauthorized", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil).Once()

				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(401))
				Expect(resp.Body.String).Should(MatchJSON(UnauthorizedAccountIDResponse))
			})
		})
		When("called with correct parameters but error in reading the database", func() {
			It("returns 500 Internal server error", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()
				mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.TransactionsData{}, errors.New("random error message")).Once()

				resp, err := client.Post(GetLendingTransactionDetailURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(InternalServerErrorResponse))
			})
		})
	})

})

func parseDetailResponse(resp hcl.Response) *api.GetLendingTransactionDetailResponse {
	respObj := &api.GetLendingTransactionDetailResponse{}
	_ = json.Unmarshal(resp.Body.Bytes, respObj)
	return respObj
}
