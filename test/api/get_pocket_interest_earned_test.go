package testapi

import (
	"encoding/json"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
)

var _ = Describe("AccountTransactionSearch", func() {
	var (
		xfccHeader            hcl.RequestModifier
		mockInterestAggregate *storage.MockIInterestAggregateDAO
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockInterestAggregate = &storage.MockIInterestAggregateDAO{}
		storage.InterestAggregateD = mockInterestAggregate

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.AccountServiceClient = mockAccountServiceClient
		service.CustomerMasterClient = mockCustomerMasterClient
	})
	Context("Service request field validation errors", func() {
		When("pocketID is missing", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketInterestEarnedURL, xfccHeader,
					hcl.JSON(`{
									"pocketType":"SAVINGS"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := MissingPocketIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("pocketType is missing", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketInterestEarnedURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"test-id"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := MissingPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
		When("pocketType is invalid", func() {
			It("returns 400 Bad request", func() {
				resp, err := client.Post(GetPocketInterestEarnedURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"test-id",
									"pocketType":"SPEND"
								}`))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedError := InvalidPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
	})
	Context("Happy-path", func() {
		When("Request is valid and no transactions are present in the db", func() {
			It("returns 200 OK and empty response", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceDBMYApi.GetAccountResponse{
						Account: &accountServiceDBMYApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData)

				// POST
				resp, err := client.Post(GetPocketInterestEarnedURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"*************",
									"pocketType":"SAVINGS"
								}`))
				expectedResponse := responses.GetPocketInterestEarnedEmptyResponse()
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketInterestEarnedResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
		When("Request is valid and transactions present in the db", func() {
			It("returns 200 OK with valid response", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					&accountServiceDBMYApi.GetAccountResponse{
						Account: &accountServiceDBMYApi.Account{
							ParentAccountID: "**********",
							CifNumber:       "test-cif",
						},
					}, nil)

				// mocking db
				mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(resources.PocketInterestTransactionsDBMockRows(), nil)

				// POST
				resp, err := client.Post(GetPocketInterestEarnedURL, xfccHeader,
					hcl.JSON(`{
									"pocketID":"*************",
									"pocketType":"SAVINGS"
								}`))
				expectedResponse := responses.GetPocketInterestEarnedValidResponse()
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetPocketInterestEarnedResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
})
