package testapi

import (
	"encoding/json"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("LendingTransactionSearch", func() {
	var (
		xfccHeader, body              hcl.RequestModifier
		mockPaymentDetailStorageDAO   *storage.MockIPaymentDetailDAO
		mockTransactionDataStorageDAO *storage.MockITransactionsDataDAO
		mockLoanDetailsStorageDAO     *storage.MockILoanDetailDAO
		xserviceIDHeader              hcl.RequestModifier
		xuserIDHeader                 hcl.RequestModifier
		mockCustomerMasterDBMYClient  customerMasterDBMYMock.CustomerMaster
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")
		body = hcl.JSON(`{"accountID": "12345", "pageSize": 2, "startDate": "2021-08-01T00:00:00Z", "endDate": "2021-08-31T15:00:00Z"}`)

		mockPaymentDetailStorageDAO = &storage.MockIPaymentDetailDAO{}
		mockTransactionDataStorageDAO = &storage.MockITransactionsDataDAO{}
		mockLoanDetailsStorageDAO = &storage.MockILoanDetailDAO{}
		storage.PaymentDetailD = mockPaymentDetailStorageDAO
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		storage.LoanDetailD = mockLoanDetailsStorageDAO

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.CustomerMasterClient = mockCustomerMasterClient
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	DescribeTable("body has missing/incorrect field",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
			Expect(resp.Body.String).Should(Equal(expectedResponse))
		},
		Entry("empty accountID",
			hcl.JSON(`{"accountID": ""}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": -1}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": 255}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`),
		Entry("invalid date format",
			hcl.JSON(`{"accountID": "12343", "pageSize": 10, "startDate":"2023-01-31"}`), `{"code":"BAD_REQUEST","message":"The request has got invalid parameters"}
`))

	Context("get account transaction search happy cases", func() {
		BeforeEach(func() {
			mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
				&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
			mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
				&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil).Once()

		})
		When("calling for account with no transactions and loan details present", func() {
			It("should returns 200", func() {
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling for account with no payment details present", func() {
			It("returns 200", func() {
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.GetAllLendingTransactionsFirstPageMockDBResponse(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling for with transactions and without loan details present", func() {
			It("returns 200", func() {
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.GetAllLendingTransactionsFirstPageMockDBResponse(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{}, nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})

		When("calling with correct parameters and resource exist - page_1 response", func() {
			It("returns 200", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.TransactionsDataMockDBRowsForLending(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailsMockDBRowsForTxnSearch(), nil)
				body = hcl.JSON(`{"accountID": "**********", "pageSize": 1, "startDate": "2022-10-01T00:00:00Z", "endDate": "2022-10-12T15:00:00Z"}`)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetAllTransactionsFirstPageResponseForLending()))
			})
		})

		When("calling with correct parameters for recovery transactions - page_1 response", func() {
			It("returns 200", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingDrawDownAndRecovery(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything).Return(resources.GetLoanDetailsByAccountID(), nil).Once()

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetAllTransactionsIncludingLendingRecovery()))
			})
		})

		When("drawdown for account with execute intent disbursement pending and loan details processing", func() {
			It("should return processing transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.UtilizeLimitSuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.ProcessingLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLendingBeforePayment(constants.DrawdownTransactionType, constants.ProcessingStatus)))
			})
		})

		When("drawdown for account with execute intent settlement pending and loan details processing", func() {
			It("should return processing transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.SettlementPendingDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.ProcessingLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.DrawdownTransactionType, constants.ProcessingStatus)))
			})
		})

		When("drawdown for account with execute intent settlement successful and loan details processing", func() {
			It("should return processing transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.SettlementSuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.ProcessingLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.DrawdownTransactionType, constants.ProcessingStatus)))
			})
		})

		When("drawdown for account with execute intent auth failed and loan details failed", func() {
			It("should return failed transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.AuthFailedDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.FailedLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLendingBeforePayment(constants.DrawdownTransactionType, constants.FailedStatus)))
			})
		})

		When("drawdown for account with execute intent settlement failed and loan details failed", func() {
			It("should return failed transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.SettlementFailedDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.FailedLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.DrawdownTransactionType, constants.FailedStatus)))
			})
		})

		When("drawdown for account with execute intent settlement successful and loan details completed", func() {
			It("should return completed transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.SettlementSuccessfulDrawdownTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.CompletedLoanDetailsMockDBRowsForLending(constants.DrawdownTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.DrawdownTransactionType, constants.CompletedStatus)))
			})
		})

		When("repayment for account with transaction successful and loan details processing", func() {
			It("should return processing transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.RepaymentTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.ProcessingLoanDetailsMockDBRowsForLending(constants.RepaymentTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetRepaymentProcessingTransactionSearchResultsForLending(constants.RepaymentTransactionType, constants.ProcessingStatus)))
			})
		})

		When("repayment for account with transaction successful and loan details failed", func() {
			It("should return failed transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.RepaymentTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.FailedLoanDetailsMockDBRowsForLending(constants.RepaymentTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.RepaymentTransactionType, constants.FailedStatus)))
			})
		})

		When("repayment for account with transaction successful and loan details completed", func() {
			It("should return completed transaction and status 200 - page_1 response", func() {
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.RepaymentTransactionsForLending(), nil).Once()
				mockLoanDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.CompletedLoanDetailsMockDBRowsForLending(constants.RepaymentTransactionType), nil)

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				parsedExpectedResponse := parseSearchResponse(resp)
				Expect(parsedExpectedResponse).Should(Equal(responses.GetTransactionSearchResultsForLending(constants.RepaymentTransactionType, constants.CompletedStatus)))
			})
		})
	})

	Context("get account transaction search error cases", func() {
		When("called with correct parameters but account does not have permission", func() {
			It("returns 401 Unauthorized", func() {
				body := hcl.JSON(`{
						"accountID": "1234895",
						"pageSize": 2,
						"startDate": "2022-10-01T00:00:00Z",
						"endDate": "2022-10-12T00:00:00Z"}`)

				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMaster.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil).Once()

				resp, err := client.Post(GetLendingTransactionSearchURL, xfccHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(401))
			})
		})
	})
})

func parseSearchResponse(resp hcl.Response) *api.GetLendingTransactionSearchResponse {
	respObj := &api.GetLendingTransactionSearchResponse{}
	_ = json.Unmarshal(resp.Body.Bytes, respObj)
	return respObj
}
