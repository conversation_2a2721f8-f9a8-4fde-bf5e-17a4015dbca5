package testapi

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var _ = Describe("ListAccountTransactionSearch", func() {
	var (
		xfccHeader                    hcl.RequestModifier
		mockPaymentDetailStorageDAO   *storage.MockIPaymentDetailDAO
		mockTransactionDataStorageDAO *storage.MockITransactionsDataDAO
		xprofileIDHeader              hcl.RequestModifier
		xserviceIDHeader              hcl.RequestModifier
		xuserIDHeader                 hcl.RequestModifier
		mockCustomerMasterDBMYClient  customerMasterDBMYMock.CustomerMaster
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		xprofileIDHeader = hcl.Header("X-grab-Profile-ID", "test-cif")
		xserviceIDHeader = hcl.Header("X-Grab-Id-Serviceid", "DIGIBANK")
		xuserIDHeader = hcl.Header("X-Grab-Id-Userid", "test-grab-id")

		mockPaymentDetailStorageDAO = &storage.MockIPaymentDetailDAO{}
		mockTransactionDataStorageDAO = &storage.MockITransactionsDataDAO{}
		storage.PaymentDetailD = mockPaymentDetailStorageDAO
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockCustomerMasterClient = &customerMasterMock.CustomerMaster{}
		service.CustomerMasterClient = mockCustomerMasterClient
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: "test-cif",
			},
		}, nil)
		service.AccountServiceClient = mockAccountServiceClient
		mockCustomerMasterDBMYClient = customerMasterDBMYMock.CustomerMaster{}
		cif := "test-cif"
		bif := "BIF1234"
		status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE
		mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
			&customerMasterDBMYApi.GetBusinessInfoResponse{
				CIF: cif,
				BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
					Bif:    &bif,
					Status: &status,
				}},
			}, nil)
		service.CustomerMasterDBMYClient = &mockCustomerMasterDBMYClient
	})

	DescribeTable("body has missing/incorrect field",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post("/v2/transactions/list", xfccHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
		},
		Entry("empty accountID",
			hcl.JSON(`{"accountID": ""}`), `{
				"code": "badRequest",
				"message": "request has invalid parameter(s)"
			}`),
		Entry("page size less than min page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": -1}`), `{
				"code": "badRequest",
				"message": "request has invalid parameter(s)"
			}`),
		Entry("page size greater than max page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": 251}`), `{
				"code": "badRequest",
				"message": "request has invalid parameter(s)"
			}`))

	Context("list account transaction search happy cases", func() {
		When("calling with correct parameters but no resource present", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil).Once()
				body := hcl.JSON(`{"accountID": "12345", "pageSize": 2}`)

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})
		When("calling with correct parameters and resource exist", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.GetAllTransactionsFirstPageMockDBResponse(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil).Once()
				body := hcl.JSON(`{
					"accountID": "**********",
					"pageSize": 2,
					"startDate": "2021-08-01",
					"endDate": "2021-08-31" }`)
				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					responses.GetAccountDetailsByAccountIDResponse(), nil)

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetTransactionsHistoryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetAllTransactionsFirstPageResponses()))
			})
		})
		When("calling with correct parameters with all inputs for payment fee", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.TransactionsDataMockDBRowsForPaymentFeeUseCaseRequest(), nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil).Once()
				body := hcl.JSON(`{"accountID": "************"}`)

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					responses.GetAccountDetailsByAccountIDResponse(), nil)

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetTransactionsHistoryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetAllTransactionsForPaymentFeeResponse()))
			})
		})
		When("calling with correct parameters with all inputs for child account transfers", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.TransactionsDataMockDBRowsForChildAccountTransfersUseCase(), nil).Once()
				body := hcl.JSON(`{"accountID": "************"}`)
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					responses.GetAccountDetailsByAccountIDResponse(), nil)

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetTransactionsHistoryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetAllTransactionsForChildAccountTransfersResponse()))
			})
		})
		When("calling with correct parameters with inputs for tax and interest payouts", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find",
					mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				).Return(resources.TransactionsDataMockDBRowsForTaxAndInterest(), nil).Once()
				body := hcl.JSON(`{"accountID": "************"}`)
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Mocking Methods of External Services
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()
				mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
					responses.GetAccountDetailsByAccountIDResponse(), nil)

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				respObj := &api.GetTransactionsHistoryResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetTransactionsForInterestAndTax()))
			})
		})
	})

	Context("list account transaction search error cases", func() {
		When("calling with correct parameters but unauthorized request", func() {
			It("returns 401", func() {
				body := hcl.JSON(`{
						"accountID": "1234895",
						"pageSize": 2}`)

				//mocking the Customer-Master Call
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil).Once()
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil).Once()

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(401))
			})
		})
		When("TransactionDB returns unknown DB error", func() {
			It("returns 500 ServerError", func() {
				mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
					&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
				mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
					&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil).Once()

				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("unknown DB fetch error"))

				// POST
				resp, err := client.Post("/v2/transactions/list", xfccHeader, xprofileIDHeader, xserviceIDHeader, xuserIDHeader,
					hcl.JSON(`{
					"accountID": "**********",
					"pageSize": 2,
					"startDate": "2021-08-01",
					"endDate": "2021-08-31" }`))
				expectedError := InternalServerErrorResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
	})
})
