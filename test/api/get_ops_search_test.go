package testapi

import (
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

var _ = Describe("GetOpsSearch", func() {
	var (
		xfccHeader                    hcl.RequestModifier
		mockPaymentDetailStorageDAO   *storage.MockIPaymentDetailDAO
		mockTransactionDataStorageDAO *storage.MockITransactionsDataDAO
	)

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.TransactionHistory, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockPaymentDetailStorageDAO = &storage.MockIPaymentDetailDAO{}
		mockTransactionDataStorageDAO = &storage.MockITransactionsDataDAO{}
		storage.PaymentDetailD = mockPaymentDetailStorageDAO
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockAccountServiceClient = &accountServiceMock.AccountService{}
		service.AccountServiceClient = mockAccountServiceClient
	})

	DescribeTable("body has missing/incorrect field",
		func(body hcl.RequestModifier, expectedResponse string) {
			resp, err := client.Post("/v1/internal/transactions/list", xfccHeader, body)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(resp.StatusCode).Should(Equal(400))
		},
		Entry("missing identifier",
			hcl.JSON(`{"accountID": "", "externalID": "", "transactionID": "", "batchID": ""}`), `{
				"code": 3001,
				"message": "Missing customer identifier or txn identifier"
			}`),
		Entry("invalid identifier combination - accountID, externalID",
			hcl.JSON(`{"accountID": "12345", "externalID": "abcde", "transactionID": "", "batchID": ""}`), `{
				"code": 3002,
				"message": "Invalid identifiers"
			}`),
		Entry("invalid identifier combination - accountID, transactionID, batchID",
			hcl.JSON(`{"accountID": "12345", "externalID": "", "transactionID": "123abc", "batchID": "456def"}`), `{
				"code": 3002,
				"message": "Invalid identifiers"
			}`),
		Entry("invalid identifier combination - externalID, transactionID",
			hcl.JSON(`{"accountID": "", "externalID": "abcde", "transactionID": "123abc", "batchID": ""}`), `{
				"code": 3002,
				"message": "Invalid identifiers"
			}`),
		Entry("invalid identifier combination - externalID, transactionID, batchID",
			hcl.JSON(`{"accountID": "", "externalID": "abcde", "transactionID": "123abc", "batchID": "456def"}`), `{
				"code": 3002,
				"message": "Invalid identifiers"
			}`),
		Entry("invalid identifier combination - transactionID, batchID",
			hcl.JSON(`{"accountID": "", "externalID": "", "transactionID": "123abc", "batchID": "456def"}`), `{
				"code": 3002,
				"message": "Invalid identifiers"
			}`),
		Entry("invalid filters - no attribute filter can be used if txn identifier is passed in",
			hcl.JSON(`{"externalID": "abcde", "status": "PROCESSING"}`), `{
			"code": 3003,
			"message": "Invalid filters"
		}`),
		Entry("must use timestamp identifier if want to filter by status",
			hcl.JSON(`{"accountID": "12343", "status": "PROCESSING"}`), `{
			"code": 3004,
			"message": "Missing timestamp filters"
		}`),
		Entry("Invalid status",
			hcl.JSON(`{"accountID": "12343", "status": "PENDING"}`), `{
			"code": 3005,
			"message": "Invalid transaction status"
		}`),
		Entry("Invalid txn type",
			hcl.JSON(`{"accountID": "12343", "transactionType": "SPEND"}`), `{
			"code": 3006,
			"message": "Invalid transaction type"
		}`),
		Entry("Invalid txn subtype",
			hcl.JSON(`{"accountID": "12343", "transactionSubtype": "SPEND"}`), `{
			"code": 3007,
			"message": "Invalid transaction subtype"
		}`),
		Entry("Invalid txn amount range",
			hcl.JSON(`{"accountID": "12343", "fromAmount": 100, "toAmount": 10}`), `{
			"code": 3008,
			"message": "Invalid transaction amount range"
		}`),
		Entry("Invalid txn amount",
			hcl.JSON(`{"accountID": "12343", "fromAmount": -100}`), `{
			"code": 3009,
			"message": "Invalid transaction amount"
		}`),
		Entry("Invalid txn amount",
			hcl.JSON(`{"accountID": "12343", "toAmount": -100}`), `{
			"code": 3009,
			"message": "Invalid transaction amount"
		}`),
		Entry("page size less than min page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": -1}`), `{
			"code": 3010,
			"message": "Invalid page size"
		}`),
		Entry("page size greater than max page size",
			hcl.JSON(`{"accountID": "12343", "pageSize": 251}`), `{
			"code": 3010,
			"message": "Invalid page size"
		}`))

	Context("list account transaction search happy cases", func() {
		When("calling with correct parameters but no resource present", func() {
			It("returns 200", func() {
				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil).Once()
				mockPaymentDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil).Once()
				body := hcl.JSON(`{"accountID": "12345", "pageSize": 2}`)

				// POST
				resp, err := client.Post("/v1/internal/transactions/list", xfccHeader, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})
	})

	Context("list account transaction search error cases", func() {
		When("TransactionDB returns unknown DB error", func() {
			It("returns 500 ServerError", func() {

				// mocking db
				mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("unknown DB fetch error"))

				// POST
				resp, err := client.Post("/v1/internal/transactions/list", xfccHeader,
					hcl.JSON(`{
					"accountID": "**********",
					"pageSize": 2,
					"startDate": "2021-08-01",
					"endDate": "2021-08-31" }`))
				expectedError := InternalServerErrorResponse
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedError))
			})
		})
	})
})
