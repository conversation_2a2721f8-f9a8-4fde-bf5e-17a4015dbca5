package responses

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
)

// GetAllTransactionsFirstPageResponseForLending ...
// nolint : dupl
func GetAllTransactionsFirstPageResponseForLending() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "/v1/lending/transactions-search?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjozMTo0MFosMywzLGFiYzEyM2VmZw==&startDate=2022-10-01T00:00:00Z&endDate=2022-10-12T15:00:00Z",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjozMTo0MFosMywzLGFiYzEyM2VmZw==",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc123efg",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -13,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		},
		},
	}
}

// GetAllTransactionsFirstPageResponseForLendingRepayment ...
// nolint : dupl
func GetAllTransactionsFirstPageResponseForLendingRepayment() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "efg1111abd",
			Description:   "Payment for Europe trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          13400,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "REPAYMENT",
			TransactionSubtype:   "RPP_NETWORK",
			LoanNames:            []string{"Europe trip"},
		},
		},
	}
}

// GetAllTransactionsIncludingLendingRecovery ...
// nolint : dupl
func GetAllTransactionsIncludingLendingRecovery() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "hij123abd",
			Description:   "Payment for Europe trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          1512,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "REPAYMENT",
			TransactionSubtype:   "RPP_NETWORK",
			LoanNames:            []string{"Europe trip"},
		}, {
			TransactionID: "abc123efg",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -13,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		},
		},
	}
}

// GetAllTransactionsNoNextPageResponseForLending ...
// nolint : dupl
func GetAllTransactionsNoNextPageResponseForLending() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc123efg",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -13,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		},
			{
				TransactionID: "efg123abd",
				Description:   "Drawdown for Europe Trip",
				IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
				Amount: &api.Money{
					CurrencyCode: "MYR",
					Val:          -513,
				},
				Status:               "COMPLETED",
				TransactionTimestamp: time.Unix(**********, 0).UTC(),
				TransactionType:      "DRAWDOWN",
				TransactionSubtype:   "INTRABANK",
				LoanNames:            []string{"Europe Trip"},
			},
			{
				TransactionID: "efg1111abd",
				Description:   "Payment for Europe Trip",
				IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
				Amount: &api.Money{
					CurrencyCode: "MYR",
					Val:          1512,
				},
				Status:               "COMPLETED",
				TransactionTimestamp: time.Unix(**********, 0).UTC(),
				TransactionType:      "REPAYMENT",
				TransactionSubtype:   "RPP_NETWORK",
				LoanNames:            []string{"Europe Trip"},
			},
		},
	}
}

// GetAllTransactionsPrevNextBothExistResponseForLending ...
func GetAllTransactionsPrevNextBothExistResponseForLending() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "/v1/lending/transactions-search?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==&startDate=2021-08-01T00:00:00Z&endDate=2021-08-31T00:00:00Z",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
			"prev":         "/v1/lending/transactions-search?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==&startDate=2021-08-01T00:00:00Z&endDate=2021-08-31T00:00:00Z",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "efg123abd",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -513,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		}},
	}
}

// GetTransactionSearchResultsForLendingBeforePayment ...
// nolint : dupl
func GetTransactionSearchResultsForLendingBeforePayment(transactionType string, status string) *api.GetLendingTransactionSearchResponse {
	var description, iconURL string
	var amount int64
	var loanNames []string
	if transactionType == "DRAWDOWN" {
		description = "Drawdown for Europe Trip"
		iconURL = "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png"
		amount = -1512
		loanNames = []string{"Europe Trip"}
	} else {
		description = "Payment for your loans"
		iconURL = "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png"
		amount = 1512
	}
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc456efg",
			Description:   description,
			IconURL:       iconURL,
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          amount,
			},
			Status:               status,
			LoanNames:            loanNames,
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "LINE_OF_CREDIT",
			TransactionSubtype:   "UTILISE_LIMIT",
		}},
	}
}

// GetTransactionSearchResultsForLending ...
// nolint : dupl
func GetTransactionSearchResultsForLending(transactionType string, status string) *api.GetLendingTransactionSearchResponse {
	var description, iconURL, subtype string
	var amount int64
	var loanNames []string
	if transactionType == "DRAWDOWN" {
		description = "Drawdown for Europe Trip"
		iconURL = "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png"
		amount = -1512
		subtype = "RPP_NETWORK"
		loanNames = []string{"Europe Trip"}
	} else {
		if status == "FAILED" {
			description = "Payment for drawdowns"
		} else {
			description = "Payment for Europe Trip"
			loanNames = []string{"Europe Trip"}
		}

		iconURL = "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png"
		amount = 1512
		subtype = "INTRABANK"
	}
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc123efg",
			Description:   description,
			IconURL:       iconURL,
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          amount,
			},
			LoanNames:            loanNames,
			Status:               status,
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      transactionType,
			TransactionSubtype:   subtype,
		}},
	}
}

// GetRepaymentProcessingTransactionSearchResultsForLending ...
// nolint : dupl
func GetRepaymentProcessingTransactionSearchResultsForLending(transactionType string, status string) *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc123efg",
			Description:   "Payment for drawdowns",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          1512,
			},
			Status:               status,
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      transactionType,
			TransactionSubtype:   "INTRABANK",
		}},
	}
}

// GetAllTransactionsOnlyPageResponseForLendingWithNoDateFilters ...
// nolint : dupl
func GetAllTransactionsOnlyPageResponseForLendingWithNoDateFilters() *api.GetLendingTransactionSearchResponse {
	return &api.GetLendingTransactionSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.LendingTransactionSearchData{{
			TransactionID: "abc123efg",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -13,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		}, {
			TransactionID: "efg123abd",
			Description:   "Drawdown for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          -513,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "DRAWDOWN",
			TransactionSubtype:   "INTRABANK",
			LoanNames:            []string{"Europe Trip"},
		}, {
			TransactionID: "efg1111abd",
			Description:   "Payment for Europe Trip",
			IconURL:       "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          1512,
			},
			Status:               "COMPLETED",
			TransactionTimestamp: time.Unix(**********, 0).UTC(),
			TransactionType:      "REPAYMENT",
			TransactionSubtype:   "RPP_NETWORK",
			LoanNames:            []string{"Europe Trip"},
		}},
	}
}
