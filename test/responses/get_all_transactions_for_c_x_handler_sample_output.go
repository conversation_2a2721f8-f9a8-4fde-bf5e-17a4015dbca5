package responses

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
)

// GetAllTransactionsForCxFirstPageResponse ...
// nolint: funlen
func GetAllTransactionsForCxFirstPageResponse(includeTxnCode bool) *dto.TransactionsHistorySearchResponse {
	response := &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "abc123efg",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -13,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 +513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}

	if includeTxnCode {
		response.Data[0].TransactionCode = &dto.TransactionCode{
			Domain:  "DEPOSITS",
			Type:    "TRANSFER_MONEY",
			SubType: "INTRABANK",
		}

		response.Data[1].TransactionCode = &dto.TransactionCode{
			Domain:  "DEPOSITS",
			Type:    "TRANSFER_MONEY",
			SubType: "INTRABANK",
		}
	}

	return response
}

// EarmarkGetAllTransactionsForCxFirstPageResponse ...
func EarmarkGetAllTransactionsForCxFirstPageResponse(includeTxnCode bool) *dto.TransactionsHistorySearchResponse {
	response := &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "abc123efg",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -13,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "",
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 +513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
			},
		}},
	}

	if includeTxnCode {
		response.Data[0].TransactionCode = &dto.TransactionCode{
			Domain:  "DEPOSITS",
			Type:    "APPLY_EARMARK",
			SubType: "BANK_INITIATED",
		}

		response.Data[1].TransactionCode = &dto.TransactionCode{
			Domain:  "DEPOSITS",
			Type:    "TRANSFER_MONEY",
			SubType: "INTRABANK",
		}
	}

	return response
}

// GetAllTransactionsLastPageCXResponse ...
// nolint: dupl
func GetAllTransactionsLastPageCXResponse() *api.GetTransactionsHistoryCXResponse {
	return &api.GetTransactionsHistoryCXResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
		},
		Data: []api.TransactionHistoryCXResponse{{
			TransactionID:                  "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                        "efg1111abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         1512,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " from UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetAllTransactionsOnlyPageCXResponse ...
// nolint : dupl
func GetAllTransactionsOnlyPageCXResponse() *api.GetTransactionsHistoryCXResponse {
	return &api.GetTransactionsHistoryCXResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/12345/transactions?pageSize=3&startingBefore=MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryCXResponse{{
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                        "abc123efg",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         -13,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                        "efg123abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         513,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:                  "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                        "efg1111abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         1512,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " from UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetAllTransactionsNoNextPageResponse ...
// nolint: dupl, funlen
func GetAllTransactionsNoNextPageResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "abc123efg",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -13,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                "efg1111abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 1512,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " from UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetMooMooTransactionsResponse ...
// nolint: dupl, funlen
func GetMooMooTransactionsResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "abc123efg",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 13,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "Withdrawal from Moomoo",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "CASH_OUT",
				SubType: "MOOMOO",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "",
				DisplayName:   "Moomoo",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "",
					DisplayName:   "Moomoo",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetAllTransactionsOnlyOnePageCXResponse ...
// nolint: dupl, funlen
func GetAllTransactionsOnlyOnePageCXResponse() *api.GetTransactionsHistoryCXResponse {
	return &api.GetTransactionsHistoryCXResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=5&startingBefore=MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryCXResponse{{
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                        "abc123efg",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         -13,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                        "efg123abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         513,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:                  "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                        "efg1111abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         1512,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			TransactionType:                "",
			TransactionCode: &api.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " from UserNo2",
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetAllTransactionsBackwardScrollingPrevPageForCxResponse ...
// nolint: dupl,funlen
func GetAllTransactionsBackwardScrollingPrevPageForCxResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "abc123efg",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -13,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetAllTransactionsPrevNextBothExistForCxResponse ...
// nolint: dupl
func GetAllTransactionsPrevNextBothExistForCxResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " to UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetTransactionsWithCardForCxResponse ...
// nolint: dupl,funlen
func GetTransactionsWithCardForCxResponse() *dto.TransactionsHistorySearchResponse {
	settlementDate := time.Unix(**********, 0).UTC()
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                        "efg123abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         -513,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: -100,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			IsQR:                           false,
			CreationTimestamp:              time.Unix(**********, 0).UTC(),
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEBIT_CARD",
				Type:    "SPEND_CARD_PRESENT",
				SubType: "PAYNET_MYDEBIT",
			},
			CounterParty: &dto.CounterParty{
				DisplayName: "MerchantDescription",
			},
			CounterParties: []dto.CounterParty{
				{
					DisplayName: "MerchantDescription",
				},
			},
			CardTransactionDetail: &dto.CxCardTransactionDetail{
				CardID:         "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
				TailCardNumber: "••9350",
				SettlementDate: &settlementDate,
			},
		}, {
			TransactionID:                  "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                        "efg1111abd",
			IconURL:                        constants.IconURLMap["DefaultTransaction"],
			Amount:                         1512,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			Status:                         "COMPLETED",
			Currency:                       "MYR",
			IsQR:                           false,
			CreationTimestamp:              time.Unix(**********, 0).UTC(),
			TransactionDescription:         " from UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetDefaultResponseForCardTransactionsWithoutCardDetail ...
// nolint: dupl,funlen
func GetDefaultResponseForCardTransactionsWithoutCardDetail() *dto.TransactionsHistorySearchResponse {
	//settlementDate := time.Unix(**********, 0).UTC()
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			IconURL:           constants.IconURLMap["DefaultTransaction"],
			Amount:            -513,
			Status:            "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			Currency:          "MYR",
			IsQR:              false,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEBIT_CARD",
				Type:    "SPEND_CARD_PRESENT",
				SubType: "PAYNET_MYDEBIT",
			},
			CounterParty: &dto.CounterParty{
				DisplayName: "",
			},
			CardTransactionDetail: &dto.CxCardTransactionDetail{},
		}, {
			TransactionID:          "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                "efg1111abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 1512,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// PocketTransactionsOrder ...
// nolint:dupl
func PocketTransactionsOrder() []*storage.TransactionsData {
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	batchDetails2, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Paris1", "destination_display_name": "MAIN_ACCOUNT"})
	return []*storage.TransactionsData{
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// FilteredAuthAndSettlementTxns ...
// nolint:dupl, funlen
func FilteredAuthAndSettlementTxns() []*storage.TransactionsData {
	return []*storage.TransactionsData{
		{
			ID:                          3,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB3",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
		{
			ID:                          2,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB2",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "CB1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "4",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
	}
}

// GetTransactionsWithRewardsForCxResponse ...
// nolint: dupl, funlen
func GetTransactionsWithRewardsForCxResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg123abdee",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "Grab Unlimited Cashback",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEBIT_CARD",
				Type:    "REWARDS_CASHBACK",
				SubType: "BANK_INITIATED",
			},
			CounterParty: &dto.CounterParty{
				AccountNumber: "",
				DisplayName:   "Grab Unlimited Cashback",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					AccountNumber: "",
					DisplayName:   "Grab Unlimited Cashback",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                "efg1111abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -1512,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: " from UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg1234abdee",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 490,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "Grab Unlimited Cashback",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "REWARDS_CASHBACK",
				SubType: "BANK_INITIATED",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "",
				DisplayName:   "Grab Unlimited Cashback",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "",
					DisplayName:   "Grab Unlimited Cashback",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}

// GetTransactionsWithEarmarkForCxResponse ...
// nolint: dupl, funlen
func GetTransactionsWithEarmarkForCxResponse() *dto.TransactionsHistorySearchResponse {
	return &dto.TransactionsHistorySearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []dto.TransactionHistorySearchData{{
			TransactionID:          "9007e9c10ea841c593bee2e65df599edf",
			BatchID:                "efg123abde",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -13,
			Status:                 "CANCELED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "On hold amount",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "RELEASE_EARMARK",
				SubType: "BANK_INITIATED",
			},
			CounterParty: &dto.CounterParty{
				AccountNumber: "",
				DisplayName:   "On hold amount",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					AccountNumber: "",
					DisplayName:   "On hold amount",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
			BatchID:                "efg1111abd",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 513,
			Status:                 "COMPLETED",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from UserNo2",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "TRANSFER_MONEY",
				SubType: "INTRABANK",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "UserNo2",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "UserNo2",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}, {
			TransactionID:          "8007e9c10ea841c593bee2e65df599ef",
			BatchID:                "efg1234abde",
			IconURL:                constants.IconURLMap["DefaultTransaction"],
			Amount:                 -1512,
			Status:                 "PROCESSING",
			Currency:               "MYR",
			IsQR:                   false,
			CreationTimestamp:      time.Unix(**********, 0).UTC(),
			TransactionDescription: "On hold amount",
			TransactionCode: &dto.TransactionCode{
				Domain:  "DEPOSITS",
				Type:    "APPLY_EARMARK",
				SubType: "BANK_INITIATED",
			},
			CounterParty: &dto.CounterParty{
				SwiftCode:     "",
				AccountNumber: "",
				DisplayName:   "On hold amount",
				TransactionDetails: map[string]string{
					"service_type": "",
				},
			},
			CounterParties: []dto.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "",
					DisplayName:   "On hold amount",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
			},
		}},
	}
}
