package responses

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	testUtils "gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"

	accountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

var currMon = time.Now().Format(utils.TimestampMonthFormat)

// GetCASATransactionsSummaryValidResponse ...
func GetCASATransactionsSummaryValidResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          15,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          60,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          12,
			},
		},
	}
}

// GetCASATransactionsSummaryValidResponse2 ...
func GetCASATransactionsSummaryValidResponse2() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          15,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          72,
			},
		},
	}
}

// GetCASATransactionsSummaryNoInterestResponse  ...
func GetCASATransactionsSummaryNoInterestResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          60,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          12,
			},
		},
	}
}

// GetCASATransactionsSummaryNoTransferResponse  ...
func GetCASATransactionsSummaryNoTransferResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          15,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
	}
}

// GetCASATransactionsSummaryEmptyResponses  ...
func GetCASATransactionsSummaryEmptyResponses() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidResponse ...
func DBMYGetCASATransactionsSummaryValidResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          45,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          60,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          12,
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidResponse2 ...
func DBMYGetCASATransactionsSummaryValidResponse2() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          45,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          15000,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          0,
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidWithRewardsResponse ...
func DBMYGetCASATransactionsSummaryValidWithRewardsResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          45,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          20000,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          5000,
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidWithCardTxnResponse ...
func DBMYGetCASATransactionsSummaryValidWithCardTxnResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          45,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          1560,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          612,
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidWithCardRefundTxnResponse ...
func DBMYGetCASATransactionsSummaryValidWithCardRefundTxnResponse() []*api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return []*api.GetCASATransactionsSummaryResponse{
		{
			TotalInterestEarned: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          45,
			},
			CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
				Month: currMon,
				MoneySpent: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          15800,
				},
			},
			CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
				Month: currMon,
				MoneyReceived: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          6800,
				},
			},
		},
		{
			TotalInterestEarned: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          45,
			},
			CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
				Month: currMon,
				MoneySpent: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          15100,
				},
			},
			CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
				Month: currMon,
				MoneyReceived: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          5100,
				},
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidWithCardOpsResponse ...
func DBMYGetCASATransactionsSummaryValidWithCardOpsResponse() []*api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return []*api.GetCASATransactionsSummaryResponse{
		{
			TotalInterestEarned: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          45,
			},
			CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
				Month: currMon,
				MoneySpent: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          20000,
				},
			},
			CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
				Month: currMon,
				MoneyReceived: &api.Money{
					CurrencyCode: locale.Currency,
					Val:          13000,
				},
			},
		},
	}
}

// DBMYGetCASATransactionsSummaryValidWithLoanResponse ...
func DBMYGetCASATransactionsSummaryValidWithLoanResponse() *api.GetCASATransactionsSummaryResponse {
	locale := testUtils.GetLocale()
	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          45,
		},
		CurrentMonthMoneySpent: &api.CurrentMonthMoneySpent{
			Month: currMon,
			MoneySpent: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          20800,
			},
		},
		CurrentMonthMoneyReceived: &api.CurrentMonthMoneyReceived{
			Month: currMon,
			MoneyReceived: &api.Money{
				CurrencyCode: locale.Currency,
				Val:          5800,
			},
		},
	}
}

// DBMYListCASAAccountsForCustomerDetailResponse ...
func DBMYListCASAAccountsForCustomerDetailResponse() *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse {
	return &accountServiceAPI.ListCASAAccountsForCustomerDetailResponse{
		Accounts: []accountServiceAPI.CASAAccountDetail{
			{
				Id:     "**********",
				Status: accountServiceAPI.AccountStatus_ACTIVE,
			}, {
				Id:     "**********001",
				Status: accountServiceAPI.AccountStatus_ACTIVE,
			}, {
				Id:     "**********002",
				Status: accountServiceAPI.AccountStatus_CLOSED,
			}, {
				Id:     "*************",
				Status: accountServiceAPI.AccountStatus_ACTIVE,
			},
		},
	}
}
