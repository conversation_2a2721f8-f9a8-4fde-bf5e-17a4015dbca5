package responses

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func getTestTransactionsData() *storage.TransactionsData {
	batchDetail, _ := json.Marshal(nil)
	txDetail, _ := json.Marshal(nil)
	violations, _ := json.Marshal(nil)

	return &storage.TransactionsData{
		TmTransactionID:             "cd5e5261-c827-4316-ac42-87f61732c43e",
		ClientTransactionID:         "33141f096c8d4492849d8b62815d3232",
		TransactionDetails:          txDetail,
		TransactionViolations:       violations,
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		TmPostingInstructionBatchID: "23aa2045-4ea7-422f-b72f-6e8b44931009",
		ClientBatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		BatchDetails:                batchDetail,
		BatchValueTimestamp:         time.Unix(1629426700, 0).UTC(),
		BatchInsertionTimestamp:     time.Unix(1629426700, 0).UTC(),
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchErrorType:              "",
		BatchErrorMessage:           "",
		BatchRemarks:                "Happy Payment Remarks",
		TmTransactionType:           "SETTLEMENT",
	}
}
