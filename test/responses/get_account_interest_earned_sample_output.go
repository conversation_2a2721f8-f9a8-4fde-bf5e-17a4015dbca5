package responses

import (
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// GetCASAInterestEarnedValidResponse ...
func GetCASAInterestEarnedValidResponse() *api.GetCASAInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          10,
		},
	}
}

// GetCASAInterestEarnedEmptyResponse ...
func GetCASAInterestEarnedEmptyResponse() *api.GetCASAInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
	}
}
