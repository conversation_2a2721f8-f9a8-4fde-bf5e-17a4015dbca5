package responses

import (
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// GetPocketInterestEarnedValidResponse ...
func GetPocketInterestEarnedValidResponse() *api.GetPocketInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetPocketInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          5,
		},
	}
}

// GetPocketInterestEarnedEmptyResponse ...
func GetPocketInterestEarnedEmptyResponse() *api.GetPocketInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetPocketInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
	}
}
