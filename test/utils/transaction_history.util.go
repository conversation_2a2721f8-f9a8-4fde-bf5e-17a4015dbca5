// Package utils provides utils support.
package utils

import (
	"encoding/json"
	"testing"

	"golang.org/x/text/language"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/common/currency"
	"gitlab.myteksi.net/dakota/common/tenants"
)

// GetLocale ...
func GetLocale() config.Locale {
	if config.GetTenant() == tenants.TenantMY {
		return config.Locale{
			Currency: currency.MYR.Code,
			Language: language.English.String(),
		}
	}
	return config.Locale{
		Currency: currency.IDR.Code,
		Language: language.Indonesian.String(),
	}
}

// CompareExpectationJSON prints out the expected and actual JSON and compares them.
// This is useful for debugging in the diff view generated by IDE.
func CompareExpectationJSON(t *testing.T, expected, actual any) {
	expectedJSON, err := json.MarshalIndent(expected, "", "  ")
	require.NoError(t, err)
	actualJSON, err := json.MarshalIndent(actual, "", "  ")
	require.NoError(t, err)
	assert.Equal(t, string(expectedJSON), string(actualJSON))
}
