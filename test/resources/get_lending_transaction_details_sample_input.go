package resources

import (
	"encoding/json"
	"time"

	"github.com/shopspring/decimal"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// LoanDetailsMockDBRows ...
// nolint:dupl
func LoanDetailsMockDBRows() []*storage.LoanDetail {
	statusDetails, _ := json.Marshal(map[string]string{"Reason": "Dummy reason", "Description": "Dummy description"})
	drawDownDetails := fetchDrawDownDetailsMock()
	repaymentDetails := fetchRepaymentDetailsMock()
	overPaidRepaymentDetails := fetchOverPaidRepaymentDetailsMock()
	return []*storage.LoanDetail{{
		ID:                   1,
		LoanTransactionID:    "12345",
		Amount:               1300,
		Currency:             "MYR",
		AccountID:            "",
		AccountDetail:        nil,
		PaymentTransactionID: "abc123efg",
		TransactionDomain:    "LENDING",
		TransactionType:      "DRAWDOWN",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   drawDownDetails,
		RepaymentDetail:      nil,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	},
		{
			ID:                   2,
			LoanTransactionID:    "12345",
			Amount:               13400,
			Currency:             "MYR",
			AccountID:            "",
			AccountDetail:        nil,
			PaymentTransactionID: "efg1111abd",
			TransactionDomain:    "LENDING",
			TransactionType:      "REPAYMENT",
			TransactionSubType:   "INTRABANK",
			Status:               "COMPLETED",
			StatusDetail:         statusDetails,
			DisbursementDetail:   nil,
			RepaymentDetail:      overPaidRepaymentDetails,
			CreatedAt:            time.Unix(**********, 0).UTC(),
			UpdatedAt:            time.Unix(**********, 0).UTC(),
		},
		{
			ID:                   3,
			LoanTransactionID:    "12345",
			Amount:               13400,
			Currency:             "MYR",
			AccountID:            "",
			AccountDetail:        nil,
			PaymentTransactionID: "efg123abd",
			TransactionDomain:    "LENDING",
			TransactionType:      "REPAYMENT",
			TransactionSubType:   "INTRABANK",
			Status:               "COMPLETED",
			StatusDetail:         statusDetails,
			DisbursementDetail:   nil,
			RepaymentDetail:      repaymentDetails,
			CreatedAt:            time.Unix(**********, 0).UTC(),
			UpdatedAt:            time.Unix(**********, 0).UTC(),
		},
	}
}

// nolint:dupl
func fetchDrawDownDetailsMock() []byte {
	details, _ := json.Marshal(dto.DrawdownDetailsDTO{
		LoanName:                "Europe Trip",
		LoanTenorInMonths:       10,
		FirstInstallmentDueDate: "2023-02-07",
		LastInstallmentDueDate:  "2023-06-07",
		TotalAmountDue:          decimal.NewFromFloat(204.35),
		Currency:                "MYR",
		TotalPrincipalDue:       decimal.NewFromInt(201),
		TotalNormalInterestDue:  decimal.NewFromFloat(3.35),
		AnnualPercentageRate:    decimal.NewFromFloat(3.5),
		EffectiveInterestRate:   decimal.NewFromFloat(5.3520566),
		RepaymentInstallment:    nil,
	})
	return details
}

// nolint:dupl
func fetchRepaymentDetailsMock() []byte {
	details, _ := json.Marshal(dto.RepaymentDetailsDTO{
		TotalRepaymentAmount:        decimal.NewFromFloat(5000),
		IsTotalInterestSaved:        true,
		TotalInterestSaved:          decimal.NewFromFloat(300),
		IsTotalPenalInterestCharged: false,
		PenalInterestCharged:        decimal.NewFromFloat(0),
		LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
			LoanName:                "Europe Trip",
			AccountID:               "12345",
			RepaymentAmount:         decimal.NewFromFloat(3000),
			Currency:                "",
			TotalDueBeforeRepayment: decimal.NewFromFloat(3000),
			TotalDueAfterRepayment:  decimal.NewFromFloat(2000),
			IsNormalInterestSaved:   true,
			NormalInterestSave:      decimal.NewFromFloat(300),
			IsPenalInterestCharged:  false,
			PenalInterestCharged:    decimal.NewFromFloat(0),
			ValueTimestamp:          time.Unix(**********, 0).UTC(),
		},
		},
		Currency: "MYR",
	})
	return details
}

// nolint:dupl
func fetchFailedRepaymentDetailsMock() []byte {
	details, _ := json.Marshal(dto.RepaymentDetailsDTO{
		TotalRepaymentAmount:        decimal.NewFromFloat(5000),
		IsTotalInterestSaved:        false,
		TotalInterestSaved:          decimal.NewFromFloat(0),
		IsTotalPenalInterestCharged: false,
		PenalInterestCharged:        decimal.NewFromFloat(0),
		LoanRepaymentDetail:         nil,
		Currency:                    "MYR",
	})
	return details
}

func fetchRecoveryDetailsMock() []byte {
	details, _ := json.Marshal(dto.RepaymentDetailsDTO{
		TotalRepaymentAmount:        decimal.NewFromFloat(5000),
		IsTotalInterestSaved:        true,
		TotalInterestSaved:          decimal.NewFromFloat(300),
		IsTotalPenalInterestCharged: false,
		PenalInterestCharged:        decimal.NewFromFloat(0),
		LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
			LoanName:                "Europe trip",
			AccountID:               "12345",
			RepaymentAmount:         decimal.NewFromFloat(3000),
			Currency:                "",
			TotalDueBeforeRepayment: decimal.NewFromFloat(3000),
			TotalDueAfterRepayment:  decimal.NewFromFloat(2000),
			IsNormalInterestSaved:   true,
			NormalInterestSave:      decimal.NewFromFloat(300),
			IsPenalInterestCharged:  false,
			PenalInterestCharged:    decimal.NewFromFloat(0),
			Status:                  "WRITTEN_OFF",
			SubStatus:               "BAD_DEBT",
			ValueTimestamp:          time.Unix(**********, 0).UTC(),
		},
		},
		Currency: "MYR",
	})
	return details
}

// nolint:dupl
func fetchOverPaidRepaymentDetailsMock() []byte {
	details, _ := json.Marshal(dto.RepaymentDetailsDTO{
		TotalRepaymentAmount:        decimal.NewFromFloat(3000),
		IsTotalInterestSaved:        true,
		TotalInterestSaved:          decimal.NewFromFloat(300),
		IsTotalPenalInterestCharged: true,
		PenalInterestCharged:        decimal.NewFromFloat(100),
		LoanRepaymentDetail: []dto.LoanRepaymentDetailDTO{{
			LoanName:                "Europe trip",
			AccountID:               "12345",
			RepaymentAmount:         decimal.NewFromFloat(5000),
			Currency:                "",
			TotalDueBeforeRepayment: decimal.NewFromFloat(3000),
			TotalDueAfterRepayment:  decimal.NewFromFloat(2000),
			IsNormalInterestSaved:   true,
			NormalInterestSave:      decimal.NewFromFloat(300),
			IsPenalInterestCharged:  true,
			PenalInterestCharged:    decimal.NewFromFloat(100),
		},
		},
		Currency: "MYR",
	})
	return details
}
