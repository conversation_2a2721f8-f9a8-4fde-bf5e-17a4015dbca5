package resources

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// TransferTransactionsDBMockRows ...
// nolint:dupl,funlen
func TransferTransactionsDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	txnDetails1, _ := json.<PERSON>(map[string]string{"original_transaction_id": "clienttransactionID5"})
	txnDetails2, _ := json.<PERSON>(map[string]string{"original_transaction_id": "clienttransactionID6"})
	return []*storage.TransactionsData{{ID: 5,
		ClientTransactionID:     "clienttransactionID5",
		ClientBatchID:           "clientbatchid5",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "SEND_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_COMMITTED",
		DebitOrCredit:           "debit",
		TransactionAmount:       "0.5",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	}, {ID: 6,
		ClientTransactionID:     "clienttransactionID6",
		ClientBatchID:           "clientbatchid6",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "TRANSFER_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_COMMITTED",
		DebitOrCredit:           "credit",
		TransactionAmount:       "0.2",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	},
		{ID: 7,
			ClientTransactionID:     "clienttransactionID7",
			ClientBatchID:           "clientbatchid7",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "SEND_MONEY_REVERSAL",
			TransactionDetails:      txnDetails1,
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.5",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 8,
			ClientTransactionID:     "clienttransactionID8",
			ClientBatchID:           "clientbatchid8",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "RECEIVE_MONEY_REVERSAL",
			TransactionDetails:      txnDetails2,
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "0.2",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 9,
			ClientTransactionID:     "clienttransactionID9",
			ClientBatchID:           "clientbatchid9",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "SEND_MONEY",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "0.6",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 10,
			ClientTransactionID:     "clienttransactionID10",
			ClientBatchID:           "clientbatchid10",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "RECEIVE_MONEY",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.12",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYTransferTransactionsDBMockRows ...
// nolint:dupl,funlen
func DBMYTransferTransactionsDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 5,
		ClientTransactionID:     "clienttransactionID5",
		ClientBatchID:           "clientbatchid5",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "SEND_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_COMMITTED",
		DebitOrCredit:           "debit",
		TransactionAmount:       "0.5",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	}, {ID: 6,
		ClientTransactionID:     "clienttransactionID6",
		ClientBatchID:           "clientbatchid6",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "TRANSFER_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_COMMITTED",
		DebitOrCredit:           "credit",
		TransactionAmount:       "0.2",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	},
		{ID: 7,
			ClientTransactionID:     "clienttransactionID7",
			ClientBatchID:           "clientbatchid7",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "SEND_MONEY_REVERSAL",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.5",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 8,
			ClientTransactionID:     "clienttransactionID8",
			ClientBatchID:           "clientbatchid8",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "RECEIVE_MONEY_REVERSAL",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "0.2",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 9,
			ClientTransactionID:     "clienttransactionID9",
			ClientBatchID:           "clientbatchid9",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "SEND_MONEY",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "0.6",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 10,
			ClientTransactionID:     "clienttransactionID10",
			ClientBatchID:           "clientbatchid10",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "RECEIVE_MONEY",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.12",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 11,
			ClientTransactionID:     "clienttransactionID11",
			ClientBatchID:           "clientbatchid11",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.SpendCardPresentTransactionType,
			TransactionSubtype:      constants.MastercardTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "20.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 12,
			ClientTransactionID:     "clienttransactionID12",
			ClientBatchID:           "clientbatchid12",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.SpendCardAtmTransactionType,
			TransactionSubtype:      constants.MastercardTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "1.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 13,
			ClientTransactionID:     "clienttransactionID13",
			ClientBatchID:           "clientbatchid13",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.SpendRefundTransactionType,
			TransactionSubtype:      constants.MastercardTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "20.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      14,
			ClientTransactionID:     "clienttransactionID14",
			ClientBatchID:           "clientbatchid14",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DepositsDomain,
			TransactionType:         constants.UnclaimedMoniesTransactionType,
			TransactionSubtype:      constants.OPSTransactionSubType,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "2.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// TransferPaymentDetailDBMockRows ...
// nolint: funlen
func TransferPaymentDetailDBMockRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	metadata1, _ := json.Marshal(map[string]string{"original_transaction_id": "clientbatchid5"})
	metadata2, _ := json.Marshal(map[string]string{"original_transaction_id": "clientbatchid6"})
	return []*storage.PaymentDetail{
		{
			ID:                 5,
			TransactionID:      "clientbatchid5",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "SEND_MONEY",
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             -50,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 6,
			TransactionID:      "clientbatchid6",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "TRANSFER_MONEY",
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             20,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 7,
			TransactionID:      "clientbatchid7",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "SEND_MONEY_REVERSAL",
			Metadata:           metadata1,
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             50,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 8,
			TransactionID:      "clientbatchid8",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "RECEIVE_MONEY_REVERSAL",
			Metadata:           metadata2,
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             -20,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 9,
			TransactionID:      "clientbatchid9",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "SEND_MONEY",
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             -60,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 10,
			TransactionID:      "clientbatchid10",
			TransactionDomain:  "DEPOSITS",
			TransactionType:    "RECEIVE_MONEY",
			TransactionSubType: "SAVINGS",
			AccountID:          "**********",
			Amount:             12,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
	}
}

// TransferCardDetailDBMockRows ...
// nolint: funlen
func TransferCardDetailDBMockRows() []*storage.CardTransactionDetail {
	locale := utils.GetLocale()
	return []*storage.CardTransactionDetail{
		{
			ID:                 5,
			CardTransactionID:  "clientbatchid11",
			TransactionDomain:  constants.DebitCardDomain,
			TransactionType:    constants.SpendCardPresentTransactionType,
			TransactionSubType: constants.MastercardTransactionSubtype,
			AccountID:          "**********",
			Amount:             -2000,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 6,
			CardTransactionID:  "clientbatchid12",
			TransactionDomain:  constants.DebitCardDomain,
			TransactionType:    constants.SpendCardAtmTransactionType,
			TransactionSubType: constants.MastercardTransactionSubtype,
			AccountID:          "**********",
			Amount:             -100,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                 7,
			CardTransactionID:  "clientbatchid13",
			TransactionDomain:  constants.DebitCardDomain,
			TransactionType:    constants.SpendRefundTransactionType,
			TransactionSubType: constants.MastercardTransactionSubtype,
			Metadata:           json.RawMessage("{\"OriginalChargeID\": \"clientbatchid11\"}"),
			AccountID:          "**********",
			Amount:             2000,
			Currency:           locale.Currency,
			CreationTimestamp:  time.Now().UTC(),
			ValueTimestamp:     time.Now().UTC(),
		},
	}
}

func CardIssuanceFeeCancelledBatchDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      100,
			ClientTransactionID:     "card-issuance-fee-clienttransactionID-1",
			ClientBatchID:           "card-issuance-fee-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.NewCardIssuanceFeeTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			TmTransactionType:       constants.OutboundAuthorisation,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            constants.PostingPhasePendingOutgoing,
			DebitOrCredit:           "debit",
			TransactionAmount:       "4.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
		{
			ID:                      101,
			ClientTransactionID:     "card-issuance-fee-clienttransactionID-1",
			ClientBatchID:           "card-issuance-fee-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.NewCardIssuanceFeeTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			TmTransactionType:       constants.Release,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            constants.PostingPhasePendingOutgoing,
			DebitOrCredit:           "credit",
			TransactionAmount:       "4.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
	}
}

// CardMaintenanceFeeTransactionsDBMockRows ...
func CardMaintenanceFeeTransactionsDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      100,
			ClientTransactionID:     "card-issuance-fee-clienttransactionID-1",
			ClientBatchID:           "card-issuance-fee-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.NewCardIssuanceFeeTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "4.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
		{
			ID:                      101,
			ClientTransactionID:     "card-annual-fee-clienttransactionID-2",
			ClientBatchID:           "card-annual-fee-clientbatchid-2",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.CardAnnualFeeTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "4.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
		{
			ID:                      102,
			ClientTransactionID:     "card-replacement-fee-clienttransactionID-1",
			ClientBatchID:           "card-replacement-fee-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.CardReplacementFeeTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "debit",
			TransactionAmount:       "4.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
	}
}

// CardMaintenanceFeeWaiverTransactionsDBMockRows ...
func CardMaintenanceFeeWaiverTransactionsDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      103,
			ClientTransactionID:     "card-issuance-fee-waiver-clienttransactionID-",
			ClientBatchID:           "card-issuance-fee-waiver-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.NewCardIssuanceFeeWaiverTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "2.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
		{
			ID:                      104,
			ClientTransactionID:     "card-annual-fee-waiver-clienttransactionID-2",
			ClientBatchID:           "card-annual-fee-waiver-clientbatchid-2",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.CardAnnualFeeWaiverTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "2.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
			TransactionDetails:      json.RawMessage("{\"cardID\": \"card-id\", \"tailCardNumber\": \"1234\"}"),
		},
		{
			ID:                      105,
			ClientTransactionID:     "card-replacement-fee-waiver-clienttransactionID-1",
			ClientBatchID:           "card-replacement-fee-waiver-clientbatchid-1",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       constants.DebitCardDomain,
			TransactionType:         constants.CardReplacementFeeWaiverTransactionType,
			TransactionSubtype:      constants.CardBankInitiatedTransactionSubtype,
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "2.00",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// PendingTransactionsDBMockRows ...
//
//nolint:dupl,funlen
func PendingTransactionsDBMockRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 5,
		ClientTransactionID:     "clienttransactionID05",
		ClientBatchID:           "clientbatchid05",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "SEND_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_PENDING_OUTGOING",
		TmTransactionType:       "OUTBOUND_AUTHORISATION",
		DebitOrCredit:           "debit",
		TransactionAmount:       "0.5",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	}, {ID: 6,
		ClientTransactionID:     "clienttransactionID06",
		ClientBatchID:           "clientbatchid06",
		BatchStatus:             "ACCEPTED",
		TransactionDomain:       "DEPOSITS",
		TransactionType:         "TRANSFER_MONEY",
		TransactionSubtype:      "SAVINGS",
		AccountID:               "**********",
		AccountAddress:          "DEFAULT",
		AccountPhase:            "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:           "credit",
		TransactionAmount:       "0.2",
		TransactionCurrency:     locale.Currency,
		BatchInsertionTimestamp: time.Now().UTC(),
		BatchValueTimestamp:     time.Now().UTC(),
	},
		{ID: 9,
			ClientTransactionID:     "clienttransactionID09",
			ClientBatchID:           "clientbatchid09",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "FINANCE",
			TransactionType:         "ADJUSTMENT",
			TransactionSubtype:      "BANK_INITIATED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.6",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{ID: 10,
			ClientTransactionID:     "clienttransactionID010",
			ClientBatchID:           "clientbatchid010",
			BatchStatus:             "ACCEPTED",
			TransactionDomain:       "DEPOSITS",
			TransactionType:         "RECEIVE_MONEY",
			TransactionSubtype:      "SAVINGS",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			DebitOrCredit:           "credit",
			TransactionAmount:       "0.12",
			TransactionCurrency:     locale.Currency,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// InterestTransactionsDBMockRows ...
//
//nolint:dupl
func InterestTransactionsDBMockRows() []*storage.InterestAggregate {
	locale := utils.GetLocale()
	return []*storage.InterestAggregate{
		{ID: 1,
			AccountID:           "**********",
			AccountAddress:      "DEFAULT",
			TotalInterestEarned: 10,
			Currency:            locale.Currency,
		}, {ID: 2,
			AccountID:           "**********",
			AccountAddress:      "**********000",
			TotalInterestEarned: 5,
			Currency:            locale.Currency,
		},
	}
}

// PocketInterestTransactionsDBMockRows ...
//
//nolint:dupl
func PocketInterestTransactionsDBMockRows() []*storage.InterestAggregate {
	locale := utils.GetLocale()
	return []*storage.InterestAggregate{
		{ID: 2,
			AccountID:           "**********",
			AccountAddress:      "**********000",
			TotalInterestEarned: 5,
			Currency:            locale.Currency,
		},
	}
}

// DBMYInterestTransactionsDBMockRows ...
//
//nolint:dupl
func DBMYInterestTransactionsDBMockRows() []*storage.InterestAggregate {
	locale := utils.GetLocale()
	return []*storage.InterestAggregate{
		{ID: 1,
			AccountID:           "**********",
			AccountAddress:      "DEFAULT",
			TotalInterestEarned: 10,
			Currency:            locale.Currency,
		}, {ID: 2,
			AccountID:           "**********001",
			AccountAddress:      "DEFAULT",
			TotalInterestEarned: 5,
			Currency:            locale.Currency,
		}, {ID: 3,
			AccountID:           "**********002",
			AccountAddress:      "DEFAULT",
			TotalInterestEarned: 25,
			Currency:            locale.Currency,
		}, {ID: 4,
			AccountID:           "**********003",
			AccountAddress:      "DEFAULT",
			TotalInterestEarned: 5,
			Currency:            locale.Currency,
		},
	}
}

// DBMYTransactionsDataDBRow ...
// nolint: funlen
func DBMYTransactionsDataDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	td, _ := json.Marshal(map[string]string{"original_transaction_id": "batchID5"})
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SendMoneyTxType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ReceiveMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ReceiveMoneyRevTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      6,
			ClientTransactionID:     "txn6",
			ClientBatchID:           "batchID6",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendMoneyRevTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			TransactionDetails:      td,
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYPaymentDetailDBRow ...
func DBMYPaymentDetailDBRow() []*storage.PaymentDetail {
	metadata1, _ := json.Marshal(map[string]string{"original_transaction_id": "batchID3"})
	return []*storage.PaymentDetail{
		{
			ID:              1,
			TransactionID:   "batchID1",
			TransactionType: constants.TransferMoneyTxType,
			Amount:          -5000,
		},
		{
			ID:              2,
			TransactionID:   "batchID2",
			TransactionType: constants.SendMoneyTxType,
			Amount:          -10000,
		},
		{
			ID:              3,
			TransactionID:   "batchID3",
			TransactionType: constants.ReceiveMoneyTxType,
			Amount:          5000,
		},
		{
			ID:              4,
			TransactionID:   "batchID4",
			TransactionType: constants.ReceiveMoneyRevTxType,
			Amount:          -5000,
			Metadata:        metadata1,
		},
	}
}

// DBMYTransactionsDataWithRewardsDBRow ...
// nolint: funlen, dupl
func DBMYTransactionsDataWithRewardsDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SendMoneyTxType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ReceiveMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.RewardsCashback,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      6,
			ClientTransactionID:     "txn6",
			ClientBatchID:           "batchID6",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.RewardsCashbackReversal,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYPaymentDetailWithRewardsDBRow ...
func DBMYPaymentDetailWithRewardsDBRow() []*storage.PaymentDetail {
	return []*storage.PaymentDetail{
		{
			ID:              1,
			TransactionID:   "batchID1",
			TransactionType: constants.TransferMoneyTxType,
			Amount:          -5000,
		},
		{
			ID:              2,
			TransactionID:   "batchID2",
			TransactionType: constants.SendMoneyTxType,
			Amount:          -10000,
		},
		{
			ID:              3,
			TransactionID:   "batchID3",
			TransactionType: constants.ReceiveMoneyTxType,
			Amount:          5000,
		},
		{
			ID:              4,
			TransactionID:   "batchID4",
			TransactionType: constants.RewardsCashback,
			Amount:          800,
		},
		{
			ID:              6,
			TransactionID:   "batchID6",
			TransactionType: constants.RewardsCashbackReversal,
			Amount:          -800,
			Metadata:        json.RawMessage("{\"original_transaction_id\": \"batchID4\"}"),
		},
	}
}

// DBMYTransactionsDataWithCardTxnDBRow ...
// nolint: funlen, dupl
func DBMYTransactionsDataWithCardTxnDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SendMoneyTxType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ReceiveMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendCardNotPresentTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendRefundTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      6,
			ClientTransactionID:     "txn6",
			ClientBatchID:           "batchID6",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendRefundTransactionType,
			TransactionAmount:       "10",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYPaymentDetailWithCardTxnDBRow ...
func DBMYPaymentDetailWithCardTxnDBRow() []*storage.PaymentDetail {
	return []*storage.PaymentDetail{
		{
			ID:              1,
			TransactionID:   "batchID1",
			TransactionType: constants.TransferMoneyTxType,
			Amount:          -5000,
		},
		{
			ID:              2,
			TransactionID:   "batchID2",
			TransactionType: constants.SendMoneyTxType,
			Amount:          -10000,
		},
		{
			ID:              3,
			TransactionID:   "batchID3",
			TransactionType: constants.ReceiveMoneyTxType,
			Amount:          5000,
		},
	}
}

// DBMYCardTxnDetailDBRow ...
func DBMYCardTxnDetailDBRow() []*storage.CardTransactionDetail {
	return []*storage.CardTransactionDetail{
		{
			ID:                4,
			CardTransactionID: "batchID4",
			TransactionType:   constants.SpendCardNotPresentTransactionType,
			Amount:            -800,
			Metadata:          json.RawMessage("{}"),
		},
		{
			ID:                5,
			CardTransactionID: "batchID5",
			TransactionType:   constants.SpendRefundTransactionType,
			Amount:            800,
			Metadata:          json.RawMessage("{}"),
		},
		{
			ID:                6,
			CardTransactionID: "batchID6",
			TransactionType:   constants.SpendRefundTransactionType,
			Amount:            1000,
			Metadata:          json.RawMessage("{}"),
		},
	}
}

// DBMYTransactionsDataWithCardReversalTxnDBRow ...
// nolint: funlen, dupl
func DBMYTransactionsDataWithCardReversalTxnDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SendMoneyTxType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ReceiveMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SpendCardAtmTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.DomesticAtmFeeTransactionType,
			TransactionAmount:       "1",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      6,
			ClientTransactionID:     "txn6",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.DomesticAtmFeeWaiverTransactionType,
			TransactionAmount:       "1",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      7,
			ClientTransactionID:     "txn7",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ATMCashWithdrawalRefundTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYCardTxnDetailWithReversalTxnDBRow ...
func DBMYCardTxnDetailWithReversalTxnDBRow() []*storage.CardTransactionDetail {
	return []*storage.CardTransactionDetail{
		{
			ID:                4,
			CardTransactionID: "batchID4",
			TransactionType:   constants.SpendCardAtmTransactionType,
			Amount:            -800,
			Metadata:          json.RawMessage("{}"),
		},
		{
			ID:                5,
			CardTransactionID: "batchID5",
			TransactionType:   constants.ATMCashWithdrawalRefundTransactionType,
			Amount:            800,
			Metadata:          json.RawMessage("{\"OriginalChargeID\":\"batchID4\"}"),
		},
	}
}

// DBMYTransactionsDataWithCardOpsDBRow ...
// nolint: funlen, dupl
func DBMYTransactionsDataWithCardOpsDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.SendMoneyTxType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ProvisionalCreditTransactionType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.OperationalLossTransactionType,
			TransactionAmount:       "80",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.ProvisionalCreditReversalTransactionType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}

// DBMYTransactionsDataWithLoanDBRow ...
// nolint: funlen, dupl
func DBMYTransactionsDataWithLoanDBRow() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                      1,
			ClientTransactionID:     "txn1",
			ClientBatchID:           "batchID1",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.TransferMoneyTxType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      2,
			ClientTransactionID:     "txn2",
			ClientBatchID:           "batchID2",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.RepaymentTransactionType,
			TransactionAmount:       "100",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      3,
			ClientTransactionID:     "txn3",
			ClientBatchID:           "batchID3",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.DrawdownTransactionType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      4,
			ClientTransactionID:     "txn4",
			ClientBatchID:           "batchID4",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.DrawdownTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "credit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      5,
			ClientTransactionID:     "txn5",
			ClientBatchID:           "batchID5",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.WriteOffTransactionType,
			TransactionAmount:       "50",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
		{
			ID:                      6,
			ClientTransactionID:     "txn6",
			ClientBatchID:           "batchID6",
			BatchStatus:             "ACCEPTED",
			AccountID:               "**********",
			AccountAddress:          "DEFAULT",
			AccountPhase:            "POSTING_PHASE_COMMITTED",
			TransactionType:         constants.RepaymentTransactionType,
			TransactionAmount:       "8",
			TransactionCurrency:     locale.Currency,
			DebitOrCredit:           "debit",
			BatchInsertionTimestamp: time.Now().UTC(),
			BatchValueTimestamp:     time.Now().UTC(),
		},
	}
}
