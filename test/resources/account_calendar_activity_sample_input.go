package resources

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

// AccountCalendarActivityDBSampleResponse ...
func AccountCalendarActivityDBSampleResponse() []*storage.AccountCalendarActivity {
	return []*storage.AccountCalendarActivity{
		{
			ID:        3,
			AccountID: "********",
			Year:      2022,
			Months:    "01,02,11",
			UpdatedAt: time.Now(),
		}, {
			ID:        4,
			AccountID: "12345",
			Year:      2022,
			Months:    "01,06,11",
			UpdatedAt: time.Now(),
		}, {
			ID:        1,
			AccountID: "12345",
			Year:      2021,
			Months:    "11,10,09",
			UpdatedAt: time.Now(),
		}, {
			ID:        2,
			AccountID: "********",
			Year:      2021,
			Months:    "11,08,06",
			UpdatedAt: time.Now(),
		},
	}
}
