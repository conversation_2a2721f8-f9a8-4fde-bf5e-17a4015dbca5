package resources //nolint:dupl

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// GetAllTransactionsFirstPageMockDBResponse ...
func GetAllTransactionsFirstPageMockDBResponse() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRows()
	return rows
}

// GetAllTransactionsLastPageMockDBResponse ...
func GetAllTransactionsLastPageMockDBResponse() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRows()
	return rows[2:]
}

// GetAllLendingTransactionsFirstPageMockDBResponse ...
func GetAllLendingTransactionsFirstPageMockDBResponse() []*storage.TransactionsData {
	rows := LendingTransactionsDataMockDBRows()
	return rows
}

// TransactionsDataMockDBRows ...
// nolint:dupl
func TransactionsDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// TransactionsDataMockDBRowsForAllCategories ...
// nolint
func TransactionsDataMockDBRowsForAllCategories() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "abc123efg",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_MAIN_TO_POCKET",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "fasdfasdf",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_POCKET_TO_MAIN",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc12fasdf3efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY_FEE",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          7,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edx",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "10",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          8,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc1sdf23efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "RECEIVE_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          9,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efdsdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
		},
		{
			ID:                          9,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599eddfsd",
			ClientBatchID:               "abc123efdgf",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7csd0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44sde6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TAX_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
		},
		{
			ID:                          10,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "ADJUSTMENT",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// TransactionsDataMockDBRowsForPaymentFeeUseCaseRequest ...
// nolint
func TransactionsDataMockDBRowsForPaymentFeeUseCaseRequest() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          7352,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "a400e8b3-d50c-4197-902b-ad3f49c4153c",
			TmTransactionID:             "5ab4623e-37d8-41e1-b991-28f1cc3a7ced",
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
		{
			ID:                          7354,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "a400e8b3-d50c-4197-902b-ad3f49c4153c",
			TmTransactionID:             "b249119a-7da2-4534-908a-7c3932651bbc",
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY_FEE",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "6500",
			TransactionCurrency:         locale.Currency,
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
		{
			ID:                          7356,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "cecb9914-3be6-442c-9cd2-b6af2ef04dca",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "credit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
		{
			ID:                          7358,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "cecb9914-3be6-442c-9cd2-b6af2ef04dca",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
		{
			ID:                          7360,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY_FEE",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "credit",
			TransactionAmount:           "6500",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
		{
			ID:                          7362,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY_FEE",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "6500",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
		},
	}
}

// TransactionsDataMockDBRowsForChildAccountTransfersUseCase ...
//
//nolint:dupl
func TransactionsDataMockDBRowsForChildAccountTransfersUseCase() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"source_display_name": "source-name", "destination_display_name": "destination-name"})
	return []*storage.TransactionsData{
		{ID: 5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599es",
			ClientBatchID:               "client-batch-id-withdrawal",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_POCKET_TO_MAIN",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
			BatchDetails:                batchDetails,
		},
		{ID: 4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ei",
			ClientBatchID:               "client-batch-id-funding",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_MAIN_TO_POCKET",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
			BatchDetails:                batchDetails,
		}}
}

// TransactionsDataMockDBRowsForTaxAndInterest ...
//
//nolint:dupl
func TransactionsDataMockDBRowsForTaxAndInterest() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"source_display_name": "source-name", "destination_display_name": "destination-name"})
	return []*storage.TransactionsData{
		{
			ID:                          9,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599eddfsd",
			ClientBatchID:               "abc123efdgf",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7csd0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44sde6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TAX_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
			BatchDetails:                batchDetails,
		},
		{
			ID:                          10,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efdsdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
			BatchDetails:                batchDetails,
		}}
}

// TransactionsDataMockDBRowsForCardTxnUseCaseRequest ...
// nolint
func TransactionsDataMockDBRowsForCardTxnUseCaseRequest() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{

		{
			ID:                          7362,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bcdd",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEBIT_CARD",
			TransactionType:             "SPEND_CARD_NOT_PRESENT",
			TransactionSubtype:          "MASTERCARD",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "100",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7360,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bcdd",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "SETTLEMENT",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEBIT_CARD",
			TransactionType:             "SPEND_CARD_NOT_PRESENT",
			TransactionSubtype:          "MASTERCARD",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "credit",
			TransactionAmount:           "100",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7358,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "cecb9914-3be6-442c-9cd2-b6af2ef04dca",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7352,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701faq",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bcdd",
			TmPostingInstructionBatchID: "a400e8b3-d50c-4197-902b-ad3f49c4153c",
			TmTransactionID:             "5ab4623e-37d8-41e1-b991-28f1cc3a7ced",
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEBIT_CARD",
			TransactionType:             "SPEND_CARD_NOT_PRESENT",
			TransactionSubtype:          "MASTERCARD",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "100",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// TransactionsDataMockDBRowsForLoanTxnUseCaseRequest ...
// nolint
func TransactionsDataMockDBRowsForLoanTxnUseCaseRequest() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{

		{
			ID:                          7362,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "02046bef37a44a24a49c79437288cf88",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "DRAWDOWN",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7360,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "a1e9564232604bfead406d69cb985fd5",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "DRAWDOWN",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "100",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7358,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "cecb9914-3be6-442c-9cd2-b6af2ef04dca",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7352,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701faq",
			ClientBatchID:               "87caacde9e3b41edaa004b430fefd5bc",
			TmPostingInstructionBatchID: "a400e8b3-d50c-4197-902b-ad3f49c4153c",
			TmTransactionID:             "5ab4623e-37d8-41e1-b991-28f1cc3a7ced",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "REPAYMENT",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "************",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "50",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// TransactionsDataMockDBRowsForBizLendingTxnUseCaseRequest ...
// nolint
func TransactionsDataMockDBRowsForBizLendingTxnUseCaseRequest() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{

		{
			ID:                          7362,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "02046bef37a44a24a49c79437288cf88",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "BIZ_LENDING",
			TransactionType:             "DRAWDOWN",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7360,
			ClientTransactionID:         "feaeb973ce6d47e3ae91343e6dacf79f",
			ClientBatchID:               "a1e9564232604bfead406d69cb985fd5",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "afa1a18e-8a4b-4c12-b1a1-6f3b20b1a923",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "BIZ_LENDING",
			TransactionType:             "REPAYMENT",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "100",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7358,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701f",
			ClientBatchID:               "aa44338fd3444a43ad45218bb54c62bc",
			TmPostingInstructionBatchID: "f154b88b-6093-4e6b-9ecc-5dbbedd0b26b",
			TmTransactionID:             "cecb9914-3be6-442c-9cd2-b6af2ef04dca",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "BIZ_LENDING",
			TransactionType:             "WRITE_OFF",
			TransactionSubtype:          "BAD_DEBT_RECOVERY_INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "22000",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "*********.04",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          7352,
			ClientTransactionID:         "b195a28d62264cc7be07f2b93c5b701faq",
			ClientBatchID:               "87caacde9e3b41edaa004b430fefd5bc",
			TmPostingInstructionBatchID: "a400e8b3-d50c-4197-902b-ad3f49c4153c",
			TmTransactionID:             "5ab4623e-37d8-41e1-b991-28f1cc3a7ced",
			TmTransactionType:           "TRANSFER",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "BIZ_LENDING",
			TransactionType:             "WRITE_OFF",
			TransactionSubtype:          "OPS_LOSS_RECOVERY_INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "50",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "-********",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// TransactionsDataMockDBRowsForInsuranceTxn ...
// nolint: dupl, funlen
func TransactionsDataMockDBRowsForInsuranceTxn() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	return []*storage.TransactionsData{
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           constants.InsuranceDomain,
			TransactionType:             constants.InsurPremiumTxType,
			TransactionSubtype:          constants.CollectCustSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// TransactionsDataMockDBRowsForBizTxn ...
func TransactionsDataMockDBRowsForBizTxn() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          6,
			ClientTransactionID:         "a1943437135d4674bd1f39df6233c83c",
			ClientBatchID:               "3e9c0187cf4941c0bf8f680fc813a656",
			TmPostingInstructionBatchID: "ecdb7ab2-5c74-433f-b855-bb84a76ae2bf",
			TmTransactionID:             "88b29426-601b-4dd2-9575-4756f4ec8acd",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           constants.BizDepositsDomain,
			TransactionType:             constants.FundInTxType,
			TransactionSubtype:          constants.RPP,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "15.2",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "436",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentDetailMockDBRows ...
// nolint
func PaymentDetailMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	metadata, _ := json.Marshal(map[string]string{"grab_activity_type": "Grab Ride"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              locale.Currency,
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    7,
			TransactionID:         "test-client-batch-id1",
			Currency:              locale.Currency,
			Amount:                -513,
			TransactionDomain:     constants.InsuranceDomain,
			TransactionType:       constants.InsurPremiumTxType,
			TransactionSubType:    constants.CollectCustSubType,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              []byte(`{"counterPartyDisplayName": "Cyber Fraud Protect payment"}`),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    8,
			TransactionID:         "efdf83ec861046dc988e78d4f16f9a7c",
			Currency:              locale.Currency,
			Amount:                -5,
			TransactionDomain:     constants.InsuranceDomain,
			TransactionType:       constants.InsurPremiumTxType,
			TransactionSubType:    constants.CollectCustSubType,
			AccountID:             "*************",
			CounterPartyAccountID: "*********",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              []byte(`{"counterPartyDisplayName": "Car insurance payment"}`),
			Status:                "AUTHORISED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentDetailMooMooSpendMoneyMockDBRows ...
// nolint
func PaymentDetailMooMooSpendMoneyMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned,
		{
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              locale.Currency,
			Amount:                -100,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "MOOMOO",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentDetailMooMooCashOutMockDBRows ...
// nolint
func PaymentDetailMooMooCashOutMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned,
		{
			ID:                    8,
			TransactionID:         "abcd1234567",
			Currency:              locale.Currency,
			Amount:                600,
			TransactionType:       "CASH_OUT",
			TransactionSubType:    "MOOMOO",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentDetailMooMooSpendMoneyRevMockDBRows ...
// nolint
func PaymentDetailMooMooSpendMoneyRevMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned,
		{
			ID:                    9,
			TransactionID:         "abcd123456",
			Currency:              locale.Currency,
			Amount:                500,
			TransactionType:       "SPEND_MONEY_REVERSAL",
			TransactionSubType:    "MOOMOO",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// SpendCardPresentReversalTrxDetails ...
func SpendCardPresentReversalTrxDetails() []*storage.CardTransactionDetail {
	cardTransaction := commonCardTransactionDetails()
	cardTransaction.TransactionDomain = constants.DebitCardDomain
	cardTransaction.TransactionType = constants.SpendCardPresentReversalTransactionType
	cardTransaction.TransactionSubType = constants.PaynetMydebitTransactionSubtype
	return []*storage.CardTransactionDetail{&cardTransaction}
}

func commonCardTransactionDetails() storage.CardTransactionDetail {
	return storage.CardTransactionDetail{
		ID:                            0,
		CardTransactionID:             "test-client-batch-id1",
		CardID:                        "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
		TransactionDomain:             constants.DebitCardDomain,
		TransactionType:               constants.SpendCardATMReversalTransactionType,
		TransactionSubType:            constants.MastercardTransactionSubtype,
		Amount:                        10000,
		Currency:                      "MYR",
		OriginalAmount:                10000,
		CaptureOriginalAmountTillDate: 1,
		OriginalCurrency:              "MYR",
		CaptureAmount:                 10000,
		CaptureAmountTillDate:         0,
		AccountID:                     "**********",
		MerchantDescription:           "Amazon",
		Status:                        "COMPLETED",
		StatusDetails:                 nil,
		Metadata:                      nil,
		TailCardNumber:                "9350",
		CreationTimestamp:             time.Unix(**********, 0).UTC(),
		ValueTimestamp:                time.Unix(**********, 0).UTC(),
		CreatedAt:                     time.Unix(**********, 0).UTC(),
		UpdatedAt:                     time.Unix(**********, 0).UTC(),
	}
}

// TransferNonResidentTrxDetails ...
func TransferNonResidentTrxDetails() []*storage.PaymentDetail {
	metadata := map[string]interface{}{
		"remarks":             "",
		"external_id":         "20240704GXSPMYKL010ORB69594889",
		"serviceType":         "",
		"transferType":        "PAYMENT",
		"cash_account_code":   "DFLT",
		"is_ops_transaction":  false,
		"payment_description": "",
		"recipient_reference": "Pindahan",
		"residentStatus":      "2",
		"purposeCode":         "16830",
		"beneficiaryCountry":  "MY",
		"relationshipCode":    "NR",
	}
	metadataBytes, _ := json.Marshal(metadata)
	paymentDetail := commonPaymentDetail()
	paymentDetail.Metadata = metadataBytes
	paymentDetail.TransactionDomain = constants.DepositsDomain
	paymentDetail.TransactionType = constants.SendMoneyTxType
	paymentDetail.TransactionSubType = constants.RPP
	return []*storage.PaymentDetail{&paymentDetail}
}

// SpendCardATMReversalTrxDetails ...
func SpendCardATMReversalTrxDetails() []*storage.CardTransactionDetail {
	cardTransaction := commonCardTransactionDetails()
	cardTransaction.TransactionDomain = constants.DebitCardDomain
	cardTransaction.TransactionType = constants.SpendCardATMReversalTransactionType
	cardTransaction.TransactionSubType = constants.MastercardTransactionSubtype
	return []*storage.CardTransactionDetail{&cardTransaction}
}

// SpendCardNotPresentReversalTrxDetails ...
func SpendCardNotPresentReversalTrxDetails() []*storage.CardTransactionDetail {
	cardTransaction := commonCardTransactionDetails()
	cardTransaction.TransactionDomain = constants.DebitCardDomain
	cardTransaction.TransactionType = constants.SpendCardNotPresentReversalTransactionType
	cardTransaction.TransactionSubType = constants.PaynetMydebitTransactionSubtype
	return []*storage.CardTransactionDetail{&cardTransaction}
}

// CardTxnDetailMockDBRows ...
// nolint
func CardTxnDetailMockDBRows() []*storage.CardTransactionDetail {
	return []*storage.CardTransactionDetail{{
		ID:                            0,
		CardTransactionID:             "aa44338fd3444a43ad45218bb54c62bcdd",
		CardID:                        "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
		TransactionDomain:             "DEBIT_CARD",
		TransactionType:               "SPEND_CARD_NOT_PRESENT",
		TransactionSubType:            "MASTERCARD",
		Amount:                        10000,
		Currency:                      "MYR",
		OriginalAmount:                10000,
		CaptureOriginalAmountTillDate: 1,
		OriginalCurrency:              "MYR",
		CaptureAmount:                 10000,
		CaptureAmountTillDate:         0,
		AccountID:                     "************",
		MerchantDescription:           "MerchantDescription",
		Status:                        "COMPLETED",
		StatusDetails:                 nil,
		Metadata:                      nil,
		TailCardNumber:                "9350",
		CreationTimestamp:             time.Unix(**********, 0).UTC(),
		ValueTimestamp:                time.Unix(**********, 0).UTC(),
		CreatedAt:                     time.Unix(**********, 0).UTC(),
		UpdatedAt:                     time.Unix(**********, 0).UTC(),
	}}
}

// LoanDetailMockDBRows ...
// nolint
func LoanDetailMockDBRows() []*storage.LoanDetail {
	return []*storage.LoanDetail{{
		ID:                   1,
		LoanTransactionID:    "",
		Amount:               100000,
		Currency:             "MYR",
		AccountID:            "************",
		AccountDetail:        nil,
		PaymentTransactionID: "02046bef37a44a24a49c79437288cf88",
		TransactionDomain:    "LENDING",
		TransactionType:      "DRAWDOWN",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   nil,
		RepaymentDetail:      nil,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}, {
		ID:                   2,
		LoanTransactionID:    "",
		Amount:               10000,
		Currency:             "MYR",
		AccountID:            "************",
		AccountDetail:        nil,
		PaymentTransactionID: "a1e9564232604bfead406d69cb985fd5",
		TransactionDomain:    "LENDING",
		TransactionType:      "DRAWDOWN",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   nil,
		RepaymentDetail:      nil,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}, {
		ID:                   3,
		LoanTransactionID:    "",
		Amount:               -5000,
		Currency:             "MYR",
		AccountID:            "************",
		AccountDetail:        nil,
		PaymentTransactionID: "87caacde9e3b41edaa004b430fefd5bc",
		TransactionDomain:    "LENDING",
		TransactionType:      "REPAYMENT",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   nil,
		RepaymentDetail:      nil,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}}
}

// PaymentDetailMockDBRowsDBMY ...
// nolint
func PaymentDetailMockDBRowsDBMY() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetailDBMY, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "USER NO2"})
	// metadata, _ := json.Marshal(map[string]string{"grab_activity_type": "Grab Ride"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetailDBMY,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
	}
}

// PaymentDetailMockDBRowsWithOriginAccount ...
func PaymentDetailMockDBRowsWithOriginAccount() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "12345", DisplayName: "OriginDisplayName"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Account:               accountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
	}
}

// GetAllTransactionsBackwardScrollingMockDBResponse ...
//
//nolint:dupl
func GetAllTransactionsBackwardScrollingMockDBResponse() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// GetAllTransactionsPrevNextExistMockDBResponse ...
func GetAllTransactionsPrevNextExistMockDBResponse() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRows()
	return rows[1:]
}

// GetCustomerTxnRowsTxnData ...
// nolint
func GetCustomerTxnRowsTxnData() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RPP_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RPP_NETWORK",
			AccountID:                   "*********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// GetCustomerTxnRowsPaymentDetailRPPData ...
// nolint
func GetCustomerTxnRowsPaymentDetailRPPData() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1"})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "User No2"})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	return map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123effasddag", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			Amount:                -13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              metadata,
		},
	}
}

// GetCustomerTxnRowsPaymentDetailIntrabankData ...
// nolint
func GetCustomerTxnRowsPaymentDetailIntrabankData() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1"})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No2"})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	return map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123efasdffdag", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			Amount:                -13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "**********",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              metadata,
		},
		{PaymentBatchID: "abc123efasdffdag", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			Amount:                13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               counterPartyAccountDetail,
			CounterPartyAccountID: "**********",
			CounterPartyAccount:   account,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              metadata,
		},
	}
}

// GetCustomerTxnRowsPaymentDetailLendingData ...
// nolint
func GetCustomerTxnRowsPaymentDetailLendingData() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	return map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123efasdffdagffff", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "abc123efasdffdagffff",
			Amount:                13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               []byte(`{"number": "**********"}`),
			CounterPartyAccountID: "************",
			CounterPartyAccount:   []byte(`{"number": "************"}`),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              nil,
		},
		{PaymentBatchID: "abc123efasdffdagffff", AccountID: "************"}: {
			ID:                    1,
			TransactionID:         "abc123efasdffdagffff",
			Amount:                -13,
			Currency:              locale.Currency,
			AccountID:             "************",
			Account:               []byte(`{"number": "************"}`),
			CounterPartyAccountID: "**********",
			CounterPartyAccount:   []byte(`{"number": "**********"}`),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              nil,
		},
		{PaymentBatchID: "abc123efasdffdag", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "abc123efasdffdag",
			Amount:                -13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               []byte(`{"number": "**********"}`),
			CounterPartyAccountID: "************",
			CounterPartyAccount:   []byte(`{"number": "************"}`),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              nil,
		},
		{PaymentBatchID: "abc123efasdffdag", AccountID: "************"}: {
			ID:                    1,
			TransactionID:         "abc123efasdffdag",
			Amount:                13,
			Currency:              locale.Currency,
			AccountID:             "************",
			Account:               []byte(`{"number": "************"}`),
			CounterPartyAccountID: "**********",
			CounterPartyAccount:   []byte(`{"number": "**********"}`),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              nil,
		},
	}
}

// GetCustomerTxnRowsLoanDetailLendingData ...
// nolint
func GetCustomerTxnRowsLoanDetailLendingData() map[storage.LoanDataKey]*storage.LoanDetail {
	locale := utils.GetLocale()
	return map[storage.LoanDataKey]*storage.LoanDetail{
		{LoanPaymentTrxID: "abc123efasdffdagffff"}: {
			ID:                   1,
			LoanTransactionID:    "abcdefg123",
			Amount:               -13,
			Currency:             locale.Currency,
			AccountID:            "************",
			AccountDetail:        nil,
			PaymentTransactionID: "abc123efasdffdagffff",
			TransactionDomain:    constants.LendingDomain,
			TransactionType:      constants.DrawdownTransactionType,
			TransactionSubType:   constants.IntrabankTransactionSubtype,
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   []byte(`{"LoanName": "Loan 1", "Currency": "MYR"}`),
			RepaymentDetail:      nil,
			CreatedAt:            time.Time{},
			UpdatedAt:            time.Time{},
		},
		{LoanPaymentTrxID: "abc123efasdffdag"}: {
			ID:                   1,
			LoanTransactionID:    "abcdefg123",
			Amount:               -13,
			Currency:             locale.Currency,
			AccountID:            "************",
			AccountDetail:        nil,
			PaymentTransactionID: "abc123efasdffdag",
			TransactionDomain:    constants.LendingDomain,
			TransactionType:      constants.RepaymentTransactionType,
			TransactionSubType:   constants.IntrabankTransactionSubtype,
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   nil,
			RepaymentDetail:      []byte(`{"Currency": "MYR", "LoanRepaymentDetail": [{"LoanName": "Loan 1"}]}`),
			CreatedAt:            time.Time{},
			UpdatedAt:            time.Time{},
		},
	}
}
