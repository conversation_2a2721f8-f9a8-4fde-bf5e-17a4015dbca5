package resources

import (
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetAccountImageDetailsSampleResponse  returns sample for accountImageDetail
func GetAccountImageDetailsSampleResponse() *accountService.GetAccountResponse {
	/*return &accountService.GetAccountDetailsResponse{
		AccountImageDetails: map[string]accountService.Image{
			"*************": {ID: "001", URL: "https://assets.sgbank.dev/*************"},
			"*************": {ID: "002", URL: "https://assets.sgbank.dev/*************"},
		},
		AccountNameDetails: map[string]string{
			"*************": "Paris",
			"*************": "Paris",
		},
	}*/

	return &accountService.GetAccountResponse{Account: &accountService.Account{ProductVariantID: constants.DepositsAccount}}
}
