package resources

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"
)

// DepositsBalanceSampleKafkaMessage ...
func DepositsBalanceSampleKafkaMessage() *deposits_balance_event.DepositsBalanceEvent {
	locale := utils.GetLocale()
	return &deposits_balance_event.DepositsBalanceEvent{
		Event: deposits_balance_event.Balance{
			BalanceID:                 "7131addf-81a2-4fb1-b21a-0f5821c8965a",
			AccountID:                 "**********",
			AccountAddress:            "DEFAULT",
			Phase:                     "",
			Asset:                     "COMMERCIAL_BANK_MONEY",
			Denomination:              locale.Currency,
			PostingInstructionBatchID: "23aa2045-4ea7-422f-b72f-6e8b44931009",
			ValueTimestamp:            time.Unix(**********, 0).UTC(),
			Amount:                    "125",
			TotalCredit:               "22",
			TotalDebit:                "147",
		},
	}
}
