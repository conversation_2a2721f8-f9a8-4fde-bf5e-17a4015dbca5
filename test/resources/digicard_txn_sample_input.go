package resources

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_transaction_tx"
)

// DigicardTxnSampleKafkaMessage ...
func DigicardTxnSampleKafkaMessage() *digicard_transaction_tx.DigicardTransactionTx {
	valueTime := time.Unix(**********, 0).UTC()
	data := &digicard_transaction_tx.DigicardTransactionTx{
		CardID:           "23aa2045-4ea7-422f-b72f-6e8b44931009",
		CustomerID:       "8720867d-59e4-4050-b08f-b23326632da9",
		TransactionID:    "8720867d-59e4-4050-b08f-b23326632da9",
		LastFourDigits:   "7931",
		RequestAmount:    3000,
		RequestCurrency:  "SGD",
		ReferenceID:      "8720867d-59e4-4050-b08f-b23326632da9",
		OriginalAmount:   3000,
		OriginalCurrency: "SGD",
		TransactionCode: &deposits_core_tx.TransactionCode{
			Domain: "LENDING", Type: "SEND_MONEY", SubType: "FAST_NETWORK",
		},
		SourceAccountID:       "********",
		DestinationAccountID:  "*********",
		Status:                "Processing",
		StatusDescription:     "",
		MerchantDescription:   "Grab Taxi",
		CaptureMode:           "Automatic",
		OriginalTransactionID: "7d-59e4-4050-b08f-b23326632",
		CreationTimestamp:     time.Unix(**********, 0).UTC(),
		ValueTimestamp:        &valueTime,
	}
	return data
}

// CardTransactionDetailSample ...
// nolint: dupl
func CardTransactionDetailSample() *storage.CardTransactionDetail {
	return &storage.CardTransactionDetail{
		ID:                    0,
		CardTransactionID:     "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
		CardID:                "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
		TransactionDomain:     "TransactionDomain",
		TransactionType:       "SPEND_CARD_ATM",
		TransactionSubType:    "MASTERCARD",
		Amount:                99,
		Currency:              "MYR",
		OriginalAmount:        100,
		OriginalCurrency:      "MYR",
		CaptureAmount:         0,
		CaptureAmountTillDate: 0,
		AccountID:             "AccountID",
		MerchantDescription:   "MerchantDescription",
		Status:                "PROCESSING",
		StatusDetails:         nil,
		Metadata:              nil,
		TailCardNumber:        "9350",
		CreationTimestamp:     time.Unix(**********, 0).UTC(),
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		CreatedAt:             time.Unix(**********, 0).UTC(),
		UpdatedAt:             time.Unix(**********, 0).UTC(),
	}
}
