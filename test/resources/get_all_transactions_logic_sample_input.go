package resources

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/dto"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// AllCommittedPosting ...
// nolint:dupl
func AllCommittedPosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// AllReleasePosting ...
//
//nolint:dupl
func AllReleasePosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY_REVERSAL",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_INGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}}
}

// FailedFASTPosting ...
//
//nolint:dupl
func FailedFASTPosting() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                          1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                constants.PostingPhasePendingOutgoing,
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}, {
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                constants.PostingPhasePendingOutgoing,
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}}
}

// CommittedAndPendingOutgoingPosting ...
//
//nolint:dupl
func CommittedAndPendingOutgoingPosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}}
}

// AllPendingIncomingPosting ...
//
//nolint:dupl
func AllPendingIncomingPosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// CommittedAndPendingIncomingPosting ...
//
//nolint:dupl
func CommittedAndPendingIncomingPosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// AllPendingOutgoingPosting ...
//
//nolint:dupl
func AllPendingOutgoingPosting() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}}
}

// GetCounterPartyDisplayNameData ...
//
//nolint:dupl, funlen
func GetCounterPartyDisplayNameData() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"source_display_name": "ManualUser1", "destination_display_name": "ManualUser2"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 4,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "ACCRUED_DEPOSIT_INTEREST_**********",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 5,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ff",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "0acb2d25-aecd-4741-b7c0-b5f138105dee",
		TmTransactionID:             "85e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	},
		{ID: 6,
			ClientTransactionID:         "client-tid-6",
			ClientBatchID:               "abcd12345",
			TmPostingInstructionBatchID: "0acb2d25-aecd-4741-b7c0-b5f138105dee",
			TmTransactionID:             "85e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "ADJUSTMENT",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BatchInsertionTimestamp:     time.Unix(**********, 0),
			BatchValueTimestamp:         time.Unix(**********, 0),
			TmTransactionType:           "SETTLEMENT",
			BatchDetails:                batchDetails,
		},
	}
}

// GetAccountDisplayNameData ...
//
//nolint:dupl, funlen
func GetAccountDisplayNameData() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"source_display_name": "ManualUser1", "destination_display_name": "ManualUser2"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	},
	}
}

// InterestPayoutTransactionReverseOrderInput ...
//
//nolint:dupl
func InterestPayoutTransactionReverseOrderInput() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
	}, {
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "ACCRUED_DEPOSIT_INTEREST",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
	}}
}

// InterestPayoutTransactionOrderedInput ...
//
//nolint:dupl
func InterestPayoutTransactionOrderedInput() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "ACCRUED_DEPOSIT_INTEREST",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0),
			BatchValueTimestamp:         time.Unix(**********, 0),
		}, {
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0),
			BatchValueTimestamp:         time.Unix(**********, 0),
		}}
}

func commonPaymentDetail() storage.PaymentDetail {
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})

	return storage.PaymentDetail{
		ID:                    1,
		TransactionID:         "test-client-batch-id1",
		Amount:                -20,
		Currency:              "MYR",
		AccountID:             "**********",
		CounterPartyAccountID: "54321",
		CounterPartyAccount:   counterPartyAccountDetail,
		Status:                "COMPLETED",
		Metadata:              []byte("{}"),
		CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
	}
}

func commonTransactionData() storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	return storage.TransactionsData{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "test-client-batch-id1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 constants.BatchStatusAccepted,
		TransactionDomain:           constants.DebitCardDomain,
		TransactionType:             constants.SpendCardPresentReversalTransactionType,
		TransactionSubtype:          constants.PaynetMydebitTransactionSubtype,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchDetails:                batchDetails,
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
	}
}

// SpendCardPresentReversalTransactions ...
func SpendCardPresentReversalTransactions() []*storage.TransactionsData {
	transaction := commonTransactionData()
	transaction.TransactionDomain = constants.DebitCardDomain
	transaction.TransactionType = constants.SpendCardPresentReversalTransactionType
	transaction.TransactionSubtype = constants.PaynetMydebitTransactionSubtype
	return []*storage.TransactionsData{&transaction}
}

// TransferNonResidentTransactions ...
func TransferNonResidentTransactions() []*storage.TransactionsData {
	transaction := commonTransactionData()
	transaction.DebitOrCredit = "debit"
	transaction.TransactionDomain = constants.DepositsDomain
	transaction.TransactionType = constants.SendMoneyTxType
	transaction.TransactionSubtype = constants.RPP
	return []*storage.TransactionsData{&transaction}
}

// SpendCardATMReversalTransactions ...
func SpendCardATMReversalTransactions() []*storage.TransactionsData {
	transaction := commonTransactionData()
	transaction.TransactionDomain = constants.DebitCardDomain
	transaction.TransactionType = constants.SpendCardATMReversalTransactionType
	transaction.TransactionSubtype = constants.MastercardTransactionSubtype
	return []*storage.TransactionsData{&transaction}
}

// SpendCardNotPresentReversalTransactions ...
func SpendCardNotPresentReversalTransactions() []*storage.TransactionsData {
	transaction := commonTransactionData()
	transaction.TransactionDomain = constants.DebitCardDomain
	transaction.TransactionType = constants.SpendCardNotPresentReversalTransactionType
	transaction.TransactionSubtype = constants.PaynetMydebitTransactionSubtype
	return []*storage.TransactionsData{&transaction}
}

func UnclaimedMoniesTransferTransactions() []*storage.TransactionsData {
	transaction := commonTransactionData()
	transaction.TransactionDomain = constants.DepositsDomain
	transaction.TransactionType = constants.UnclaimedMoniesTransactionType
	transaction.TransactionSubtype = constants.CardBankInitiatedTransactionSubtype
	transaction.DebitOrCredit = constants.DEBIT
	return []*storage.TransactionsData{&transaction}
}

// PocketTransactions ...
// nolint: funlen
func PocketTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	batchDetails2, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Paris1", "destination_display_name": "MAIN_ACCOUNT"})
	return []*storage.TransactionsData{
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// PocketTransactionsDBMY ...
// nolint: dupl, funlen
func PocketTransactionsDBMY() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	batchDetails2, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Paris1", "destination_display_name": "MAIN_ACCOUNT"})
	return []*storage.TransactionsData{
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {
			ID:                          7,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// CounterPartyTransactionEntryForPocketTransactions will be just the counterparty transaction PocketTransactions method
func CounterPartyTransactionEntryForPocketTransactions() []*storage.TransactionsData {
	pocketTransfer := PocketTransactions()
	return []*storage.TransactionsData{pocketTransfer[0], pocketTransfer[2]}
}

// PocketInterestPayoutTransactionInput ...
//
//nolint:dupl
func PocketInterestPayoutTransactionInput() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "APPLY_ACCRUED_DEPOSIT_INTEREST",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "ACCRUED_DEPOSIT_INTEREST_**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id-interest-payout",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}}
}

// NormalRewardCashBackTransactions ...
func NormalRewardCashBackTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "payment-core", "batch_remarks": ""})
	transactionDetails, _ := json.Marshal(map[string]string{"priority": "", "external_id": "", "campaignName": "Add money reward", "campaignDescription": "RM8 cashback"})
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "test-client-batch-id1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 constants.BatchStatusAccepted,
		TransactionDomain:           constants.DepositsDomain,
		TransactionType:             constants.RewardsCashback,
		TransactionSubtype:          constants.OPSTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchRemarks:                "GX rewards input",
		BatchDetails:                batchDetails,
		TransactionDetails:          transactionDetails,
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
	}}
}

// GXRewardCashBackTransactions ...
func GXRewardCashBackTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "payment-ops-trf", "batch_remarks": "GXBank Reward", "posting_source": "BANK", "source_display_name": "", "destination_display_name": "GXBank Reward via Ops Portal"})
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "test-client-batch-id1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 constants.BatchStatusAccepted,
		TransactionDomain:           constants.DepositsDomain,
		TransactionType:             constants.RewardsCashback,
		TransactionSubtype:          constants.OPSTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchRemarks:                "GX rewards input",
		BatchDetails:                batchDetails,
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
	}}
}

// InsuranceTransactions ...
func InsuranceTransactions() []*storage.TransactionsData {
	return []*storage.TransactionsData{{ID: 7,
		ClientTransactionID:         "8a554e99c9894f819f0d115470608d58",
		ClientBatchID:               "efdf83ec861046dc988e78d4f16f9a7c",
		TmPostingInstructionBatchID: "daf191c5-a4da-4017-8182-c6cbf19171ab",
		TmTransactionID:             "e48612a6-bc8a-48d3-b815-afe7a83e748a",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "INSURANCE",
		TransactionType:             "INSUR_PREMIUM",
		TransactionSubtype:          "COLLECT_CUST",
		AccountID:                   "*************",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.05",
		TransactionCurrency:         utils.GetLocale().Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0),
		BatchValueTimestamp:         time.Unix(**********, 0),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}}
}
