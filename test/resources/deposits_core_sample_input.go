package resources

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
)

// DepositsCoreSampleKafkaMessage ...
func DepositsCoreSampleKafkaMessage() *deposits_core_tx.DepositsCoreTx {
	locale := utils.GetLocale()
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "FAST_NETWORK"},
		CommittedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "25",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "",
			}, {
				Credit:         true,
				Amount:         "25",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "",
			},
		},
	}
	data := &deposits_core_tx.DepositsCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
		BatchRemarks:          "Happy Payment Remarks",
	}
	return data
}

// DepositsCoreInterestKafkaMessage ...
// nolint:dupl
func DepositsCoreInterestKafkaMessage() *deposits_core_tx.DepositsCoreTx {
	locale := utils.GetLocale()
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "INTEREST_PAYOUT", SubType: "SAVINGS"},
		CommittedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "ACCRUED_DEPOSIT_INTEREST",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			}, {
				Credit:         true,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			},
		},
	}
	data := &deposits_core_tx.DepositsCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
	}
	return data
}

// DepositsCoreInterestReversalKafkaMessage ...
//
//nolint:dupl
func DepositsCoreInterestReversalKafkaMessage() *deposits_core_tx.DepositsCoreTx {
	locale := utils.GetLocale()
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "INTEREST_PAYOUT_REVERSAL", SubType: "SAVINGS"},
		CommittedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			}, {
				Credit:         true,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "ACCRUED_DEPOSIT_INTEREST",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			},
		},
	}
	data := &deposits_core_tx.DepositsCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "ACCEPTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
	}
	return data
}

// DepositsCoreRejectedTxnKafkaMessage ...
//
//nolint:dupl
func DepositsCoreRejectedTxnKafkaMessage() *deposits_core_tx.DepositsCoreTx {
	locale := utils.GetLocale()
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "INTEREST_PAYOUT", SubType: "SAVINGS"},
		RejectedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "ACCRUED_DEPOSIT_INTEREST",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			}, {
				Credit:         true,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			},
		},
	}
	data := &deposits_core_tx.DepositsCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "REJECTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
	}
	return data
}
