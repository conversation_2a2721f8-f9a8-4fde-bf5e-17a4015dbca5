package resources

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_tx"
)

// LoanCoreTxSampleKafkaMessage ...
func LoanCoreTxSampleKafkaMessage() *loan_core_tx.LoanCoreTx {
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "LENDING", Type: "SEND_MONEY", SubType: "FAST_NETWORK"},
		CommittedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "25",
				Currency:       "SGD",
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "",
			}, {
				Credit:         true,
				Amount:         "25",
				Currency:       "SGD",
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "",
			},
		},
	}
	data := &loan_core_tx.LoanCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
		BatchRemarks:          "Happy Payment Remarks",
	}
	return data
}

// LoanCoreInterestKafkaMessage ...
// nolint:dupl
func LoanCoreInterestKafkaMessage() *loan_core_tx.LoanCoreTx {
	locale := utils.GetLocale()
	transactionEnquiry := deposits_core_tx.TransactionEnquiry{
		ID:                 "cd5e5261-c827-4316-ac42-87f61732c43e",
		TransactionID:      "33141f096c8d4492849d8b62815d3232",
		TransactionType:    "SETTLEMENT",
		TransactionDetails: nil,
		Violations:         nil,
		TransactionCode:    &deposits_core_tx.TransactionCode{Domain: "LENDING", Type: "SEND_MONEY", SubType: "FAST_NETWORK"},
		CommittedPostings: []*deposits_core_tx.Posting{
			{
				Credit:         false,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "ACCRUED_DEPOSIT_INTEREST",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			}, {
				Credit:         true,
				Amount:         "0.13",
				Currency:       locale.Currency,
				AccountID:      "**********",
				AccountAddress: "DEFAULT",
				Asset:          "COMMERCIAL_BANK_MONEY",
				Phase:          "POSTING_PHASE_COMMITTED",
			},
		},
	}
	data := &loan_core_tx.LoanCoreTx{
		ID:                    "23aa2045-4ea7-422f-b72f-6e8b44931009",
		BatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		RequestIdempotencyKey: "7931addf-81a2-4fb1-b21a-0f5821c8965a",
		BatchDetails:          nil,
		ValueTimestamp:        time.Unix(**********, 0).UTC(),
		InsertionTimestamp:    time.Unix(**********, 0).UTC(),
		Status:                "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		ErrorMessage:          "",
		ErrorType:             "",
		TransactionEnquiries:  []deposits_core_tx.TransactionEnquiry{transactionEnquiry},
	}
	return data
}

// LoanCoreEmptySliceOfTransactionList ...
func LoanCoreEmptySliceOfTransactionList() ([]*storage.TransactionsData, []*storage.TransactionsData) {
	postingLevelTransactionList := []*storage.TransactionsData{}
	dbTransactionsDataList := []*storage.TransactionsData{}
	return postingLevelTransactionList, dbTransactionsDataList
}

// LoanCoreEqualRecordsInBothList ...
func LoanCoreEqualRecordsInBothList() ([]*storage.TransactionsData, []*storage.TransactionsData) {
	postingLevelTransactionList := []*storage.TransactionsData{
		{
			TmTransactionID:         "1",
			AccountID:               "A",
			AccountPhase:            "Phase1",
			AccountAddress:          "Address1",
			CreatedAt:               time.Now(),
			BalanceAfterTransaction: strconv.Itoa(100),
			Metadata:                json.RawMessage{},
		},
	}
	dbTransactionsDataList := []*storage.TransactionsData{
		{
			TmTransactionID:         "1",
			AccountID:               "A",
			AccountPhase:            "Phase1",
			AccountAddress:          "Address1",
			ID:                      1,
			CreatedAt:               time.Now(),
			BalanceAfterTransaction: strconv.Itoa(100),
			Metadata:                json.RawMessage{},
		},
	}
	return postingLevelTransactionList, dbTransactionsDataList
}

// LoanCoreDifferentRecordsInBothAndUpdateSecondList ...
func LoanCoreDifferentRecordsInBothAndUpdateSecondList() ([]*storage.TransactionsData, []*storage.TransactionsData) {
	postingLevelTransactionList := []*storage.TransactionsData{
		{
			TmTransactionID: "1",
			AccountID:       "A",
			AccountPhase:    "Phase1",
			AccountAddress:  "Address1",
			Metadata:        json.RawMessage{},
		}, {
			TmTransactionID: "2",
			AccountID:       "B",
			AccountPhase:    "Phase2",
			AccountAddress:  "Address2",
			Metadata:        json.RawMessage{},
		},
	}
	dbTransactionsDataList := []*storage.TransactionsData{
		{
			TmTransactionID:         "1",
			AccountID:               "A",
			AccountPhase:            "Phase1",
			AccountAddress:          "Address1",
			ID:                      1,
			CreatedAt:               time.Now(),
			BalanceAfterTransaction: strconv.Itoa(100),
			Metadata:                json.RawMessage{},
		},
	}
	return postingLevelTransactionList, dbTransactionsDataList
}

// LoanCoreDifferentRecordsInBothAndUpdateFirstList ...
func LoanCoreDifferentRecordsInBothAndUpdateFirstList() ([]*storage.TransactionsData, []*storage.TransactionsData) {
	postingLevelTransactionList := []*storage.TransactionsData{
		{
			TmTransactionID:         "1",
			AccountID:               "A",
			AccountPhase:            "Phase1",
			AccountAddress:          "Address1",
			ID:                      2,
			CreatedAt:               time.Now(),
			BalanceAfterTransaction: strconv.Itoa(100),
			Metadata:                json.RawMessage{},
		},
	}
	dbTransactionsDataList := []*storage.TransactionsData{
		{
			TmTransactionID:         "1",
			AccountID:               "A",
			AccountPhase:            "Phase1",
			AccountAddress:          "Address1",
			ID:                      1,
			CreatedAt:               time.Now(),
			BalanceAfterTransaction: strconv.Itoa(100),
			Metadata:                json.RawMessage{},
		},
	}
	return postingLevelTransactionList, dbTransactionsDataList
}

// MockTransactionDataSaveBatchAndUpdate ...
func MockTransactionDataSaveBatchAndUpdate() {
	// Mocking TransactionData
	mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
	mockTransactionDataStorageDAO.On("Update", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockTransactionDataStorageDAO.On("SaveBatch", mock.Anything, mock.Anything).Return(nil)
	storage.TransactionsDataD = mockTransactionDataStorageDAO
}
