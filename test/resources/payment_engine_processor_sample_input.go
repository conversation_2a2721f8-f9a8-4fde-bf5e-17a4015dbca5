package resources

import (
	"encoding/json"
	"time"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
)

// PaymentEngineIntrabankTxHappyInput ...
// nolint:dupl
func PaymentEngineIntrabankTxHappyInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "INTRABANK",
		CustomerID:              "********",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "********",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "123456", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "654321", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "TRANSFER_MONEY", SubType: "INTRABANK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineProxyObjectSampleInput ...
func PaymentEngineProxyObjectSampleInput() *payment_engine_tx.PaymentEngineTx {
	message := &payment_engine_tx.PaymentEngineTx{}
	message.Properties.Proxy.Channel = "PAYNOW"
	message.Properties.Proxy.Type = "NRIC"
	return message
}

// PaymentEngineIntrabankTxErrorInput ...
//
//nolint:dupl
func PaymentEngineIntrabankTxErrorInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "INTRABANK",
		CustomerID:              "********",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "********",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "TRANSFER_MONEY", SubType: "INTRABANK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Error Payment",
	}
	return peMessage
}

// PaymentEngineMooMooRevSampleInput ...
//
//nolint:dupl
func PaymentEngineMooMooRevSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SPEND_MONEY_REVERSAL", SubType: "MOOMOO"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineMooMooSampleInput ...
//
//nolint:dupl
func PaymentEngineMooMooSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SPEND_MONEY", SubType: "MOOMOO"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineMooMooSampleErrorInput ...
//
//nolint:dupl
func PaymentEngineMooMooSampleErrorInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SPEND_MONEY", SubType: "MOOMOO"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineFASTSendTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineFASTSendTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "FAST_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineFASTSendTransferErrorInput ...
//
//nolint:dupl
func PaymentEngineFASTSendTransferErrorInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "FAST_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineFASTReceiveTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineFASTReceiveTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "COLLECTION",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  10,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "RECEIVE_MONEY", SubType: "FAST_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineGrabSendTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineGrabSendTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SPEND_MONEY", SubType: "GRAB"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, PartnerPayload: &payment_engine_tx.PartnerPayload{ActivityType: "Grab Ride"}},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineGrabReceiveTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineGrabReceiveTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "RECEIVE_MONEY", SubType: "GRAB"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{FAST: &payment_engine_tx.FASTProperties{PurposeCode: "OTHR"}, PartnerPayload: &payment_engine_tx.PartnerPayload{ActivityType: "Grab Ride"}},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRPPSendTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineRPPSendTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "Duitnow", Type: "Mobile Number"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "RPP_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{RPP: &payment_engine_tx.RPPProperties{RecipientReference: "OTHR", PaymentDescription: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRPPSendTransferErrorInput ...
//
//nolint:dupl
func PaymentEngineRPPSendTransferErrorInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "RPP_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{RPP: &payment_engine_tx.RPPProperties{RecipientReference: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRPPReceiveTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineRPPReceiveTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	proxy := &payment_engine_tx.Proxy{Channel: "PAYNOW", Type: "NRIC"}
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "COLLECTION",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  10,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "UOVBMYKL", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "PHBMMYKL", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "RECEIVE_MONEY", SubType: "RPP_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{RPP: &payment_engine_tx.RPPProperties{RecipientReference: "OTHR"}, Proxy: proxy},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRPPFundInTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineRPPFundInTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "COLLECTION",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  10,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "FUND_IN", SubType: "RPP_NETWORK"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{RPP: &payment_engine_tx.RPPProperties{RecipientReference: "OTHR", PaymentDescription: "OTHR1"}, Proxy: nil},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRentasReceiveTransferSampleInput ...
//
//nolint:dupl
func PaymentEngineRentasReceiveTransferSampleInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "COLLECTION",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  10,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "UOVBMYKL", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{DisplayName: "testReceiver", Number: "********", SwiftCode: "PHBMMYKL", PairingID: "ABCD123"},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "RECEIVE_MONEY", SubType: "RENTAS"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineRentasReceiveTransferErrorInput ...
//
//nolint:dupl
func PaymentEngineRentasReceiveTransferErrorInput() *payment_engine_tx.PaymentEngineTx {
	locale := utils.GetLocale()
	metaData, _ := json.Marshal(map[string]map[string]string{})
	timeStampObject := time.Now().UTC()
	peMessage := &payment_engine_tx.PaymentEngineTx{
		Type:                    "PAYMENT",
		CustomerID:              "123456",
		TransactionID:           uuid.NewString(),
		ReferenceID:             "123456",
		Status:                  "completed",
		Amount:                  92,
		Currency:                locale.Currency,
		SourceAccount:           &payment_engine_tx.AccountDetail{DisplayName: "testSender", Number: "********", SwiftCode: "483838", PairingID: "ABCD123"},
		DestinationAccount:      &payment_engine_tx.AccountDetail{},
		StatusReason:            "transaction is completed",
		StatusReasonDescription: "transaction is completed",
		CaptureMethod:           "random",
		TransactionCode:         &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "SEND_MONEY", SubType: "RENTAS"},
		Metadata:                metaData,
		Properties:              &payment_engine_tx.Properties{},
		CreationTimestamp:       timeStampObject,
		ValueTimestamp:          &timeStampObject,
		Remarks:                 "Happy Payment",
	}
	return peMessage
}

// PaymentEngineBankListRespSample ...
func PaymentEngineBankListRespSample() *paymentExperience.GetExperienceResponse {
	bankListResp := &paymentExperience.GetExperienceResponse{
		Data: &paymentExperience.ExperienceResource{
			Banks: []paymentExperience.BankInfo{
				{
					Name:        "Affin Bank Berhad",
					SwiftCode:   "PHBMMYKL",
					CountryCode: "MY",
					CountryName: "Malaysia",
					ShortName:   "Affin Bank",
				},
				{
					Name:        "United Overseas Bank Berhad (UOB)",
					SwiftCode:   "UOVBMYKL",
					CountryCode: "MY",
					CountryName: "Malaysia",
					ShortName:   "UOB",
				},
			},
		},
	}
	return bankListResp
}
