package resources

import (
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// GetPocketActivitiesLastPageMockDBResponse ...
func GetPocketActivitiesLastPageMockDBResponse() []*storage.TransactionsData {
	rows := PocketTransactionsDataMockDBRows()
	return rows[2:]
}

// PocketTransactionsDataMockDBRows ...
// nolint:dupl
func PocketTransactionsDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 3,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "3.12",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// PocketTxnWithNextAndPrevDataMockDBRows ...
//
//nolint:dupl
func PocketTxnWithNextAndPrevDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 3,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "3.12",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// ReversePocketTransactionsDataMockDBRows ...
//
//nolint:dupl
func ReversePocketTransactionsDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "3.12",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          constants.PocketFundingTransactionSubType,
		AccountID:                   "**********",
		AccountAddress:              "**********000",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}
