package resources

import pairingService "gitlab.myteksi.net/dakota/payment/pairing-service/api"

// PairingServiceResponseMock ...
func PairingServiceResponseMock() *pairingService.LookUpInfoResponse {
	return &pairingService.LookUpInfoResponse{
		Data: &pairingService.LookUpInfoResult{
			ReferenceID: "",
			Account:     nil,
			Proxy: &pairingService.Proxy{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
			Status:       "",
			StatusReason: "",
			TransferType: "",
		},
	}
}
