package resources

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
)

func outboundAuthorizationTransaction() []*storage.TransactionsData {
	batchDetails, _ := json.<PERSON>(map[string]string{"loanTransactionID": "12345"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "06593e45-557d-4752-b326-882408384de1",
		ClientBatchID:               "abc456efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		BatchDetails:                batchDetails,
		TransactionDomain:           "LENDING",
		TransactionType:             "LINE_OF_CREDIT",
		TransactionSubtype:          "UTILISE_LIMIT",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		TransactionDetails:          nil,
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}}
}

func drawdownTransaction() []*storage.TransactionsData {
	transactionDetailsDrawDown, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "ed179e2742fc4b2da750f887695b3973",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "DRAWDOWN",
		TransactionSubtype:          "RPP_NETWORK",
		TransactionDetails:          transactionDetailsDrawDown,
		AccountID:                   "**********",
		AccountAddress:              "DISBURSEMENT_CONTRA",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

//nolint:dupl
func settlementTransaction() []*storage.TransactionsData {
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "ed179e2742fc4b2da750f887695b3973",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "LINE_OF_CREDIT",
		TransactionSubtype:          "UTILISE_LIMIT",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

func releaseTransaction() []*storage.TransactionsData {
	batchDetails, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "ed179e2742fc4b2da750f887695b3973",
		ClientBatchID:               "abc456efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		BatchDetails:                batchDetails,
		TransactionDomain:           "LENDING",
		TransactionType:             "LINE_OF_CREDIT",
		TransactionSubtype:          "UTILISE_LIMIT",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "0",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}}
}

// SuccessfulDrawdownTransactionsForLending ...
func SuccessfulDrawdownTransactionsForLending() []*storage.TransactionsData {
	return drawdownTransaction()
}

// UtilizeLimitSuccessfulDrawdownTransactionsForLending ...
func UtilizeLimitSuccessfulDrawdownTransactionsForLending() []*storage.TransactionsData {
	return outboundAuthorizationTransaction()
}

// AuthFailedDrawdownTransactionsForLending ...
func AuthFailedDrawdownTransactionsForLending() []*storage.TransactionsData {
	var transactions []*storage.TransactionsData
	transactions = append(transactions, outboundAuthorizationTransaction()...)
	transactions = append(transactions, releaseTransaction()...)
	return transactions
}

// SettlementPendingDrawdownTransactionsForLending ...
func SettlementPendingDrawdownTransactionsForLending() []*storage.TransactionsData {
	var transactions []*storage.TransactionsData
	transactions = append(transactions, outboundAuthorizationTransaction()...)
	transactions = append(transactions, drawdownTransaction()...)
	return transactions
}

// SettlementFailedDrawdownTransactionsForLending ...
func SettlementFailedDrawdownTransactionsForLending() []*storage.TransactionsData {
	var transactions []*storage.TransactionsData
	transactions = append(transactions, outboundAuthorizationTransaction()...)
	transactions = append(transactions, drawdownTransaction()...)
	transactions = append(transactions, releaseTransaction()...)
	return transactions
}

// SettlementSuccessfulDrawdownTransactionsForLending ...
func SettlementSuccessfulDrawdownTransactionsForLending() []*storage.TransactionsData {
	var transactions []*storage.TransactionsData
	transactions = append(transactions, outboundAuthorizationTransaction()...)
	transactions = append(transactions, drawdownTransaction()...)
	transactions = append(transactions, settlementTransaction()...)
	return transactions
}

// RepaymentTransactionsForLending ...
func RepaymentTransactionsForLending() []*storage.TransactionsData {
	transactionDetailsDrawDown, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "80056543657_13__1685691099785801000_REPAY_PRINCIPAL",
		ClientBatchID:               "80056543657_13__1685691099785801000",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 2,
		ClientTransactionID:         "80056543657_13__1685691099785801000_REPAY_PRINCIPAL",
		ClientBatchID:               "80056543657_13__1685691099785801000",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 3,
		ClientTransactionID:         "ed179e2742fc4b2da750f887695b3973",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "200",
		TransactionCurrency:         "MYR",
		TransactionDetails:          transactionDetailsDrawDown,
		BalanceAfterTransaction:     "200",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// ProcessingLoanDetailsMockDBRowsForLending ...
func ProcessingLoanDetailsMockDBRowsForLending(transactionType string) []*storage.LoanDetail {
	drawDownDetails := fetchDrawDownDetailsMock()
	return []*storage.LoanDetail{{
		ID:                   2,
		LoanTransactionID:    "12345",
		Amount:               1512,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "",
		TransactionDomain:    "LENDING",
		TransactionType:      transactionType,
		TransactionSubType:   "RPP_NETWORK",
		Status:               "PROCESSING",
		StatusDetail:         nil,
		DisbursementDetail:   drawDownDetails,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}}
}

// FailedLoanDetailsMockDBRowsForLending ...
func FailedLoanDetailsMockDBRowsForLending(transactionType string) []*storage.LoanDetail {
	drawDownDetails := fetchDrawDownDetailsMock()
	repaymentDetails := fetchFailedRepaymentDetailsMock()
	return []*storage.LoanDetail{{
		ID:                   2,
		LoanTransactionID:    "12345",
		Amount:               1512,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "abc123efg",
		TransactionDomain:    "LENDING",
		TransactionType:      transactionType,
		TransactionSubType:   "RPP_NETWORK",
		Status:               "FAILED",
		StatusDetail:         nil,
		DisbursementDetail:   drawDownDetails,
		RepaymentDetail:      repaymentDetails,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}}
}

// CompletedLoanDetailsMockDBRowsForLending ...
func CompletedLoanDetailsMockDBRowsForLending(transactionType string) []*storage.LoanDetail {
	drawDownDetails := fetchDrawDownDetailsMock()
	repaymentDetails := fetchRepaymentDetailsMock()
	return []*storage.LoanDetail{{
		ID:                   2,
		LoanTransactionID:    "12345",
		Amount:               1512,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "abc123efg",
		TransactionDomain:    "LENDING",
		TransactionType:      transactionType,
		TransactionSubType:   "RPP_NETWORK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   drawDownDetails,
		RepaymentDetail:      repaymentDetails,
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}}
}

// TransactionsMockDBRowsForLending ...
// nolint:dupl
func TransactionsMockDBRowsForLending() []*storage.TransactionsData {
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "LENDING",
		TransactionType:             "DRAWDOWN",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	},
	}
}

// TransactionsDataMockDBRowsForLending ...
func TransactionsDataMockDBRowsForLending() []*storage.TransactionsData {
	transactionDetailsRepayment, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	transactionDetailsDrawDown23456, _ := json.Marshal(map[string]string{"loanTransactionID": "23456"})
	transactionDetailsDrawDown11111, _ := json.Marshal(map[string]string{"loanTransactionID": "11111"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "DRAWDOWN",
		TransactionSubtype:          "INTRABANK",
		TransactionDetails:          transactionDetailsDrawDown23456,
		AccountID:                   "**********",
		AccountAddress:              "DISBURSEMENT_CONTRA",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "DRAWDOWN",
		TransactionSubtype:          "INTRABANK",
		TransactionDetails:          transactionDetailsDrawDown11111,
		AccountID:                   "**********",
		AccountAddress:              "DISBURSEMENT_CONTRA",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		TransactionDetails:          transactionDetailsRepayment,
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// TransactionsDataMockDBRowsForNormal ...
func TransactionsDataMockDBRowsForNormal() []*storage.TransactionsData {
	transactionDetailsRecovery, _ := json.Marshal(map[string]string{"loanTransactionID": "54321"})
	return []*storage.TransactionsData{{ID: 4,
		ClientTransactionID:         "8748025063d5728fa25a1e5b9a71bbe4",
		ClientBatchID:               "hij123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		TransactionDetails:          transactionDetailsRecovery,
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// TransactionsDataMockDBRowsForWriteOff ...
func TransactionsDataMockDBRowsForWriteOff() []*storage.TransactionsData {
	transactionDetailsRecovery, _ := json.Marshal(map[string]string{"loanTransactionID": "54321", "loan_asset_account_id": "**************", "loc_asset_account_id": "**********", "recovery_distribution": "{\"account_id\":\"**************\",\"total_component\":\"151.25\",\"final_recovery\":true,\"total_due_before\":\"151.25\"}", "sub_status": "BAD_DEBT", "total_recovery_component": "151.25"})
	return []*storage.TransactionsData{{ID: 5,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "hij123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "WRITE_OFF",
		TransactionSubtype:          "BAD_DEBT_RECOVERY_INTRABANK",
		TransactionDetails:          transactionDetailsRecovery,
		AccountID:                   "*********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "151.25",
		TransactionCurrency:         "MYR",
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 6,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "hij123abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "WRITE_OFF",
		TransactionSubtype:          "BAD_DEBT_RECOVERY_INTRABANK",
		TransactionDetails:          transactionDetailsRecovery,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "151.25",
		TransactionCurrency:         "MYR",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// LoanDetailsMockDBRowsForTxnSearch ...
// nolint:dupl
func LoanDetailsMockDBRowsForTxnSearch() []*storage.LoanDetail {
	repaymentDetails := fetchRepaymentDetailsMock()
	drawDownDetails := fetchDrawDownDetailsMock()
	return []*storage.LoanDetail{
		{
			ID:                   1,
			LoanTransactionID:    "23456",
			Amount:               13,
			Currency:             "MYR",
			AccountID:            "**********",
			AccountDetail:        nil,
			PaymentTransactionID: "abc123efg",
			TransactionDomain:    "LENDING",
			TransactionType:      "DRAWDOWN",
			TransactionSubType:   "INTRABANK",
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   drawDownDetails,
			RepaymentDetail:      nil,
			CreatedAt:            time.Unix(**********, 0).UTC(),
			UpdatedAt:            time.Unix(**********, 0).UTC(),
		},
		{
			ID:                   2,
			LoanTransactionID:    "12345",
			Amount:               1512,
			Currency:             "MYR",
			AccountID:            "**********",
			AccountDetail:        nil,
			PaymentTransactionID: "efg1111abd",
			TransactionDomain:    "LENDING",
			TransactionType:      "REPAYMENT",
			TransactionSubType:   "RPP_NETWORK",
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   nil,
			RepaymentDetail:      repaymentDetails,
			CreatedAt:            time.Unix(**********, 0).UTC(),
			UpdatedAt:            time.Unix(**********, 0).UTC(),
		},
		{
			ID:                   3,
			LoanTransactionID:    "11111",
			Amount:               513,
			Currency:             "MYR",
			AccountID:            "**********",
			AccountDetail:        nil,
			PaymentTransactionID: "efg123abd",
			TransactionDomain:    "LENDING",
			TransactionType:      "DRAWDOWN",
			TransactionSubType:   "RPP_NETWORK",
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   drawDownDetails,
			RepaymentDetail:      nil,
			CreatedAt:            time.Unix(**********, 0).UTC(),
			UpdatedAt:            time.Unix(**********, 0).UTC(),
		},
	}
}

// LoanDetailsMockDBRowsForRecovery ...
// nolint:dupl
func LoanDetailsMockDBRowsForRecovery() []*storage.LoanDetail {
	return []*storage.LoanDetail{{
		ID:                   5,
		LoanTransactionID:    "54321",
		Amount:               1512,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "hij123abd",
		TransactionDomain:    "LENDING",
		TransactionType:      "REPAYMENT",
		TransactionSubType:   "RPP_NETWORK",
		Status:               "COMPLETED",
		RepaymentDetail:      fetchRecoveryDetailsMock(),
		CreatedAt:            time.Unix(**********, 0).UTC(),
		UpdatedAt:            time.Unix(**********, 0).UTC(),
	}}
}

// GetLoanDetailsByAccountID ...
func GetLoanDetailsByAccountID() []*storage.LoanDetail {
	rows := append(LoanDetailsMockDBRowsForTxnSearch(), LoanDetailsMockDBRowsForRecovery()...)
	return rows
}

// GetAllTransactionsPrevNextExistMockDBResponseForLending ...
func GetAllTransactionsPrevNextExistMockDBResponseForLending() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRowsForLending()
	return rows[1:]
}

// GetAllTransactionsFirstPageMockDBResponseForLendingRepayment ...
func GetAllTransactionsFirstPageMockDBResponseForLendingRepayment() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRowsForLending()
	return rows[2:]
}

// GetAllTransactionsFirstPageMockDBResponseForLendingDrawDown ...
func GetAllTransactionsFirstPageMockDBResponseForLendingDrawDown() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRowsForLending()
	return rows[0:1]
}

// GetAllTransactionsFirstPageMockDBResponseForLendingDrawDownAndRecovery ...
func GetAllTransactionsFirstPageMockDBResponseForLendingDrawDownAndRecovery() []*storage.TransactionsData {
	rows := GetAllTransactionsFirstPageMockDBResponseForLendingDrawDown()
	rows = append(rows, TransactionsDataMockDBRowsForNormal()...)
	return rows
}

// GetAllTransactionsFirstPageMockDBResponseForLending ...
func GetAllTransactionsFirstPageMockDBResponseForLending() []*storage.TransactionsData {
	rows := TransactionsDataMockDBRowsForLending()
	return rows
}
