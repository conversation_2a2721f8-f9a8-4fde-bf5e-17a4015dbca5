package resources

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
)

// PaymentDataSTM453MockDBRows ...
// nolint:dupl
func PaymentDataSTM453MockDBRows() []*storage.PaymentDetail {
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	counterPartyAccountDetail1, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	metadata := []byte(`{"remarks": "Thanks for the dinner", "transferType": "FAST_NETWORK", "externalID":"fastID123"}`)
	statusDetails, _ := json.Marshal(dto.StatusDetails{Reason: "ACCOUNT_CLOSED", Description: "Rejected due to account is closed"})
	locale := utils.GetLocale()
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       "TRANSFER_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "12345",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              metadata,
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			AccountID:             "**********",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    3,
			TransactionID:         "efg1111abd",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail1,
			Status:                "FAILED",
			StatusDetails:         statusDetails,
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    4,
			TransactionID:         "efg1sda22d",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail1,
			Status:                "FAILED",
			StatusDetails:         statusDetails,
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// STM453TransactionInfoData ...
func STM453TransactionInfoData() *dto.STM453TransactionInfo {
	return &dto.STM453TransactionInfo{
		TransactionType:        "Incoming (Rejected)",
		TransactionSubtype:     "ATXN",
		TransactionAmount:      -20,
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		FastTransactionID:      "fastID123",
		SenderAccountNumber:    "12345",
		RecipientAccountNumber: "54321",
		RecipientBank:          "GXSPSGS0XXX",
	}
}
