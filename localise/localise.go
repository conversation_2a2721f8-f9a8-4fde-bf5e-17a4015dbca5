// Package localise ...
package localise

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

// Localizer ...
var Localizer *i18n.Localizer
var bundle *i18n.Bundle

// Init ...
func Init(conf *config.AppConfig) {
	var lang string
	bundle = i18n.NewBundle(language.English)
	bundle.RegisterUnmarshalFunc("json", json.Unmarshal)
	path := os.Getenv("LOCALISATION_PATH")
	_, err := bundle.LoadMessageFile(path + "/en.json")
	if err != nil {
		slog.Error(fmt.Errorf("[Localisation] Failed to load en.json file %w", err))
	}
	switch conf.Locale.Language {
	case "id":
		_, err = bundle.LoadMessageFile(path + "/id.json")
		if err != nil {
			slog.Error(fmt.Errorf("[Localisation] Failed to load id.json file %w", err))
		}
		lang = language.Indonesian.String()
	default:
		if config.GetTenant() == tenants.TenantMY {
			_, err = bundle.LoadMessageFile(path + "/dbmy/en.json")
			if err != nil {
				slog.Error(fmt.Errorf("[Localisation] Failed to load en.json file %w", err))
			}
		}
		lang = language.English.String()
	}
	Localizer = i18n.NewLocalizer(bundle, lang, "en")
}

// Translate ...
func Translate(s string) string {
	localizeConfig := i18n.LocalizeConfig{
		MessageID: s,
	}
	localization, _ := Localizer.Localize(&localizeConfig)
	return localization
}
