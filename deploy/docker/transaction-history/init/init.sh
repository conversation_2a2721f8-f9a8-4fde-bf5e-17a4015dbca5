#!/usr/bin/env sh

./transaction-history

app_exit_code=$?

# if application exit with error, we will skip all manual quit ops and exit with the same error code
if [ $app_exit_code -ne 0 ]; then
  echo "ERROR: Application exited with error"
  if [ "${MANUAL_QUIT_ISTIO}" = "true" ]; then
    echo "INFO: Will not quit Istio sidecar"
  fi
  if [ "${MANUAL_QUIT_VAULT}" = "true" ]; then
    echo "INFO: Will not quit Vault sidecar"
  fi
  exit $app_exit_code
fi

# quit vault sidecar
if [ "${MANUAL_QUIT_VAULT}" = "true" ]; then
  i=1
  vault_exit_code=""
  while [ $i -lt 6 ] && [ "${vault_exit_code}" != "0" ]; do
    echo "INFO: Shutdown Vault sidecar manually, attempt ${i}"
    curl -v -XPOST http://localhost:8200/agent/v1/quit
    vault_exit_code=$?
    if [ "${vault_exit_code}" = "0" ]; then
      break
    fi
    i=$((i+1))
    sleep 1
  done

  if [ "${vault_exit_code}" != "0" ]; then
    echo "ERROR: Vault sidecar quit with error code ${vault_exit_code}"
  else
    echo "INFO: Successfully call Vault sidecar quit api"
  fi
fi

# we make quit istio sidecar as last step as network topology changes might cause issue to steps that require network communication
if [ "${MANUAL_QUIT_ISTIO}" = "true" ]; then
  echo "INFO: Shutdown Istio sidecar manually via quit api"
  curl -sf -XPOST http://localhost:15020/quitquitquit
  istio_exit_code=$?
  if [ "${istio_exit_code}" -ne 0 ]; then
    echo "ERROR: Istio sidecar quit with error code ${istio_exit_code}"
  else
    echo "INFO: Successfully call Istio sidecar quit api"
  fi
fi
