BEGIN;

CREATE TABLE `interest_aggregate_v2`
(
    `id`                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `account_id`                VARCHAR(64)     NOT NULL,
    `account_address`           VARCHAR(64),
    `total_interest_earned`     json          NOT NULL,
    `recent_day` json DEFAULT NULL,
    `monthly_history` json DEFAULT NULL,
    `currency`                  CHAR(3)         NOT NULL DEFAULT '',
    `created_at`                DATETIME(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at`                DATETIME(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    <PERSON>IMARY KEY (`id`),
    UNIQUE KEY `uk_account_id_account_address` (`account_id`, `account_address`),
    <PERSON><PERSON>Y                         `index_created_at` (`created_at`),
    <PERSON><PERSON>Y                         `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;
