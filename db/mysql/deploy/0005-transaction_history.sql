-- Deploy transaction_history:0005-transaction_history to mysql

BEGIN;

CREATE TABLE `scheduler_lock`
(
    `id`           BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `scheduler_id` VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Unique Identifier for the scheduler',
    `last_run_at`  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date and time at which last time scheduler was run',
    `created_at`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY            `index_created_at` (`created_at`),
    KEY            `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;
