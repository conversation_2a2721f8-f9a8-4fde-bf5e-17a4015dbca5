-- Deploy transaction_history:0004-transaction_history to mysql

BEGIN;

CREATE TABLE `interest_aggregate`
(
    `id`                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `account_id`                VARCHAR(64)     NOT NULL DEFAULT '',
    `account_address`           VARCHAR(64),
    `total_interest_earned`     BIGINT          NOT NULL,
    `currency`                  CHAR(3)         NOT NULL DEFAULT '',
    `created_at`                DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_account_id_account_address` (`account_id`, `account_address`),
    KEY                         `index_created_at` (`created_at`),
    <PERSON><PERSON>Y                         `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;
