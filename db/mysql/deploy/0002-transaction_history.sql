-- Deploy transaction_history:0002-transaction_history to mysql

BEGIN;

CREATE TABLE `account_calendar_activity`
(
    `id`         BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `account_id` VARCHAR(36) NOT NULL DEFAULT '',
    `year`       BIGINT      NOT NULL,
    `months`     VARCHAR(64) NOT NULL DEFAULT '',
    `created_at` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y          `index_account_id` (`account_id`),
    <PERSON><PERSON><PERSON>          `index_created_at` (`created_at`),
    <PERSON>EY          `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_accountID_year` (`account_id`, `year`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;
