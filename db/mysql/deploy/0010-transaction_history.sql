-- Deploy transaction_history:0010-transaction_history.sql to mysql

BEGIN;

CREATE TABLE `loan_detail`
(
    `id`                     BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `loan_transaction_id`    VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Unique transaction_id generated by LoanExperience (its also withdrawal_id for borrow and repayment_id for repayment)',
    `amount`                 BIGINT      NOT NULL COMMENT 'Disbursement or Repayment amount ',
    `currency`               CHAR(3)     NOT NULL COMMENT 'Currency associated with amount',
    `account_id`             VARCHAR(64) NOT NULL COMMENT 'Account Identifier of limit or loan',
    `account_detail`         JSON        NOT NULL,
    `payment_transaction_id` VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Unique transaction_id generated by payments (its also transfer_id)',
    `transaction_domain`     VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_type`       VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_subtype`    VARCHAR(64) NOT NULL DEFAULT '',
    `status`                 VARCHAR(64) NOT NULL DEFAULT '',
    `status_detail`          JSON        NOT NULL,
    `disbursement_detail`    JSON        NOT NULL,
    `repayment_detail`       JSON        NOT NULL,
    `created_at`             datetime(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at`             datetime(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`, `created_at`),
    KEY                      `index_accountID_loanTxID` (`account_id`, `loan_transaction_id`),
    KEY                      `index_updated_at` (`updated_at`),
    KEY                      `index_created_at` (`created_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci PARTITION BY RANGE (to_days(`created_at`)) (
    PARTITION p2024_02
  VALUES
    LESS THAN (739311) ENGINE = InnoDB,
    PARTITION p2024_03
  VALUES
    LESS THAN (739342) ENGINE = InnoDB,
    PARTITION p2024_04
  VALUES
    LESS THAN (739372) ENGINE = InnoDB,
    PARTITION p2024_05
  VALUES
    LESS THAN (739403) ENGINE = InnoDB,
    PARTITION p2024_06
  VALUES
    LESS THAN (739433) ENGINE = InnoDB,
    PARTITION p2024_07
  VALUES
    LESS THAN (739464) ENGINE = InnoDB,
    PARTITION p2024_08
  VALUES
    LESS THAN (739495) ENGINE = InnoDB,
    PARTITION p2024_09
  VALUES
    LESS THAN (739525) ENGINE = InnoDB,
    PARTITION p2024_10
  VALUES
    LESS THAN (739556) ENGINE = InnoDB,
    PARTITION p2024_11
  VALUES
    LESS THAN (739586) ENGINE = InnoDB,
    PARTITION p2024_12
  VALUES
    LESS THAN (739617) ENGINE = InnoDB,
    PARTITION pMAX
  VALUES
    LESS THAN MAXVALUE ENGINE = InnoDB
);

COMMIT;
