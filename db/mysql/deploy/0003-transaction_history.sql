-- Deploy transaction_history:0003-transaction_history to mysql

BEGIN;

CREATE TABLE `transactions_data`
(
    `id`                              BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `client_transaction_id`           VARCHAR(255) NOT NULL DEFAULT '',
    `client_batch_id`                 VARCHAR(255) NOT NULL DEFAULT '',
    `tm_posting_instruction_batch_id` VARCHAR(255) NOT NULL,
    `tm_transaction_id`               VARCHAR(255) NOT NULL,
    `batch_remarks`                   VARCHAR(255),
    `batch_status`                    VARCHAR(255),
    `batch_error_type`                VARCHAR(255),
    `batch_details`                   JSON,
    `batch_error_message`             VARCHAR(255),
    `transaction_domain`              VARCHAR(64),
    `transaction_type`                VARCHAR(64),
    `tm_transaction_type`             VARCHAR(64)  NOT NULL DEFAULT '',
    `transaction_subtype`             VARCHAR(64),
    `transaction_details`             JSON,
    `transaction_violations`          JSO<PERSON>,
    `account_id`                      VARCHA<PERSON>(64),
    `account_address`                 VARCHAR(64),
    `account_asset`                   VARCHAR(64),
    `account_phase`                   VA<PERSON>HAR(64),
    `debit_or_credit`                 VARCHAR(64),
    `transaction_amount`              VARCHAR(64),
    `transaction_currency`            VARCHAR(16),
    `balance_after_transaction`       VARCHAR(64),
    `metadata`                        JSON,
    `batch_insertion_timestamp`       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `batch_value_timestamp`           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_at`                      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`, `batch_value_timestamp`),
    KEY                               `index_accountID_tmBatchID` (`account_id`, `tm_posting_instruction_batch_id`) COMMENT 'To search based on account ID and posting batch ID',
    KEY                               `index_batch_value_timestamp` (`batch_value_timestamp`) COMMENT 'To search based on date range',
    KEY                               `index_batch_insertion_timestamp` (`batch_insertion_timestamp`) COMMENT 'To search based on date range for interest entries',
    KEY                               `index_created_at` (`created_at`),
    KEY                               `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci PARTITION BY RANGE (to_days(`batch_value_timestamp`)) (
  PARTITION p2021
  VALUES
    LESS THAN (738521) ENGINE = InnoDB,
    PARTITION p2022_02
  VALUES
    LESS THAN (738580) ENGINE = InnoDB,
    PARTITION p2022_04
  VALUES
    LESS THAN (738641) ENGINE = InnoDB,
    PARTITION p2022_06
  VALUES
    LESS THAN (738702) ENGINE = InnoDB,
    PARTITION p2022_08
  VALUES
    LESS THAN (738764) ENGINE = InnoDB,
    PARTITION p2022_10
  VALUES
    LESS THAN (738825) ENGINE = InnoDB,
    PARTITION p2022_12
  VALUES
    LESS THAN (738885) ENGINE = InnoDB,
    PARTITION p2023_02
  VALUES
    LESS THAN (738944) ENGINE = InnoDB,
    PARTITION p2023_04
  VALUES
    LESS THAN (739005) ENGINE = InnoDB,
    PARTITION p2023_06
  VALUES
    LESS THAN (739066) ENGINE = InnoDB,
    PARTITION p2023_08
  VALUES
    LESS THAN (739128) ENGINE = InnoDB,
    PARTITION p2023_10
  VALUES
    LESS THAN (739189) ENGINE = InnoDB,
    PARTITION p2023_12
  VALUES
    LESS THAN (739250) ENGINE = InnoDB,
    PARTITION pMAX
  VALUES
    LESS THAN MAXVALUE ENGINE = InnoDB
);

COMMIT;
