-- Deploy transaction_history:0006-transaction_history to mysql

BEGIN;

CREATE TABLE `card_transaction_detail`
(
    `id`                       BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `card_transaction_id`      CHAR(36)    NOT NULL DEFAULT '',
    `card_id`                  CHAR(36)    NOT NULL DEFAULT '',
    `transaction_domain`       VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_type`         VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_subtype`      VARCHAR(64) NOT NULL DEFAULT '',
    `transfer_type`            VARCHAR(64) NOT NULL DEFAULT '',
    `amount`                   BIGINT      NOT NULL,
    `currency`                 CHAR(3)     NOT NULL DEFAULT '',
    `original_amount`          BIGINT      NOT NULL,
    `original_currency`        CHAR(3)     NOT NULL DEFAULT '',
    `capture_amount`           BIGINT      NOT NULL,
    `captured_amount_till_date` BIGINT               DEFAULT 0,
    `account_id`               VARCHAR(64) NOT NULL DEFAULT '',
    `account`                  JSON ,
    `merchant_description`     VARCHAR(100)         DEFAULT '',
    `status`                   VARCHAR(64) NOT NULL DEFAULT '',
    `status_details`           JSON ,
    `metadata`                 JSON        NOT NULL,
    `tail_card_number`         CHAR(4)     NOT NULL ,
    `created_at`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `creation_timestamp`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `value_timestamp`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                        `index_creation_timestamp` (`creation_timestamp`),
    KEY                        `index_updated_at` (`updated_at`),
    KEY                        `index_created_at` (`created_at`),
    KEY                        `index_card_transaction_id` (`card_transaction_id`),
    KEY                        `index_account_id_card_transaction_id` (`account_id`,`card_transaction_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;