-- Deploy transaction_history:0008-transaction_history to mysql

BEGIN;

-- -- XXX Add DDLs here.
-- CREATE INDEX index_client_transaction_id
-- ON transactions_data (client_transaction_id);
--
-- CREATE INDEX index_client_batch_id
-- ON transactions_data (client_batch_id);
--
-- CREATE INDEX index_external_id
-- ON transactions_data ((CAST(transaction_details->>'$.external_id' AS CHAR(255)) COLLATE utf8mb4_bin));

ALTER TABLE `transactions_data` ADD INDEX `index_client_transaction_id` (`client_transaction_id`);
ALTER TABLE `transactions_data` ADD INDEX `index_client_batch_id` (`client_batch_id`);
ALTER TABLE `transactions_data` ADD INDEX `index_external_id` ((CAST(transaction_details->>'$.external_id' AS CHAR(255)) COLLATE utf8mb4_bin));

COMMIT;