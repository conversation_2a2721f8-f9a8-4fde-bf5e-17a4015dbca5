-- Deploy transaction_history:0007-transaction_history to mysql

BEGIN;

-- XXX Add DDLs here.
ALTER TABLE account_calendar_activity
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE interest_aggregate
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE payment_detail
    MODIFY creation_timestamp datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY value_timestamp datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    <PERSON><PERSON><PERSON><PERSON> created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE scheduler_lock
    MODIFY last_run_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE transactions_data
    MODIFY batch_insertion_timestamp datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY batch_value_timestamp datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

COMMIT;