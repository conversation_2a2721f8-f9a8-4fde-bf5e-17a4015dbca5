-- Deploy transaction_history:0000-transaction_history to mysql

BEGIN;

CREATE TABLE `payment_detail`
(
    `id`                       BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `uuid`                     CHAR(36)    NOT NULL DEFAULT '',
    `transaction_id`           CHAR(36)    NOT NULL DEFAULT '',
    `transaction_domain`       VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_type`         VARCHAR(64) NOT NULL DEFAULT '',
    `transaction_subtype`      VARCHAR(64) NOT NULL DEFAULT '',
    `amount`                   BIGINT      NOT NULL,
    `currency`                 CHAR(3)     NOT NULL DEFAULT '',
    `account_id`               VARCHAR(64) NOT NULL DEFAULT '',
    `counter_party_account_id` VARCHAR(64) NOT NULL DEFAULT '',
    `account`                  JSON        NOT NULL,
    `counter_party_account`    JSON        NOT NULL,
    `status`                   VARCHAR(64) NOT NULL DEFAULT '',
    `status_details`           JSON        NOT NULL,
    `metadata`                 JSON        NOT NULL,
    `created_at`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `creation_timestamp`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `value_timestamp`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`               DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uuid` (`uuid`),
    KEY                        `index_creation_timestamp` (`creation_timestamp`),
    KEY                        `index_updated_at` (`updated_at`),
    KEY                        `index_created_at` (`created_at`),
    KEY                        `index_transaction_id` (`transaction_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;