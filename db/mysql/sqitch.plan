%syntax-version=1.0.0
%project=transaction_history

0000-transaction_history 2021-08-11T10:06:05Z Naveen Kewalramani <naveen.kew<PERSON><PERSON><PERSON>@ITIN000399-MAC> # Add Transaction History schema
0001-transaction_history 2021-08-17T12:49:39Z Naveen <PERSON>wal<PERSON>ni <naveen.kew<PERSON><PERSON>ni@ITIN000399-MAC> # Add AccountID & TxID composite Index
0002-transaction_history 2021-08-19T15:53:10Z Naveen Kewalramani <naveen.kew<PERSON><PERSON>ni@ITIN000399-MAC> # AccountCalendarActivity Schema
0003-transaction_history 2021-10-29T05:01:02Z Naveen Kewalramani <naveen.kew<PERSON><PERSON>ni@ITIN000399-MAC> # TransactionsData Table Schema
0004-transaction_history 2022-06-17T04:10:47Z System Administrator <root@ITIN000593-MAC> # Add interest-aggregate schema
0005-transaction_history 2022-07-05T05:47:16Z Naveen Kewalramani <naveen.k<PERSON><PERSON><PERSON><PERSON>@ITIN000489-MAC> # SchedulerLockTable
0007-transaction_history 2023-05-09T15:00:00Z <PERSON><PERSON> <<EMAIL>> # Increase datetime precision for all datetime columns
0008-transaction_history 2023-07-20T16:38:00Z Sheh Jing Tan <<EMAIL>> # Add in index for client_txn_id, client_batch_id and external_id
0010-transaction_history 2024-02-23T02:40:59Z Ong Jun Rong <<EMAIL>> # Add LoanDetail Table Schema
