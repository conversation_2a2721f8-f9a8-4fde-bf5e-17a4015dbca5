package queries

// SetBalanceWithBalanceID uses to update balance fields. This is backward compatible with 'null' value
const SetBalanceWithBalanceID = `
	UPDATE transactions_data
	SET
    	metadata = case when metadata = cast('null' as json) or metadata is null then ? else JSON_SET(metadata, '$.tm_balance_id', ?) end,
    	balance_after_transaction = ?,
    	updated_at = ?
	WHERE id = ? AND (metadata->>'$.tm_balance_id' IS NULL OR metadata->>'$.tm_balance_id' NOT LIKE ?)
`
