// Package queries ...
package queries

// SelectSchedulerLockQuery ...
const SelectSchedulerLockQuery = `select id, scheduler_id, last_run_at, created_at, updated_at from scheduler_lock where scheduler_id = ? limit 1 for UPDATE`

// UpdateSchedulerLockQuery ...
const UpdateSchedulerLockQuery = `update scheduler_lock set last_run_at = ? where scheduler_id = ?`

// SelectTxnDataForPostingProcessingQuery query the transaction data for posting processing by the keys combination
const SelectTxnDataForPostingProcessingQuery = `SELECT id,
       client_transaction_id,
       client_batch_id,
       tm_posting_instruction_batch_id,
       tm_transaction_id,
       batch_remarks,
       batch_status,
       batch_error_type,
       coalesce(batch_details, json_object())       as batch_details,
       batch_error_message,
       transaction_domain,
       transaction_type,
       tm_transaction_type,
       transaction_subtype,
       coalesce(transaction_details, json_object()) as transaction_details,
       coalesce(transaction_violations, '[]')       as transaction_violations,
       account_id,
       account_address,
       account_asset,
       account_phase,
       debit_or_credit,
       transaction_amount,
       transaction_currency,
       balance_after_transaction,
       coalesce(metadata, json_object())            as metadata,
       batch_insertion_timestamp,
       batch_value_timestamp,
       created_at,
       updated_at
FROM transactions_data
WHERE tm_posting_instruction_batch_id = ?
  AND tm_transaction_id = ?
  AND account_id = ?
  AND account_phase = ?
  AND account_address = ?`

// InsertTxnDataCommand ...
const InsertTxnDataCommand = `INSERT INTO transactions_data (client_transaction_id,
                               client_batch_id, tm_posting_instruction_batch_id,
                               tm_transaction_id, tm_transaction_type, batch_remarks, batch_status, batch_error_type,
                               batch_details, batch_error_message, batch_insertion_timestamp, batch_value_timestamp,
                               transaction_domain, transaction_type, transaction_subtype, transaction_details,
                               transaction_violations, account_id, account_address, account_asset, account_phase,
                               debit_or_credit, transaction_amount, transaction_currency, balance_after_transaction,
                               metadata, created_at, updated_at)
    VALUE (
           ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
           ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
    )`

// UpdateTxnDataTransferCommand updates transfer related columns only in transaction data
const UpdateTxnDataTransferCommand = `UPDATE transactions_data SET client_transaction_id = ?,
                               client_batch_id = ?, tm_posting_instruction_batch_id = ?,
                               tm_transaction_id = ?, tm_transaction_type = ?, batch_remarks = ?, batch_status = ?, batch_error_type = ?,
                               batch_details = ?, batch_error_message = ?, batch_insertion_timestamp = ?, batch_value_timestamp = ?,
                               transaction_domain = ?, transaction_type = ?, transaction_subtype = ?, transaction_details = ?,
                               transaction_violations = ?, account_id = ?, account_address = ?, account_asset = ?, account_phase = ?,
                               debit_or_credit = ?, transaction_amount = ?, transaction_currency = ?, updated_at = ? WHERE id = ?`

// SelectTxnDataByExternalID query the transaction data for ops search
const SelectTxnDataByExternalID = `SELECT id,
       client_transaction_id,
       client_batch_id,
       tm_posting_instruction_batch_id,
       tm_transaction_id,
       batch_remarks,
       batch_status,
       batch_error_type,
       coalesce(batch_details, json_object())       as batch_details,
       batch_error_message,
       transaction_domain,
       transaction_type,
       tm_transaction_type,
       transaction_subtype,
       coalesce(transaction_details, json_object()) as transaction_details,
       coalesce(transaction_violations, '[]')       as transaction_violations,
       account_id,
       account_address,
       account_asset,
       account_phase,
       debit_or_credit,
       transaction_amount,
       transaction_currency,
       balance_after_transaction,
       coalesce(metadata, json_object())            as metadata,
       batch_insertion_timestamp,
       batch_value_timestamp,
       created_at,
       updated_at
FROM transactions_data 
WHERE transaction_details->>'$.external_id' = ?`
