-- Deploy transaction_history:0007-transaction_history to mysql

BEGIN;

-- XXX Add DDLs here.
ALTER TABLE account_calendar_activity
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE interest_aggregate
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE payment_detail
    MODIFY creation_timestamp datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY value_timestamp datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    M<PERSON><PERSON>Y created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE scheduler_lock
    MODIFY last_run_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE transactions_data
    MODIFY batch_insertion_timestamp datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY batch_value_timestamp datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

COMMIT;