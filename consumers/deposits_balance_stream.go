package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/storage"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/metrics"
	depositsBalance "gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/deposits"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	depositsBalanceDTO = "DepositsBalanceEvent"
)

var (
	dbConsumer kafkareader.Client
	dbTopic    string
)

// startConsumeDepositsBalanceStream creates Kafka Read and Register the method responsible to consumer
func startConsumeDepositsBalanceStream(conf Config) {
	reader, err := streamStaticReader(
		context.Background(),
		DepositsCoreStream,
		convertConfig(conf.depositBalanceKafkaConf, depositsBalanceDTO),
		&deposits_balance_event.DepositsBalanceEvent{},
		func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		},
	)
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf.depositBalanceKafkaConf, err))
	}

	dbConsumer = reader
	dbTopic = conf.depositBalanceKafkaConf.TopicName
	registerDepositsBalanceHandler(constants.DepositsBalanceTag, dbConsumer.GetDataChan(), conf)
}

var registerDepositsBalanceHandler = func(tag string, ch <-chan *kafkareader.Entity, conf Config) {
	wg.Go(tag, func() {
		consumeDBStream(slog.NewContextWithLogger(context.Background(), logger), ch, conf)
	})
}

// consumeDBStream receives event from stream and pass it to processing methods
// deprecated: use retryable stream instead
func consumeDBStream(ctx context.Context, ch <-chan *kafkareader.Entity, conf Config) {
	for event := range ch {
		data, ok := event.Event.(*deposits_balance_event.DepositsBalanceEvent)
		logTags := append(commonTags(dbTopic, depositsBalanceDTO), eventMetaTags(event)...)
		eventCtx := slog.AddTagsToContext(ctx, logTags...)
		if !ok {
			StatsDClient.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreBalanceKafkaMessage,
				metrics.FailedTag,
				"errorOccured:wrong_entity")
			slog.FromContext(eventCtx).Fatal(constants.DepositsBalanceTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(dbTopic, depositsBalanceDTO)...)
			continue
		}
		eventCtx = slog.AddTagsToContext(ctx, slog.CustomTag("tm_balance_id", data.Event.BalanceID), slog.CustomTag("tm_batch_id", data.Event.PostingInstructionBatchID))

		slog.FromContext(eventCtx).Info(constants.DepositsBalanceTag, fmt.Sprintf("%+v", data))
		err := depositsBalance.HandleDBStream(eventCtx, data, storage.DB, StatsDClient, conf.historyConfig)
		if err != nil {
			slog.FromContext(eventCtx).Warn(constants.DepositsBalanceTag, fmt.Sprintf("handling stream event failed, err: %s", err.Error()),
				commonTags(dbTopic, depositsBalanceDTO)...)
		}
	}
}
