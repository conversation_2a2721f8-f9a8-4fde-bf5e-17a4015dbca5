package consumers

import "gitlab.myteksi.net/dakota/schemas/streams"

// Streams IDs.
const (
	PaymentEngineStream                      streams.StreamID = "pestream"
	DepositsCoreStream                       streams.StreamID = "dcstream"
	DigicardTxStream                         streams.StreamID = "digicardTxStream"
	LoanCoreTxStream                         streams.StreamID = "loanCoreTxStream"
	LoanExpTxStream                          streams.StreamID = "loanExpTxStream"
	DepositsCoreForInterestAggregationStream streams.StreamID = "dcForInterestAggStream"
)
