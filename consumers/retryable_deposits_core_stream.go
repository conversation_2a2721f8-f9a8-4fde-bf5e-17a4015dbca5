package consumers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/snd/streamsdk/kafka"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/metrics"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/deposits"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// DepositsCoreHandler ...
type DepositsCoreHandler struct {
	featFlags featureflag.Repo
}

// GetEventSchema ...
func (h DepositsCoreHandler) GetEventSchema() kafka.Entity {
	return &deposits_core_tx.DepositsCoreTx{}
}

// Handle ...
func (h DepositsCoreHandler) Handle(ctx context.Context, entity kafka.Entity) error {
	StatsDClient.Duration(logTag, metrics.StreamConsumeLatencyMetric, entity.GetStreamInfo().StreamTime, constants.DepositsCoreTag)
	defer StatsDClient.Duration(logTag, metrics.StreamProcessLatencyMetric, time.Now(), constants.DepositsCoreTag)

	ctx = featureflag.NewContextWithFeatureFlags(ctx, h.featFlags)

	data, ok := entity.(*deposits_core_tx.DepositsCoreTx)
	logTags := append(commonTags(dcTopic, depositsCoreDTO), infoMetaTags(data.GetStreamInfo())...)
	eventCtx := slog.AddTagsToContext(ctx, logTags...)
	if !ok {
		StatsDClient.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreKafkaMessage,
			metrics.FailedTag,
			"errorOccured:wrong_entity")
		slog.FromContext(eventCtx).Fatal(constants.DepositsCoreTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", entity),
			commonTags(dcTopic, depositsCoreDTO)...)
		return errors.New("wrong entity in reader")
	}
	eventCtx = slog.AddTagsToContext(eventCtx, slog.CustomTag("tm_batch_id", data.ID))

	slog.FromContext(eventCtx).Info(constants.DepositsCoreTag, fmt.Sprintf("%+v", data))
	err := deposits.HandleDepositsCoreStream(eventCtx, data, StatsDClient, storage.DB)
	if err != nil {
		handledErr := handleErrorForRetryableStream(eventCtx, err, isRetryableDepositCoreStreamEnabled, true, constants.DepositsCoreTag)
		if handledErr == nil {
			slog.FromContext(eventCtx).Warn(constants.DepositsCoreTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(dcTopic, depositsCoreDTO)...)
		}
		return handledErr
	}
	return nil
}
