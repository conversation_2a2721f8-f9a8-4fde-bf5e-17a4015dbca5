package consumers

import (
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

type streamLogger struct {
	slogger slog.YallLogger
}

func (l *streamLogger) Fatal(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.Fatal(eventType, message)
		return
	}
	l.slogger.Fatal(eventType, fmt.Sprintf(message, args...))
}

func (l *streamLogger) DFatal(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.DFatal(eventType, message)
		return
	}
	l.slogger.DFatal(eventType, fmt.Sprintf(message, args...))
}

func (l *streamLogger) Error(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.Error(eventType, message)
		return
	}
	l.slogger.Error(eventType, fmt.Sprintf(message, args...))
}

func (l *streamLogger) Warn(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.Warn(eventType, message)
		return
	}
	l.slogger.Warn(eventType, fmt.Sprintf(message, args...))
}

func (l *streamLogger) Info(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.Info(eventType, message)
		return
	}
	l.slogger.Info(eventType, fmt.Sprintf(message, args...))
}

func (l *streamLogger) Debug(eventType string, message string, args ...interface{}) {
	if len(args) == 0 {
		l.slogger.Debug(eventType, message)
		return
	}
	l.slogger.Debug(eventType, fmt.Sprintf(message, args...))
}
