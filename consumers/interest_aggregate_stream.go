package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/deposits"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	InterestAggStreamDTO = "DepositsCoreTx"
)

var (
	interestAggConsumer kafkareader.Client
	interestAggTopic    string
)

// startConsumeInterestAgg creates Kafka Read and Register the method responsible to consumer
func startConsumeInterestAgg(conf *config.KafkaConfig) {
	reader, err := streams.NewStaticReader(
		context.Background(),
		DepositsCoreForInterestAggregationStream,
		convertConfig(conf, InterestAggStreamDTO),
		&deposits_core_tx.DepositsCoreTx{},
		func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		},
	)
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	interestAggConsumer = reader
	registerHandler(context.Background(), InterestAggStreamDTO, interestAggConsumer.GetDataAckChan(), consumeDepositsCoreStreamForInterestAgg)
}

// consumeDepositsCoreStreamForInterestAgg receives event from stream and pass it to processing methods
func consumeDepositsCoreStreamForInterestAgg(ctx context.Context, entity kafka.Entity) error {
	data, ok := entity.(*deposits_core_tx.DepositsCoreTx)
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("client_batch_id", data.BatchID))
	if !ok {
		slog.FromContext(ctx).Fatal(constants.InterestAggStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", entity))
		return nil
	}
	slog.FromContext(ctx).Debug(constants.InterestAggStreamLogTag, fmt.Sprintf("%+v", data))
	return deposits.HandleDepositsCoreStreamForInterestAgg(ctx, redisClient, interestAggConfig, data, StatsDClient)
}
