package consumers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/metrics"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/processor/deposits"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// InterestAggregateHandler ...
type InterestAggregateHandler struct {
	featFlags featureflag.Repo
}

// GetEventSchema ...
func (h InterestAggregateHandler) GetEventSchema() kafka.Entity {
	return &deposits_core_tx.DepositsCoreTx{}
}

// Handle ...
func (h InterestAggregateHandler) Handle(ctx context.Context, entity kafka.Entity) error {
	StatsDClient.Duration(logTag, metrics.StreamConsumeLatencyMetric, entity.GetStreamInfo().StreamTime, constants.InterestAggStreamLogTag)
	defer StatsDClient.Duration(logTag, metrics.StreamProcessLatencyMetric, time.Now(), constants.InterestAggStreamLogTag)

	ctx = featureflag.NewContextWithFeatureFlags(ctx, h.featFlags)

	data, ok := entity.(*deposits_core_tx.DepositsCoreTx)
	logTags := append(commonTags(dcTopic, InterestAggStreamDTO), infoMetaTags(data.GetStreamInfo())...)
	eventCtx := slog.AddTagsToContext(ctx, logTags...)
	if !ok {
		StatsDClient.Count1(string(servicename.TransactionHistory), metrics.InterestAggMessage,
			metrics.FailedTag,
			"errorOccured:wrong_entity")
		slog.FromContext(eventCtx).Fatal(constants.InterestAggStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", entity),
			commonTags(dcTopic, InterestAggStreamDTO)...)
		return errors.New("wrong entity in reader")
	}
	eventCtx = slog.AddTagsToContext(eventCtx, slog.CustomTag("tm_batch_id", data.ID))

	slog.FromContext(eventCtx).Debug(constants.InterestAggStreamLogTag, fmt.Sprintf("%+v", data))
	err := deposits.HandleDepositsCoreStreamForInterestAgg(eventCtx, redisClient, interestAggConfig, data, StatsDClient)
	if err != nil {
		handledErr := handleErrorForRetryableStream(eventCtx, err, isRetryableInterestAggStreamEnabled, true, constants.InterestAggStreamLogTag)
		if handledErr == nil {
			slog.FromContext(eventCtx).Warn(constants.InterestAggStreamLogTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(interestAggTopic, InterestAggStreamDTO)...)
		}
		return handledErr
	}
	return nil
}
