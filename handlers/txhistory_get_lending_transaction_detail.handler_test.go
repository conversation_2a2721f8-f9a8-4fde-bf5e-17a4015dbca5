package handlers

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	txnhistoryUtils "gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	paymentServiceMock "gitlab.myteksi.net/dakota/payment/pairing-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetLendingTransactionDetail(t *testing.T) {
	mockCustomerMasterServiceClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsReplicaReadEnabled").Return(false)
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnhistoryUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)
	ctx = featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
		Account: &accountService.Account{
			CifNumber: "test-cif",
		},
	}, nil)
	txHistoryService := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterServiceClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, FeatureFlags: flagRepo,
	}
	mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)

	t.Run("missing-parameter-accountID", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "",
			TransactionID: "abcd-efgh-ijkl",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'accountID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetLendingTransactionDetailResponse)(nil), response)
	})

	t.Run("missing-parameter-transactionID", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID: "50051",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'transactionID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionDetailResponse)(nil), response)
	})

	t.Run("invalid-productVariantCode", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:          "50051",
			TransactionID:      "abcd-efgh-ijkl",
			ProductVariantCode: "INVALID",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'productVariantCode' is invalid."}},
		}

		response, err := txHistoryService.GetLendingTransactionDetail(context.Background(), req)
		assert.Equal(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionDetailResponse)(nil), response)
	})

	t.Run("No transaction db data", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.TransactionsDataD = mockTransactionData
		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.LoanDetailD = mockLoanDetailDao
		expectedResp := customErr.BuildErrorResponse(customErr.ResourceNotFound, "No transaction data found")

		_, err := txHistoryService.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, expectedResp, err)
	})

	t.Run("error-response-request", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := txHistoryService.GetLendingTransactionDetail(ctx, req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}

func TestGetLendingTransactionDetailUnauthorizedPath(t *testing.T) {
	// Mocking External Services
	mockCustomerMasterServiceClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockPairingServiceClient := &paymentServiceMock.PairingService{}
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsReplicaReadEnabled").Return(false)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
		Account: &accountService.Account{
			CifNumber: "test-cif",
		},
	}, nil)

	txhistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterServiceClient, AccountServiceClient: mockAccountServiceClient, PairingServiceClient: mockPairingServiceClient, FeatureFlags: flagRepo}

	t.Run("unauthorized-account-request", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		}
		mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil)
		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txhistoryInterface.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionDetailResponse)(nil), response)
	})

	t.Run("invalid-cif-request", func(t *testing.T) {
		req := &api.GetLendingTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		}
		expectedError := servus.ServiceError{HTTPCode: 401, Code: "unauthorized", Message: "CIF_MAPPING_NOT_FOUND:cif number not found", Errors: []servus.ErrorDetail(nil)}
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, expectedError)

		response, err := txhistoryInterface.GetLendingTransactionDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionDetailResponse)(nil), response)
	})
}
