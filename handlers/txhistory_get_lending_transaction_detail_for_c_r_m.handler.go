package handlers // nolint: dupl

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLendingTransactionDetailForCRM ...
// nolint: dupl
func (t *TxHistoryService) GetLendingTransactionDetailForCRM(ctx context.Context, req *api.GetLendingTransactionDetailForCRMRequest) (*api.GetLendingTransactionDetailResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	lendingTransactionDetailReq := &api.GetLendingTransactionDetailRequest{
		AccountID:          req.AccountID,
		TransactionID:      req.TransactionID,
		ProductVariantCode: req.ProductVariantCode,
	}

	// Check whether the request is valid
	if validationErrors := handlerlogic.GetLendingTransactionDetailRequestValidator(lendingTransactionDetailReq); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Check Authorization via CIF Number
	if _, authErr := CheckIfAuthorized(ctx, req.AccountID, t); authErr != nil {
		return nil, authErr
	}

	getTransactionDetailObj := handlerlogic.GetLendingTransactionDetailStruct{
		AccountServiceClient:            t.AccountServiceClient,
		PairingServiceClient:            t.PairingServiceClient,
		TransactionHistoryServiceConfig: t.AppConfig.TransactionHistoryServiceConfig,
		FeatureFlags:                    t.AppConfig.FeatureFlags,
	}
	response, err := getTransactionDetailObj.GetLendingTransactionDetail(ctx, lendingTransactionDetailReq)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v, with response: %+v", req, response))
	return response, nil
}
