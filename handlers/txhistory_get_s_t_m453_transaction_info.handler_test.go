package handlers

import (
	"context"
	"database/sql"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestGetSTM453TransactionInfo(t *testing.T) {
	txHistoryService := TxHistoryService{}

	t.Run("missing-parameter-transactionID", func(t *testing.T) {
		req := &api.GetSTM453TransactionInfoRequest{}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'transactionID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetSTM453TransactionInfo(context.Background(), req)
		assert.Equal(t, err, expectedError)
		assert.Nil(t, response)
	})

	t.Run("paymentDetail-notFound", func(t *testing.T) {
		req := &api.GetSTM453TransactionInfoRequest{
			TransactionID: "abc1234ac",
		}

		//mocking
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetSTM453TransactionInfo(context.Background(), req)

		expectedResponse := &api.GetSTM453TransactionInfoResponse{}
		assert.Equal(t, response, expectedResponse)
		assert.NoError(t, err)
	})

	t.Run("fastReferenceID-notFound", func(t *testing.T) {
		req := &api.GetSTM453TransactionInfoRequest{
			TransactionID: "abc1234ac",
		}

		// mocking
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetSTM453TransactionInfo(context.Background(), req)

		expectedResponse := &api.GetSTM453TransactionInfoResponse{}
		assert.Equal(t, response, expectedResponse)
		assert.NoError(t, err)
	})

	t.Run("snowflakeGet-error", func(t *testing.T) {
		req := &api.GetSTM453TransactionInfoRequest{
			TransactionID: "abc1234ac",
		}

		// mocking
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataSTM453MockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		mockSnowflakeClient := &storage.MockSnowflakeQueryImpl{}
		mockSnowflakeClient.On("GetSTM453TransactionDetail", mock.Anything).Return(&sql.Rows{}, fmt.Errorf("some error"))
		handlerlogic.SnowflakeStruct = mockSnowflakeClient

		response, err := txHistoryService.GetSTM453TransactionInfo(context.Background(), req)

		expectedResponse := &api.GetSTM453TransactionInfoResponse{}
		assert.Equal(t, response, expectedResponse)
		assert.NoError(t, err)
	})
}
