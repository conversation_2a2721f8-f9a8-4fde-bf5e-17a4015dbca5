package handlers

import (
	"context"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	txnhistoryUtils "gitlab.com/gx-regional/dbmy/transaction-history/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
)

func TestListAccountTransactionsSearch(t *testing.T) {
	locale := utils.GetLocale()
	tests := []struct {
		testDesc                            string
		req                                 *api.GetTransactionsHistoryRequest
		expectedResponse                    *api.GetTransactionsHistoryResponse
		LookUpCifFromCustomerMasterResponse *customerMasterApi.LookupCIFNumberResponse
		LookUpCifFromCustomerMasterError    error

		CheckPermissionsForAccountResponse *accountServiceApi.CheckPermissionsForAccountResponse
		CheckPermissionsForAccountError    error

		TransactionDataDBFindResponse []*storage.TransactionsData
		TransactionDataDBFindError    error

		GetAccountDetailsByAccountIDResponse *accountServiceApi.GetAccountResponse
		GetAccountDetailsByAccountIDError    error

		ListCASAAccountsForCustomerDetailResponse *accountServiceApi.ListCASAAccountsForCustomerDetailResponse
		ListCASAAccountsForCustomerDetailError    error

		isErrorExpected bool
		expectedErr     error
	}{
		{
			testDesc:                                  "Should return  error when account id is missing",
			req:                                       &api.GetTransactionsHistoryRequest{AccountID: ""},
			isErrorExpected:                           true,
			LookUpCifFromCustomerMasterResponse:       nil,
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        nil,
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             nil,
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      nil,
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: nil,
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters"),
		},
		{
			testDesc:                                  "Should return  error when page size is greater than max page size",
			req:                                       &api.GetTransactionsHistoryRequest{AccountID: "test-id", PageSize: 251},
			isErrorExpected:                           true,
			LookUpCifFromCustomerMasterResponse:       nil,
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        nil,
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             nil,
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      nil,
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: nil,
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters"),
		},
		{
			testDesc:                                  "Should return  error when page size is lesser than min page size",
			req:                                       &api.GetTransactionsHistoryRequest{AccountID: "test-id", PageSize: -1},
			isErrorExpected:                           true,
			LookUpCifFromCustomerMasterResponse:       nil,
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        nil,
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             nil,
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      nil,
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: nil,
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters"),
		},
		{
			testDesc:                                  "Should return error when CheckPermissionAccountService give error",
			req:                                       &api.GetTransactionsHistoryRequest{AccountID: "test-id", PageSize: 150},
			isErrorExpected:                           true,
			expectedResponse:                          nil,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        nil,
			CheckPermissionsForAccountError:           customErr.BuildErrorResponse(customErr.InternalServerError, "Failed to connect accounts-service"),
			TransactionDataDBFindResponse:             nil,
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      nil,
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: nil,
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               customErr.BuildErrorResponse(customErr.InternalServerError, "Failed to connect accounts-service"),
		},
		{
			testDesc:                                  "Should return error when error returned from TransactionDataDB call",
			req:                                       &api.GetTransactionsHistoryRequest{AccountID: "test-id", PageSize: 150},
			isErrorExpected:                           true,
			expectedResponse:                          nil,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             nil,
			TransactionDataDBFindError:                customErr.DefaultInternalServerError,
			GetAccountDetailsByAccountIDResponse:      nil,
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: nil,
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               customErr.DefaultInternalServerError,
		},
		{
			testDesc: "Should return success for the first page when no error received",
			req: &api.GetTransactionsHistoryRequest{
				AccountID: "**********",
				StartDate: "2021-08-01",
				EndDate:   "2021-08-31",
				PageSize:  2,
			},
			isErrorExpected:                           false,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             resources.GetAllTransactionsFirstPageMockDBResponse(),
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: responses.ListCASAAccountsForCustomerDetailResponse(),
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               nil,
			expectedResponse:                          responses.GetAllTransactionsFirstPageResponseForListTransaction(),
		},
		{
			testDesc: "Should return success for the no date filters when no error received",
			req: &api.GetTransactionsHistoryRequest{
				AccountID: "12345",
				PageSize:  3,
			},
			isErrorExpected:                           false,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             resources.TransactionsDataMockDBRows(),
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: responses.ListCASAAccountsForCustomerDetailResponse(),
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               nil,
			expectedResponse:                          responses.GetAllTransactionsOnlyPageResponse(),
		},
		{
			testDesc: "Should return success for the last page when no error received",
			req: &api.GetTransactionsHistoryRequest{
				AccountID:      "**********",
				StartDate:      "2021-08-01",
				EndDate:        "2021-08-31",
				PageSize:       2,
				StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
			},
			isErrorExpected:                           false,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             resources.GetAllTransactionsLastPageMockDBResponse(),
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: responses.ListCASAAccountsForCustomerDetailResponse(),
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               nil,
			expectedResponse:                          responses.GetAllTransactionsLastPageResponseForListResponse(),
		},
		{
			testDesc: "Should return success for backward scrolling when no error received",
			req: &api.GetTransactionsHistoryRequest{
				AccountID:   "**********",
				StartDate:   "2021-08-01",
				EndDate:     "2021-08-31",
				PageSize:    2,
				EndingAfter: "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			},
			isErrorExpected:                           false,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             resources.GetAllTransactionsBackwardScrollingMockDBResponse(),
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: responses.ListCASAAccountsForCustomerDetailResponse(),
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               nil,
			expectedResponse:                          responses.GetAllTransactionsBackwardScrollingPrevPageResponse(),
		},
		{
			testDesc: "Should return success for prev next page exist when no error received",
			req: &api.GetTransactionsHistoryRequest{
				AccountID:      "**********",
				StartDate:      "2021-08-01",
				EndDate:        "2021-08-31",
				PageSize:       1,
				StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
			},
			isErrorExpected:                           false,
			LookUpCifFromCustomerMasterResponse:       &customerMasterApi.LookupCIFNumberResponse{},
			LookUpCifFromCustomerMasterError:          nil,
			CheckPermissionsForAccountResponse:        &accountServiceApi.CheckPermissionsForAccountResponse{Status: constants.AccountPermissionAllowed},
			CheckPermissionsForAccountError:           nil,
			TransactionDataDBFindResponse:             resources.GetAllTransactionsBackwardScrollingMockDBResponse(),
			TransactionDataDBFindError:                nil,
			GetAccountDetailsByAccountIDResponse:      responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:         nil,
			ListCASAAccountsForCustomerDetailResponse: responses.ListCASAAccountsForCustomerDetailResponse(),
			ListCASAAccountsForCustomerDetailError:    nil,
			expectedErr:                               nil,
			expectedResponse:                          responses.GetAllTransactionsPrevNextBothExistResponse(),
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.testDesc, func(t *testing.T) {
			flagRepo := featureflag.NewMockRepo(t)
			flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true).Maybe()
			flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(true).Maybe()
			flagRepo.On("IsReplicaReadEnabled").Return(false).Maybe()
			flagRepo.On("IsBoostPocketNameQueryEnabled").Return(true).Maybe()
			ctx := context.Background()
			activeProfileObj := activeProfile.ActiveProfile{
				ProfileID:   "test-cif",
				ProfileType: "CIF",
			}
			ctx = txnhistoryUtils.AddActiveProfileToHeader(ctx, activeProfileObj)
			ctx = featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
			mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
			mockAccountServiceClient := &accountServiceMock.AccountService{}
			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					DefaultCurrency: locale.Currency,
				},
				IconConfig: config.IconConfig{
					SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
					SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
					DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
					TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
				},
			}
			constants.InitializeDynamicConstants(mockAppConfig)
			txInterface := TxHistoryService{
				CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, FeatureFlags: flagRepo,
			}
			// Mocking Methods of External Services
			mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
				test.LookUpCifFromCustomerMasterResponse, test.LookUpCifFromCustomerMasterError)
			mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
				test.CheckPermissionsForAccountResponse, test.CheckPermissionsForAccountError)
			mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(test.GetAccountDetailsByAccountIDResponse, test.GetAccountDetailsByAccountIDError)
			mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
				test.ListCASAAccountsForCustomerDetailResponse, test.ListCASAAccountsForCustomerDetailError)
			mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					CifNumber: "test-cif",
				},
			}, nil)

			// Mocking Payment Call
			mockStorageDAO := &storage.MockIPaymentDetailDAO{}
			mockStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return(resources.PaymentDetailMockDBRows(), nil)
			storage.PaymentDetailD = mockStorageDAO

			// mocking TransactionsData call
			mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
			mockTransactionDataStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(test.TransactionDataDBFindResponse, test.TransactionDataDBFindError)
			storage.TransactionsDataD = mockTransactionDataStorageDAO

			got, err := txInterface.ListAccountTransactionsSearch(ctx, test.req)

			if test.isErrorExpected {
				assert.Error(t, err, test.testDesc)
				assert.Equal(t, test.expectedErr, err, test.testDesc)
			} else {
				assert.Equal(t, test.expectedResponse, got, test.testDesc)
			}
		})
	}
}
