package handlers

import (
	"context"
	"errors"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetInternalTransactionDetail(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	txHistoryService := TxHistoryService{
		AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
		resources.GetAccountImageDetailsSampleResponse(), nil)

	t.Run("missing-parameter-accountID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			TransactionID: "abcd-efgh-ijkl",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'accountID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), response)
	})

	t.Run("missing-parameter-transactionID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "50051",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'transactionID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, err, expectedError)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), response)
	})

	t.Run("empty-response-request", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData
		actualResp, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), actualResp)
		assert.Error(t, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No data present in transactionDataDB"), err)
	})

	t.Run("error-response-request", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("Intrabank-transaction", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[4]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentDataMockDBRows()[3]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("FundIn-transaction", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[1]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[3]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("ReceiveMoney-transaction", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[2]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[4]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[1]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("SendMoney-transaction", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[3]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[5]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[2]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("err-getExternalTransactionID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[3]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[8]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Error(t, customErr.BuildErrorResponse(customErr.ResourceNotFound, "Failed to get externalTransactionID"), err)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), response)
	})

	t.Run("Momo-transaction", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "momo12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[8]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.MooMooTransactionsMockDBRows()[1]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentMooMooDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}
