package handlers

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	txnHistortUtils "gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetAllTransactionsParameterMissingPath(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("accountID-missing", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`accountID` is a mandatory field."}},
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txInterface.GetAccountTransactionsSearch(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionsHistoryResponse)(nil), response)
	})
}

func TestGetAllTransactionsParameterHappyPath(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnHistortUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)

	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	// Mocking Payment Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find",
		mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
	).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResp := &api.GetTransactionsHistoryResponse{
			Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
			Data:  []api.TransactionHistoryResponse{},
		}

		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponse(), nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			responses.GetAccountDetailsByAccountIDResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsFirstPageResponse()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-no-date-filters", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			PageSize:  3,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsOnlyPageResponseForSearch()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-lastPage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       2, // Page Size > remaining entries
			StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsLastPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsLastPageResponse()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-onlyOnePage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5, // Page Size > No. of Matching Entries
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsOnlyPageResponseForSearch()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-backwardScrolling", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    2,
			EndingAfter: "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsBackwardScrollingMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsBackwardScrollingPrevPageResponseForSearch()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-prev-next-page-exist", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
		}
		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsBackwardScrollingMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsPrevNextBothExistResponseForSearch()
		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestGetAllTransactionsParameterErrorPath(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnHistortUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	t.Run("db-fetch-error", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetTransactionsHistoryResponse)(nil), response)
	})
}

func TestGetAllTransactionsUnauthorizedPath(t *testing.T) {
	locale := utils.GetLocale()
	// Mocking External Services
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
		Locale: utils.GetLocale(),
	}
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnHistortUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)

	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("unauthorized_path", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil)

		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txInterface.GetAccountTransactionsSearch(ctx, request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionsHistoryResponse)(nil), response)
	})
}
