package handlers

import (
	"context"
	"errors"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

var isCheckStatementStatusCtx = func(ctx context.Context, enabled bool) context.Context {
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsCheckStatementStatusEnabled").Return(enabled)
	return featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
}

// Test case for GetAccountCalendarActivities
func TestGetAccountCalendarActivities(t *testing.T) {
	cifNumber := "SG148087844878"
	digibankServiceID := "DIGIBANK"
	grabUserID := "test-grab-id"
	config.SetStatementReadyByDays(1)
	ctx := commonCtx.WithServiceID(context.Background(), digibankServiceID)
	ctx = isCheckStatementStatusCtx(ctx, true)
	ctx = commonCtx.WithUserID(ctx, grabUserID)
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockStorageDAO := &storage.MockIAccountCalendarActivityDAO{}

	storage.AccountCalendarActivityD = mockStorageDAO
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient,
		AccountServiceClient: mockAccountServiceClient,
		AppConfig: &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				PastMonthsThresholdForCalendarActivity: 12,
			},
		},
	}

	lookupCifRequest := &customerMasterApi.LookupCIFNumberRequest{
		ID: grabUserID,
		Target: &customerMasterApi.TargetGroup{
			ServiceID: digibankServiceID,
		},
	}
	listCASAAccountsForCifResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
		Accounts: []accountServiceApi.CASAAccountDetail{
			{
				Id:               "12345",
				ProductVariantID: "deposit_product",
				AccountType:      "CASA",
				Status:           "ACTIVE",
				ProductSpecificParameters: map[string]string{
					"applicableHoldcodes": "",
				},
				Features: &accountServiceApi.Feature{
					Credit: true,
					Debit:  true,
				},
				OpeningTimestamp: time.Now(),
			},
			{
				Id:               "********",
				ParentAccountID:  "12345",
				ProductVariantID: "deposit_product",
				AccountType:      "CASA",
				AccountName:      "sub account",
				Status:           "ACTIVE",
				ProductSpecificParameters: map[string]string{
					"applicableHoldcodes": "",
				},
				Features: &accountServiceApi.Feature{
					Credit: true,
					Debit:  true,
				},
				OpeningTimestamp: time.Now(),
			}},
		MaxChildAccountLimit: 8,
	}
	listCASAAccountForCifRequest := &accountServiceApi.ListCASAAccountsForCustomerDetailRequest{CifNumber: cifNumber}

	t.Run("happy path", func(t *testing.T) {
		prevMonthDate := utils.ToPreviousMonth(time.Now())
		currYear := prevMonthDate.Year()
		mockDBResponse := []*storage.AccountCalendarActivity{{
			ID:        10,
			AccountID: "12345",
			Year:      utils.MustConvertToInt64(currYear),
			Months:    prevMonthDate.Format(utils.TimestampMonthFormat),
			UpdatedAt: time.Now(),
		}}
		getActivityReq := &api.GetAccountCalendarActivityRequest{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
		singleAccountListCASAResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
			Accounts: []accountServiceApi.CASAAccountDetail{
				{
					Id:               "12345",
					ProductVariantID: "deposit_product",
					AccountType:      "CASA",
					Status:           "ACTIVE",
					ProductSpecificParameters: map[string]string{
						"applicableHoldcodes": "",
					},
					Features: &accountServiceApi.Feature{
						Credit: true,
						Debit:  true,
					},
					OpeningTimestamp: time.Now(), // "2022-10-13T13:32:12Z"
				}},
			MaxChildAccountLimit: 8,
		}
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
			Return(singleAccountListCASAResponse, nil).Once()
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		// storage call mock
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil).Once()

		expectedResponse := &api.AccountCalendarActivityResponse{Dates: []api.Date{{
			Year:   strconv.Itoa(currYear),
			Months: []string{prevMonthDate.Format(utils.TimestampMonthFormat)},
		}}}

		activeProfileObj := activeProfile.ActiveProfile{
			ProfileID:   "SG148087844878",
			ProfileType: "CIF",
		}
		ctx = utils.AddActiveProfileToHeader(ctx, activeProfileObj)
		response, err := txInterface.GetAccountCalendarActivity(ctx, getActivityReq)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy path - no rows found", func(t *testing.T) {
		var emptyDBResponse []*storage.AccountCalendarActivity
		getActivityReq := &api.GetAccountCalendarActivityRequest{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
		singleAccountListCASAResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
			Accounts: []accountServiceApi.CASAAccountDetail{
				{
					Id:               "12345",
					ProductVariantID: "deposit_product",
					AccountType:      "CASA",
					Status:           "ACTIVE",
					ProductSpecificParameters: map[string]string{
						"applicableHoldcodes": "",
					},
					Features: &accountServiceApi.Feature{
						Credit: true,
						Debit:  true,
					},
					OpeningTimestamp: time.Now(), // "2022-10-13T13:32:12Z"
				}},
			MaxChildAccountLimit: 8,
		}
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
			Return(singleAccountListCASAResponse, nil).Once()

		// storage call mock
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(emptyDBResponse, nil).Once()

		expectedResponse := &api.AccountCalendarActivityResponse{Dates: []api.Date{}}
		response, err := txInterface.GetAccountCalendarActivity(ctx, getActivityReq)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("should return error when filtered account does not exist", func(t *testing.T) {
		getActivityReq := &api.GetAccountCalendarActivityRequest{AccountID: "54321"}
		listCASAAccountResponse := &accountServiceApi.ListCASAAccountsForCustomerDetailResponse{
			Accounts: []accountServiceApi.CASAAccountDetail{
				{
					Id:               "12345",
					ProductVariantID: "deposit_product",
					AccountType:      "CASA",
					Status:           "ACTIVE",
					ProductSpecificParameters: map[string]string{
						"applicableHoldcodes": "",
					},
					Features: &accountServiceApi.Feature{
						Credit: true,
						Debit:  true,
					},
					OpeningTimestamp: time.Now(), // "2022-10-13T13:32:12Z"
				}},
			MaxChildAccountLimit: 8,
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
			Return(listCASAAccountResponse, nil).Once()

		response, err := txInterface.GetAccountCalendarActivity(ctx, getActivityReq)
		assert.Error(t, err, customErr.BuildErrorResponse(customErr.ResourceNotFound, "Account not found"))
		assert.Equal(t, (*api.AccountCalendarActivityResponse)(nil), response)
	})

	t.Run("error - failed to retrieve AccountCalendarActivity from DB", func(t *testing.T) {
		getActivityReq := &api.GetAccountCalendarActivityRequest{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
			Return(listCASAAccountsForCifResponse, nil).Once()

		// storage call mock
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
			[]*storage.AccountCalendarActivity{}, errors.New("failed to retrieve rows from DB")).Once()

		response, err := txInterface.GetAccountCalendarActivity(ctx, getActivityReq)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.AccountCalendarActivityResponse)(nil), response)
	})

	t.Run("error - could not retrieve accounts through account-service", func(t *testing.T) {
		getActivityReq := &api.GetAccountCalendarActivityRequest{}
		accountServiceErr := errors.New("failed to retrieve accounts")
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, lookupCifRequest).
			Return(&customerMasterApi.LookupCIFNumberResponse{CifNumber: cifNumber}, nil).Once()
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, listCASAAccountForCifRequest).
			Return(nil, accountServiceErr).Once()

		_, err := txInterface.GetAccountCalendarActivity(ctx, getActivityReq)

		assert.Error(t, err, accountServiceErr)
	})
}
