package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetInternalTransactionDetail API to fetch transaction detail for internal usage
func (t *TxHistoryService) GetInternalTransactionDetail(ctx context.Context, req *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error) {
	slog.FromContext(ctx).Info(constants.GetInternalTransactionDetailLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetInternalTransactionDetailRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	getTransactionDetailObj := handlerlogic.GetTransactionDetailStruct{
		AccountServiceClient: t.AccountServiceClient,
	}
	response, err := getTransactionDetailObj.GetInternalTransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return response, nil
}
