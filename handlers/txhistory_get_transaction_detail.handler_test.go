package handlers

import (
	"context"
	"errors"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"
	"gitlab.com/gx-regional/dbmy/transaction-history/localise"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"
	txnhistoryUtils "gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
)

func TestGetTransactionDetail(t *testing.T) {
	locale := utils.GetLocale()
	ctx := context.Background()
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx = txnhistoryUtils.AddActiveProfileToHeader(ctx, activeProfileObj)
	mockCustomerMasterServiceClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	flagRepo := featureflag.NewMockRepo(t)
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true).Maybe()
	flagRepo.On("IsBoostPocketNameQueryEnabled").Return(true).Maybe()

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransferOut:             "https://assets.dev.bankfama.net/dev/transfer_money.png",
			Grab:                    "https://assets.dev.g-bank.app/txn-history/grab.png",
		},
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	txHistoryService := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterServiceClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, FeatureFlags: flagRepo,
	}
	mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
		resources.GetAccountImageDetailsSampleResponse(), nil)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
		Account: &accountService.Account{
			CifNumber: "test-cif",
			Id:        "**********",
		},
	}, nil)

	t.Run("missing-parameter-accountID", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			TransactionID: "abcd-efgh-ijkl",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'accountID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetTransactionDetailResponse)(nil), response)
	})

	t.Run("missing-parameter-transactionID", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID: "50051",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'transactionID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionDetailResponse)(nil), response)
	})

	t.Run("Payment-transaction", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDataMockDBRows(), nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-FundIn-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[4]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[3]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-ReceiveMoney-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[5]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[4]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[1]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-SendMoney-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[6]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[5]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[2]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-SendMoneyReversal-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[7]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[10]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[11]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-ReceiveMoneyReversal-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[8]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[11]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[12]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-FundInReversal-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetTransactionDetailResponse()[9]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[12]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[13]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-SpendMoney-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd12345",
		}
		expectedResp := responses.DBMYGetTransactionDetailForGRABResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.DBMYGrabTransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentGrabDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-Insurance-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abcd12345",
		}
		expectedResp := responses.DBMYGetTransactionDetailForInsuranceResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.DBMYInsuranceTransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentInsuranceDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("InterestPayout-transaction", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "sfg-132gsb34-88800",
		}
		expectedResp := responses.GetInterestEarnedTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.InterestPayoutTransactionsMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("TaxPayout-transaction", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "sfg-132gsb34-88800",
		}
		expectedResp := responses.GetTaxDeductedTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TaxPayoutTransactionsMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("OPS-transaction", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "sfg-132gsb34-88800",
		}
		expectedResp := responses.GetOPSTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.OPSTransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("empty-response-request", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		expectedResp := &api.GetTransactionDetailResponse{}
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("error-response-request", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("Earmark-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "sfg-132gsb34-88800",
		}
		expectedResp := responses.GetEarmarkTransactionResponse()[1]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.EarmarkTransactionsMockDBRows()[1]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("Earmark-transaction", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "sfg-132gsb34-88800",
		}
		expectedResp := responses.GetEarmarkTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.EarmarkTransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-CASA-loan-transaction-repayment", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "8007e9c10ea841c593bee2e65df599ed",
		}
		expectedResp := responses.GetLoanTransactionResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.LoanTransactionsMockDBRows()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentLoanDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		mockLoanDao := &storage.MockILoanDetailDAO{}
		mockLoanDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanTransactionDetailMockDBRow()[0]}, nil)
		storage.LoanDetailD = mockLoanDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("DBMY-CASA-loan-transaction-drawdown", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "8007e9c10ea841c593bee2e65df599ff",
		}
		expectedResp := responses.GetLoanTransactionResponse()[1]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.LoanTransactionsMockDBRows()[1]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockLoanDao := &storage.MockILoanDetailDAO{}
		mockLoanDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanTransactionDetailMockDBRow()[1]}, nil)
		storage.LoanDetailD = mockLoanDao

		response, err := txHistoryService.GetTransactionDetail(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}
func TestGetTransactionDetailUnauthorizedPath(t *testing.T) {
	// Mocking External Services
	mockCustomerMasterServiceClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsBizAuthorisationEnabled").Return(false)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountService.GetAccountResponse{
		Account: &accountService.Account{
			CifNumber: "test-cif",
		},
	}, nil)
	txhistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterServiceClient, AccountServiceClient: mockAccountServiceClient, FeatureFlags: flagRepo}

	t.Run("unauthorized-account-request", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		}
		mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil)
		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txhistoryInterface.GetTransactionDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionDetailResponse)(nil), response)
	})

	t.Run("invalid-cif-request", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		}
		expectedError := servus.ServiceError{HTTPCode: 401, Code: "unauthorized", Message: "CIF_MAPPING_NOT_FOUND:cif number not found", Errors: []servus.ErrorDetail(nil)}
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, expectedError)

		response, err := txhistoryInterface.GetTransactionDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionDetailResponse)(nil), response)
	})
}
