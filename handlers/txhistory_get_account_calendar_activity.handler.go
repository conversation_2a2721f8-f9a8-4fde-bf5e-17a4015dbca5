package handlers

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetAccountCalendarActivity implementation of GetAccountCalendarActivities via POST
func (t *TxHistoryService) GetAccountCalendarActivity(ctx context.Context, req *api.GetAccountCalendarActivityRequest) (*api.AccountCalendarActivityResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetAccountCalendarActivity"))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	//Check Authorization via CIF Number
	cifNumber, err := CheckIfAuthorized(ctx, req.AccountID, t)
	if err != nil {
		return nil, err
	}

	accounts, err := utils.GetAccountList(ctx, t.AccountServiceClient, cifNumber)
	accountIds := utils.GetAccountIdsFromList(accounts)
	if err != nil {
		return nil, err
	}
	if req.AccountID != "" {
		if !utils.SearchStringArray(accountIds, req.AccountID) {
			return nil, errors.BuildErrorResponse(errors.ResourceNotFound, "Account not found")
		}
		accountIds = []string{req.AccountID}
	}

	// Fetch Activity and Return Response
	currDate := time.Now()
	response, err := handlerlogic.GetAccountCalendarActivities(ctx, accountIds, currDate, t.AppConfig.TransactionHistoryServiceConfig.PastMonthsThresholdForCalendarActivity)
	if err != nil {
		return nil, err
	}
	isLending := isLendingAccount(req.AccountID, accounts)
	response = handlerlogic.FilterAccountCalanderActivities(ctx, response, currDate, req.AccountID, isLending)

	// Filter Response

	return &api.AccountCalendarActivityResponse{Dates: response}, nil
}

func isLendingAccount(accountID string, accounts []accountService.CASAAccountDetail) bool {
	isLending := false
	for _, account := range accounts {
		if accountID == account.Id && account.AccountType == constants.LendingAccountType {
			isLending = true
		}
	}
	return isLending
}
