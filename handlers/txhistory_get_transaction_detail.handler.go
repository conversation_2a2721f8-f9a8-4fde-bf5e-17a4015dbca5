package handlers //nolint: dupl

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetTransactionDetail via POST
func (t *TxHistoryService) GetTransactionDetail(ctx context.Context, req *api.GetTransactionDetailRequest) (*api.GetTransactionDetailResponse, error) {
	slog.FromContext(ctx).Info(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetTransactionDetailRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Check Authorization via CIF Number
	if _, authErr := CheckIfAuthorized(ctx, req.AccountID, t); authErr != nil {
		return nil, authErr
	}

	getTransactionDetailObj := handlerlogic.GetTransactionDetailStruct{
		AccountServiceClient: t.AccountServiceClient,
	}
	response, err := getTransactionDetailObj.GetTransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}
