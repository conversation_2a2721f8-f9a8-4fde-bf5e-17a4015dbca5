package handlers

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetPocketActivitiesRequestValidation(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/common/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/common/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/common/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/common/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("pocketID-missing", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pocketID` is a mandatory field."}},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
	t.Run("pocketType-missing", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:  "1234000",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pocketType` is a mandatory field."}},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
	t.Run("pocketType-invalid", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SPEND",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'pocketType' is invalid."}},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
	t.Run("pageSize greater than the MaxPageSize", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   251,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pageSize` greater than maxPageSize"}},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
	t.Run("pageSize smaller than the MinPageSize", func(t *testing.T) {
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   -1,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pageSize` less than minPageSize"}},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
}

func TestGetPocketActivitiesHappyPath(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/common/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/common/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/common/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/common/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)

	t.Run("no-matching-resources", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResp := &api.GetPocketActivitiesResponse{
			Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
			Data:  []api.TransactionHistoryResponse{},
		}

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesFirstPageResponse()
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
	t.Run("happy-path-no-date-filters", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			PageSize:   3,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesSinglePageResponse()
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-lastPage", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:       "*************",
			PocketType:     "SAVINGS",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       2, // Page Size > remaining entries
			StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwxLGVmZzEyM2FiZA==",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetPocketActivitiesLastPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesLastPageResponse()
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-onlyOnePage", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   5, // Page Size > No. of Matching Entries
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesSinglePageResponse()
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-backwardScrolling", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:    "*************",
			PocketType:  "SAVINGS",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    3,
			EndingAfter: "MjAyMS0wOC0yMFQwMjozMTo0MFosMywxLGVmZzExMTFhYmQ=",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.ReversePocketTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetPocketActivitiesBackwardScrollingPrevPageResponse()
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestGetPocketActivitiesErrorPath(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}

	t.Run("not a pocket error", func(t *testing.T) {
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedError := customErr.BuildErrorResponse(customErr.BadRequest, "The pocket in the request is invalid")
		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
	t.Run("db-fetch-error", func(t *testing.T) {
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
}

func TestGetPocketActivitiesUnauthorizedPath(t *testing.T) {
	t.Run("unauthorized_path", func(t *testing.T) {
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "",
					CifNumber:       "test-cif2",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient,
		}
		request := &api.GetPocketActivitiesRequest{
			PocketID:   "*************",
			PocketType: "SAVINGS",
			StartDate:  "2021-08-01",
			EndDate:    "2021-08-31",
			PageSize:   2,
		}

		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txInterface.GetPocketActivities(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivitiesResponse)(nil), response)
	})
}
