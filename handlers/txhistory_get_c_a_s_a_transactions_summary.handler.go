package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"

	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetCASATransactionsSummary via POST
func (t *TxHistoryService) GetCASATransactionsSummary(ctx context.Context, req *api.GetCASATransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error) {
	slog.FromContext(ctx).Info(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetCASATransactionsSummaryRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	var cifNumber string

	// Only check auth for T6
	clientIdentity := commonCtx.GetClientIdentity(ctx)
	if clientIdentity == servicename.SentryT6.ToString() {
		//Check Authorization via CIF Number
		lookupCifNumber, err := CheckIfAuthorized(ctx, req.AccountID, t)
		if err != nil {
			return nil, err
		}
		cifNumber = lookupCifNumber
	} else {
		lookupCifNumber, err := utils.GetCustomerIDWithActiveProfile(ctx, req.AccountID, t.AccountServiceClient, t.CustomerMasterDBMYClient)
		if err != nil {
			return nil, err
		}
		cifNumber = lookupCifNumber
	}

	var (
		response *api.GetCASATransactionsSummaryResponse
		err      error
	)
	switch config.GetTenant() {
	case tenants.TenantMY:
		logicImpl := &handlerlogic.CasaTransactionsSummaryImpl{AccountService: t.AccountServiceClient}
		response, err = logicImpl.GetTransactionsSummary(ctx, &handlerlogic.CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         cifNumber,
		})
	default:
		response, err = handlerlogic.GetTransactionsSummary(ctx, req)
	}
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}
