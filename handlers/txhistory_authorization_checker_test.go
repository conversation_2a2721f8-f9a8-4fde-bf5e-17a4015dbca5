package handlers

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customError "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerMasterDBMYApi "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

const (
	accountID       = "1234A"
	CifNumber       = "SG1234A"
	parentAccountID = "1234"
)

// TestCheckPermissionAccountService ...
func TestCheckPermissionAccountService(t *testing.T) {
	t.Run("allowed_path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking account service call
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := utils.CheckPermissionAccountService(context.Background(), accountID, CifNumber, txHistoryInterface.AccountServiceClient)
		assert.Nil(t, err)
		assert.Equal(t, string(accountServiceApi.AccountPermission_ALLOWED), response)
	})

	t.Run("forbidden_path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking account service call
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil)

		response, err := utils.CheckPermissionAccountService(context.Background(), accountID, CifNumber, txHistoryInterface.AccountServiceClient)
		assert.Nil(t, err)
		assert.Equal(t, string(accountServiceApi.AccountPermission_FORBIDDEN), response)
	})

	t.Run("error_path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking account service call
		accountServiceError := fmt.Errorf("some error occured")
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: ""}, accountServiceError)

		response, err := utils.CheckPermissionAccountService(context.Background(), accountID, CifNumber, txHistoryInterface.AccountServiceClient)
		assert.Error(t, accountServiceError, err)
		assert.Equal(t, "", response)
	})
}

// TestLookUpCifFromCustomerMaster ...
func TestLookUpCifFromCustomerMaster(t *testing.T) {
	ctx := context.Background()

	t.Run("happy-path-cif-number-found", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "SG1234A"}, nil)

		response, err := utils.LookUpCifFromCustomerMaster(ctx, txHistoryInterface.CustomerMasterClient)
		assert.Nil(t, err)
		assert.Equal(t, "SG1234A", response)
	})

	t.Run("cif-number-Notfound", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		expectedError := servus.ServiceError{HTTPCode: 401, Code: "unauthorized", Message: "CIF_MAPPING_NOT_FOUND:cif number not found", Errors: []servus.ErrorDetail(nil)}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, expectedError)

		response, err := utils.LookUpCifFromCustomerMaster(ctx, txHistoryInterface.CustomerMasterClient)
		assert.Error(t, err, expectedError)
		assert.Equal(t, "", response)
	})

	t.Run("api-call-error", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		apiCallError := servus.ServiceError{HTTPCode: 500, Code: "internalServerError", Message: "internalServerError", Errors: []servus.ErrorDetail(nil)}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, apiCallError)

		expectedError := customError.BuildErrorResponse(customError.InternalServerError, "Failed to connect customer-master")
		response, err := utils.LookUpCifFromCustomerMaster(ctx, txHistoryInterface.CustomerMasterClient)
		assert.Error(t, err, expectedError)
		assert.Equal(t, "", response)
	})
}

// TestGetAccountFromAccountService ...
func TestGetAccountFromAccountService(t *testing.T) {
	t.Run("allowed_path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking account service call
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       CifNumber,
				},
			}, nil)

		response, err := utils.GetAccountFromAccountService(context.Background(), accountID, txHistoryInterface.AccountServiceClient)
		assert.Nil(t, err)
		assert.Equal(t, parentAccountID, response.ParentAccountID)
	})

	t.Run("error_path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking account service call
		expectedErr := customError.DefaultInternalServerError
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(nil, errors.New("failed to connect accounts-service"))

		response, err := utils.GetAccountFromAccountService(context.Background(), accountID, txHistoryInterface.AccountServiceClient)
		assert.Error(t, expectedErr, err)
		assert.Nil(t, response)
	})
}

// TestCheckIfAuthorized ...
func TestCheckIfAuthorized(t *testing.T) {
	accountID := "1234A"
	ctx := context.Background()
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "SG1234A",
		ProfileType: "CIF",
	}
	ctx = utils.AddActiveProfileToHeader(ctx, activeProfileObj)

	t.Run("happy-path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}
		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "SG1234A"}, nil)

		// mocking account service call
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.Nil(t, err)
	})

	t.Run("error-path-CheckPermissionsForAccount-failed", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}
		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "SG1234A"}, nil)

		// mocking account service call
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.Error(t, customError.BuildErrorResponse(customError.Unauthorized, "AccountPermission_FORBIDDEN"), err)
	})

	t.Run("error-path-CheckPermissionsForAccount-api-call-error", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "SG1234A"}, nil)

		// mocking account service call
		accountServiceError := fmt.Errorf("some error occured")
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: ""}, accountServiceError)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.Error(t, customError.BuildErrorResponse(customError.Unauthorized, "AccountPermission_FORBIDDEN"), err)
	})
}

func TestCheckIfAuthorizedWithBizAuthorisation(t *testing.T) {
	accountID := "1234A"
	cif := "MY1234"
	bif := "BIF1234"
	status := customerMasterDBMYApi.BusinessRelationshipStatus_ACTIVE

	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsBizAuthorisationEnabled").Return(true)
	ctx := featureflag.NewContextWithFeatureFlags(context.Background(), flagRepo)
	ctx = commonCtx.WithServiceID(ctx, "DIGIBANK")
	ctx = commonCtx.WithUserID(ctx, "test-id")

	mockCustomerMasterDBMYClient := &customerMasterDBMYMock.CustomerMaster{}
	mockCustomerMasterDBMYClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(
		&customerMasterDBMYApi.GetBusinessInfoResponse{
			CIF: cif,
			BusinessRelationships: []customerMasterDBMYApi.BusinessRelationship{{
				Bif:    &bif,
				Status: &status,
			}},
		}, nil)

	t.Run("happy-path", func(t *testing.T) {
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterDBMYClient: mockCustomerMasterDBMYClient, AccountServiceClient: mockAccountServiceClient}

		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       bif,
				},
			}, nil)

		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.Nil(t, err)
	})

	t.Run("customerID not match", func(t *testing.T) {
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterDBMYClient: mockCustomerMasterDBMYClient, AccountServiceClient: mockAccountServiceClient}

		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       "test-cif",
				},
			}, nil)

		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.NotNil(t, err)
	})

	t.Run("account permission is not allowed", func(t *testing.T) {
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterDBMYClient: mockCustomerMasterDBMYClient, AccountServiceClient: mockAccountServiceClient}

		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       cif,
				},
			}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil)

		_, err := CheckIfAuthorized(ctx, accountID, &txHistoryInterface)
		assert.NotNil(t, err)
	})
}

// TestCheckPermissionAndGetParent ...
func TestCheckPermissionAndGetParent(t *testing.T) {
	ctx := context.Background()

	t.Run("happy-path", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: CifNumber}, nil)

		// mocking account service call
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       CifNumber,
				},
			}, nil)

		parentID, err := CheckPermissionAndGetParent(ctx, accountID, &txHistoryInterface)
		assert.Nil(t, err)
		assert.Equal(t, parentID, parentAccountID)
	})

	t.Run("error-path-LookupCIFNumber-failed", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		cifLookUpError := servus.ServiceError{HTTPCode: 401, Code: "unauthorized", Message: "CIF_MAPPING_NOT_FOUND:cif number not found", Errors: []servus.ErrorDetail(nil)}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, cifLookUpError)

		_, err := CheckPermissionAndGetParent(ctx, accountID, &txHistoryInterface)
		assert.Error(t, cifLookUpError, err)
	})

	t.Run("error-path-forbidden", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: CifNumber}, nil)

		// mocking account service call
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceApi.GetAccountResponse{
				Account: &accountServiceApi.Account{
					ParentAccountID: parentAccountID,
					CifNumber:       "SG1234B",
				},
			}, nil)

		parentID, err := CheckPermissionAndGetParent(ctx, accountID, &txHistoryInterface)
		assert.Error(t, customError.BuildErrorResponse(customError.Unauthorized, "AccountPermission_FORBIDDEN"), err)
		assert.Equal(t, "", parentID)
	})

	t.Run("error-path-LookupCIFNumber-api-call-error", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		apiCallError := servus.ServiceError{HTTPCode: 500, Code: "internalServerError", Message: "internalServerError", Errors: []servus.ErrorDetail(nil)}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(nil, apiCallError)

		expectedError := customError.BuildErrorResponse(customError.InternalServerError, "Failed to connect customer-master")
		_, err := CheckPermissionAndGetParent(ctx, accountID, &txHistoryInterface)
		assert.Error(t, expectedError, err)
	})

	t.Run("error-path-CheckPermissionsForAccount-api-call-error", func(t *testing.T) {
		// mocking TxHistoryService interface
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		txHistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient}

		// mocking the Customer-Master Call
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMasterApi.LookupCIFNumberResponse{CifNumber: CifNumber}, nil)

		// mocking account service call
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(nil, errors.New("failed to connect accounts-service"))

		parentID, err := CheckPermissionAndGetParent(ctx, accountID, &txHistoryInterface)
		assert.Error(t, customError.BuildErrorResponse(customError.InternalServerError, "Failed to connect accounts-service"), err)
		assert.Equal(t, "", parentID)
	})
}
