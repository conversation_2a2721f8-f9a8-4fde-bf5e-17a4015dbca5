package handlers

import (
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.myteksi.net/dakota/common/tracing"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	pairingService "gitlab.myteksi.net/dakota/payment/pairing-service/api"
	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	externalLib "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/activeprofile"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

// TxHistoryService serves as the context for handlers.
type TxHistoryService struct {
	AppConfig                   *config.AppConfig                   `inject:"config"`
	CustomerMasterClient        customerMaster.CustomerMaster       `inject:"client.customerMaster"`
	CustomerMasterDBMYClient    customerMasterDBMY.CustomerMaster   `inject:"client.customerMasterDBMY"`
	AccountServiceClient        accountService.AccountService       `inject:"client.accountService"`
	PairingServiceClient        pairingService.PairingService       `inject:"client.pairingService"`
	PaymentExperienceClient     paymentExperience.PaymentExperience `inject:"client.paymentExperience"`
	Tracer                      tracing.Tracer                      `inject:"tracer"`
	Store                       storage.DatabaseStore               `inject:"dBStore"`
	FeatureFlags                featureflag.Repo                    `inject:"featureFlags"`
	Statsd                      statsd.Client                       `inject:"statsD"`
	ExternalActiveProfileClient externalLib.ExternalActiveProfile   `inject:"client.externalActiveProfileClient"`
}
