package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetSTM453TransactionInfo : API to fetch STM453 Transaction Info
func (t *TxHistoryService) GetSTM453TransactionInfo(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error) {
	slog.FromContext(ctx).Info(constants.GetSTM453TransactionInfoLogTag, fmt.Sprintf("Received Request: %+v", req))

	if validationError := handlerlogic.GetSTM453TransactionDetailRequestValidator(req); validationError != nil {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = append(err.Errors, *validationError)
		return nil, err
	}

	response, err := handlerlogic.GetSTM453TransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetSTM453TransactionInfoLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}
