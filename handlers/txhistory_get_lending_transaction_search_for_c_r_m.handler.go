package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/utils"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceDBMY "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// GetLendingTransactionSearchForCRM : API to fetch all transactions for customer
func (t *TxHistoryService) GetLendingTransactionSearchForCRM(ctx context.Context, req *api.GetLendingTransactionSearchForCRMRequest) (*api.GetLendingTransactionSearchResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	parsedRequest := &dto.TransactionHistorySearchRequest{
		AccountID:          req.AccountID,
		EndDate:            req.EndDate,
		StartDate:          req.StartDate,
		PageSize:           req.PageSize,
		StartingBefore:     req.StartingBefore,
		EndingAfter:        req.EndingAfter,
		TransactionType:    req.TransactionType,
		TransactionSubtype: req.TransactionSubtype,
		Status:             req.Status,
		ProductVariantCode: req.ProductVariantCode,
	}

	if errors := handlerlogic.GetLendingTransactionSearchValidator(parsedRequest); len(errors) != 0 {
		return nil, customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
	}

	// Check Authorization via CIF Number
	_, err := CheckIfAuthorized(ctx, req.AccountID, t)
	if err != nil {
		return nil, err
	}

	// Below logic is to fetch the product variant ID from account service DBMY client
	featureFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featureFlags != nil && featureFlags.IsBizAuthorisationEnabled() {
		slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, "biz authorisation is enabled")
		var account *accountServiceDBMY.Account
		account, err = utils.GetAccountFromAccountService(ctx, req.AccountID, t.AccountServiceClient)
		if err != nil {
			return nil, err
		}
		parsedRequest.ProductVariantCode = account.ProductVariantID
	}
	getLendingTransactionSearchObj := handlerlogic.GetLendingTransactionsSearchImpl{
		AccountServiceClient:            t.AccountServiceClient,
		TransactionHistoryServiceConfig: t.AppConfig.TransactionHistoryServiceConfig,
		FeatureFlags:                    t.AppConfig.FeatureFlags,
	}
	response, err := getLendingTransactionSearchObj.GetAllTransactionsFromDBForLending(ctx, parsedRequest)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, err
}
