package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/featureflag"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/dto"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetAccountTransactionsSearchForCX API to fetch all transactions for customer (currently CASA account only) for CX.
func (t *TxHistoryService) GetAccountTransactionsSearchForCX(ctx context.Context, req *api.GetTransactionsHistoryCXRequest) (*api.GetTransactionsHistoryCXResponse, error) {
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Received Request: %+v", req))

	parsedRequest := &dto.TransactionHistorySearchRequest{
		AccountID:          req.AccountID,
		EndDate:            req.EndDateTimeStamp,
		StartDate:          req.StartDateTimeStamp,
		PageSize:           req.PageSize,
		EndingAfter:        req.EndingAfter,
		StartingBefore:     req.StartingBefore,
		TransactionDomain:  req.TransactionDomain,
		TransactionType:    req.TransactionType,
		TransactionSubtype: req.TransactionSubtype,
	}
	if err := handlerlogic.GetCxTransactionListValidator(parsedRequest); err != nil {
		return nil, err
	}

	getAccountTransactionSearchObj := handlerlogic.GetAccountTransactionsSearchStruct{
		AccountServiceClient: t.AccountServiceClient,
	}
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)
	featureFlags := featureflag.FeatureFlagsFromContext(ctx)
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Feature flag: %+v", featureFlags))

	if featureFlags == nil || !featureFlags.IsExhaustiveCursorPaginationEnabledForCX() {
		response, err := getAccountTransactionSearchObj.GetAllCxTransactionsFromDB(ctx, parsedRequest)
		if err != nil {
			return nil, err
		}
		slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
		return handlerlogic.ParseTransactionHistoryForCXResponse(response), err
	}

	// Start to fetch transaction with exhaustive pagination
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, "Start to fetch transaction with exhaustive pagination")
	response, err := getAccountTransactionSearchObj.FetchTxnWithExhaustiveCursorPagination(ctx, parsedRequest)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return handlerlogic.ParseTransactionHistoryForCXResponse(response), nil
}
