package handlers //nolint:dupl

import (
	"context"
	"errors"
	"os"
	"testing"

	"gitlab.com/gx-regional/dbmy/transaction-history/test/utils"

	"gitlab.com/gx-regional/dbmy/transaction-history/internal/presenterhelper"

	"gitlab.com/gx-regional/dbmy/transaction-history/localise"

	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/transaction-history/storage"
	"gitlab.com/gx-regional/dbmy/transaction-history/test/resources"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/server/config"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	accountServiceApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetAllTransactionsForCXParameterMissingPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("accountID-missing", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			StartDateTimeStamp: "2021-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "`accountID` is a mandatory field",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`accountID` is a mandatory field."}},
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionsHistoryCXResponse)(nil), response)
	})

	t.Run("accountID-invalid", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "***********",
			StartDateTimeStamp: "2021-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "Invalid `accountID`",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "Invalid `accountID`"}},
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionsHistoryCXResponse)(nil), response)
	})
}

func TestGetAllTransactionsForCXParameterHappyPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	// Mocking Payment Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find",
		mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
	).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2022-10-01T00:00:00Z",
			EndDateTimeStamp:   "2022-10-12T15:00:00Z",
			PageSize:           2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsForCxFirstPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsForCxFirstPageResponse(true)
		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, handlerlogic.ParseTransactionHistoryForCXResponse(expectedResponse).Data, response.Data)
	})

	t.Run("happy-path-no-date-filters", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID: "*************",
			PageSize:  3,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CxTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResponse := responses.GetAllTransactionsOnlyPageCXResponse()

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse.Data, response.Data)
	})

	t.Run("happy-path-lastPage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2022-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           2, // Page Size > remaining entries
			StartingBefore:     "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			mock.Anything).Return(resources.GetAllTransactionsForCxLastPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsLastPageCXResponse()

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse.Data, response.Data)
	})

	t.Run("happy-path-onlyOnePage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2022-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           5, // Page Size > No. of Matching Entries
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			mock.Anything).Return(resources.CxTransactionsDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsOnlyOnePageCXResponse()

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse.Data, response.Data)
	})

	t.Run("happy-path-backwardScrolling", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2022-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           2,
			EndingAfter:        "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			mock.Anything).Return(resources.GetAllTransactionsBackwardScrollingForCxMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsBackwardScrollingPrevPageForCxResponse()
		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, handlerlogic.ParseTransactionHistoryForCXResponse(expectedResponse).Data, response.Data)
	})

	t.Run("happy-path-prev-next-page-exist", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2021-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           1,
			StartingBefore:     "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
		}
		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			mock.Anything).Return(resources.GetAllTransactionsBackwardScrollingForCxMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsPrevNextBothExistForCxResponse()
		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, handlerlogic.ParseTransactionHistoryForCXResponse(expectedResponse).Data, response.Data)
	})
}

func TestGetAllTransactionsForCXParameterErrorPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "SGD",
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	t.Run("db-fetch-error", func(t *testing.T) {
		request := &api.GetTransactionsHistoryCXRequest{
			AccountID:          "*************",
			StartDateTimeStamp: "2022-10-01T00:00:00Z",
			EndDateTimeStamp:   "2021-10-12T15:00:00Z",
			PageSize:           2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := txInterface.GetAccountTransactionsSearchForCX(context.Background(), request)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetTransactionsHistoryCXResponse)(nil), response)
	})
}
