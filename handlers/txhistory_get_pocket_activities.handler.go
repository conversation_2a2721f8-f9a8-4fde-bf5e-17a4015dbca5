package handlers //nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/transaction-history/api"
	"gitlab.com/gx-regional/dbmy/transaction-history/constants"
	"gitlab.com/gx-regional/dbmy/transaction-history/logic/handlerlogic"
	customErr "gitlab.com/gx-regional/dbmy/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetPocketActivities via POST
func (t *TxHistoryService) GetPocketActivities(ctx context.Context, req *api.GetPocketActivitiesRequest) (*api.GetPocketActivitiesResponse, error) {
	slog.FromContext(ctx).Info(constants.GetPocketActivitiesHandlerLogTag, fmt.Sprintf("Received Request: %+v", req))

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetPocketActivitiesRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Check Permission and Get Parent func call to check permission and get the parent account
	account, authErr := CheckPermissionAndGetParent(ctx, req.PocketID, t)
	if authErr != nil {
		return nil, authErr
	}

	//Check whether the pocket is valid
	if getPocketErr := handlerlogic.GetPocketValidator(account); getPocketErr != nil {
		return nil, getPocketErr
	}

	response, err := handlerlogic.GetPocketActivitiesFromDB(ctx, req, account)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetPocketActivitiesHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}
